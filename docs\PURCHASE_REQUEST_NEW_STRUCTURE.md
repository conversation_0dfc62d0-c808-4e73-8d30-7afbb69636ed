# Purchase Request - New Structure Implementation

## Overview

Implementasi baru untuk sistem Purchase Request dengan struktur database yang diperbarui dan navigasi drill-down yang intuitif.

## Database Structure Changes

### 1. Purchase Requests Table
```sql
CREATE TABLE purchase_requests (
    id BIGINT PRIMARY KEY,
    purchase_request_date DATE NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

**Perubahan dari struktur lama:**
- Menghapus `outlet_id` (dipindah ke details)
- Menghapus `request_date` → diganti `purchase_request_date`
- Menghapus `is_processed` (tidak diperlukan)

### 2. Purchase Request Details Table
```sql
CREATE TABLE purchase_request_details (
    id BIGINT PRIMARY KEY,
    purchase_request_id BIGINT FOREIGN KEY,
    outlet_id BIGINT FOREIGN KEY,
    product_id BIGINT FOREIGN KEY,
    purchase_quantity INTEGER NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

**<PERSON><PERSON>tur tetap sama** - menyimpan detail produk untuk setiap outlet dan purchase request.

## Model Updates

### PurchaseRequest Model
```php
class PurchaseRequest extends Model
{
    protected $fillable = ['purchase_request_date'];
    protected $casts = ['purchase_request_date' => 'date'];

    // Relationships
    public function details(): HasMany
    {
        return $this->hasMany(PurchaseRequestDetail::class);
    }

    // Helper Methods
    public function getTotalOutlets(): int
    public function getTotalProducts(): int
    public function getTotalQuantity(): int
    public function getOutlets()
    public function getOutletStats()
}
```

## Drill-Down Navigation Implementation

### 1. Level 1: Purchase Request Dates
**URL:** `/admin/purchase-requests`

**Tampilan:**
- Daftar tanggal purchase request
- Statistics: Total requests, outlets, products, quantity
- Action: Klik untuk melihat outlets

**Query:**
```php
PurchaseRequest::query()
    ->selectRaw('purchase_request_date, COUNT(*) as total_requests, MIN(id) as id')
    ->groupBy('purchase_request_date')
    ->orderBy('purchase_request_date', 'desc')
```

### 2. Level 2: Outlets for Selected Date
**State:** `currentView = 'outlets'`, `selectedDate = '2025-01-15'`

**Tampilan:**
- Daftar outlet untuk tanggal yang dipilih
- Statistics: Product count, total quantity per outlet
- Action: Klik untuk melihat products
- Back button: Kembali ke dates

**Query:**
```php
PurchaseRequestDetail::query()
    ->whereHas('purchaseRequest', function ($query) {
        $query->where('purchase_request_date', $this->selectedDate);
    })
    ->with(['outlet'])
    ->selectRaw('outlet_id, COUNT(*) as product_count, SUM(purchase_quantity) as total_quantity, MIN(id) as id')
    ->groupBy('outlet_id')
```

### 3. Level 3: Products for Selected Date & Outlet
**State:** `currentView = 'products'`, `selectedDate = '2025-01-15'`, `selectedOutlet = 1`

**Tampilan:**
- Daftar produk untuk outlet dan tanggal yang dipilih
- Detail: Product name, barcode, unit, pack quantity, purchase quantity
- Calculated: Total value (quantity × pack quantity)
- Back button: Kembali ke outlets

**Query:**
```php
PurchaseRequestDetail::query()
    ->whereHas('purchaseRequest', function ($query) {
        $query->where('purchase_request_date', $this->selectedDate);
    })
    ->where('outlet_id', $this->selectedOutlet)
    ->with(['product', 'purchaseRequest'])
```

## State Management

### Component State
```php
public ?string $selectedDate = null;
public ?string $selectedOutlet = null;
public string $currentView = 'dates'; // dates, outlets, products
```

### Navigation Logic
```php
// Forward navigation (drill-down)
Action::make('view_outlets')
    ->action(function () use ($record) {
        $this->selectedDate = $record->purchase_request_date;
        $this->currentView = 'outlets';
        $this->dispatch('$refresh');
    })

// Backward navigation
Action::make('back')
    ->action(function () {
        if ($this->currentView === 'products') {
            $this->currentView = 'outlets';
            $this->selectedOutlet = null;
        } elseif ($this->currentView === 'outlets') {
            $this->currentView = 'dates';
            $this->selectedDate = null;
        }
        $this->dispatch('$refresh');
    })
```

## Form Updates

### Multi-Step Form Schema
```php
// Step 1: Purchase Date
DatePicker::make('purchase_request_date')
    ->label('Purchase Request Date')
    ->required()
    ->default(now())
    ->maxDate(now()->addDays(30))

// Step 2: Outlet Selection (unchanged)
// Step 3: Product Selection (unchanged)
```

### Record Creation Logic
```php
protected function handleRecordCreation(array $data): Model
{
    $purchaseDetails = $data['purchase_details'] ?? [];
    $outletId = $data['outlet_id'] ?? null;
    unset($data['purchase_details'], $data['outlet_id']);

    // Create purchase request (only date)
    $record = static::getModel()::create($data);

    // Create details with outlet_id
    foreach ($purchaseDetails as $detail) {
        PurchaseRequestDetail::create([
            'purchase_request_id' => $record->id,
            'outlet_id' => $outletId,
            'product_id' => $detail['product_id'],
            'purchase_quantity' => $detail['purchase_quantity'],
        ]);
    }

    return $record;
}
```

## User Experience Features

### 1. Dynamic Titles & Descriptions
```php
public function getTitle(): string
{
    return match ($this->currentView) {
        'dates' => 'Purchase Requests',
        'outlets' => 'Outlets - ' . Carbon::parse($this->selectedDate)->format('M j, Y'),
        'products' => 'Products - ' . Outlet::find($this->selectedOutlet)?->name,
    };
}

public function getSubheading(): ?string
{
    return match ($this->currentView) {
        'dates' => 'Select a date to view outlets with purchase requests',
        'outlets' => 'Select an outlet to view products in purchase requests',
        'products' => 'Purchase request details for the selected outlet and date',
    };
}
```

### 2. Contextual Actions
- **Dates Level**: "View Outlets" action
- **Outlets Level**: "View Products" action + "Back" button
- **Products Level**: "Back" button

### 3. Visual Indicators
- **Badges**: Different colors for different metrics
- **Icons**: Contextual icons for each level
- **Descriptions**: Helper text for each column

## Benefits

### 1. Better Data Organization
- **Hierarchical Structure**: Natural drill-down flow
- **Flexible Relationships**: Multiple outlets per date
- **Scalable Design**: Easy to add more levels

### 2. Improved User Experience
- **Progressive Disclosure**: Show relevant information at each level
- **Intuitive Navigation**: Natural back/forward flow
- **Contextual Information**: Dynamic titles and descriptions

### 3. Performance Optimization
- **Efficient Queries**: Aggregated data at each level
- **Lazy Loading**: Only load data when needed
- **Minimal State**: Simple state management

## Testing

### Sample Data Creation
```php
// Create purchase requests
$pr1 = PurchaseRequest::create(['purchase_request_date' => '2025-01-15']);
$pr2 = PurchaseRequest::create(['purchase_request_date' => '2025-01-16']);

// Create details
PurchaseRequestDetail::create([
    'purchase_request_id' => $pr1->id,
    'outlet_id' => 1,
    'product_id' => 1,
    'purchase_quantity' => 10
]);
```

### Navigation Testing
1. **Level 1**: Verify dates display with correct statistics
2. **Level 2**: Click date → verify outlets display for that date
3. **Level 3**: Click outlet → verify products display for that outlet/date
4. **Back Navigation**: Verify back button works correctly
5. **State Management**: Verify state resets properly

## Migration Guide

### From Old Structure
1. **Backup existing data**
2. **Run migration**: `php artisan migrate:fresh --seed`
3. **Update forms**: Change field references from `request_date` to `purchase_request_date`
4. **Update queries**: Remove outlet_id filters from main table
5. **Test navigation**: Verify drill-down functionality

### Deployment Checklist
- [ ] Database migration completed
- [ ] Sample data created
- [ ] Form functionality tested
- [ ] Navigation flow tested
- [ ] Performance verified
- [ ] User permissions checked

## Future Enhancements

### Short Term
1. **Export Functionality**: Export data at any level
2. **Advanced Filtering**: Date ranges, multi-select
3. **Bulk Operations**: Batch actions

### Long Term
1. **Real-time Updates**: Live data refresh
2. **Analytics Dashboard**: Trends and insights
3. **Mobile Optimization**: Responsive design
4. **API Integration**: External system connectivity

## Conclusion

Implementasi baru memberikan:
- **Better Structure**: More flexible and scalable database design
- **Intuitive Navigation**: Natural drill-down user experience
- **Performance**: Optimized queries and state management
- **Maintainability**: Clean code structure and separation of concerns

Sistem ini ready untuk production use dan dapat diakses melalui `/admin/purchase-requests`.
