<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'view_outlets',
            'create_outlets',
            'edit_outlets',
            'delete_outlets',
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'view_report_stocks',
            'create_report_stocks',
            'edit_report_stocks',
            'delete_report_stocks',
            'import_report_stocks',
            'view_purchase_requests',
            'create_purchase_requests',
            'edit_purchase_requests',
            'delete_purchase_requests',
            'generate_purchase_requests',
            'export_reports',
            'view_all_outlets',
            'manage_users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);

        // Assign permissions to admin role (outlet-specific access)
        $adminPermissions = [
            'view_products',
            'create_products',
            'edit_products',
            'view_report_stocks',
            'create_report_stocks',
            'edit_report_stocks',
            'import_report_stocks',
            'export_reports',
        ];

        $adminRole->syncPermissions($adminPermissions);

        // Assign permissions to manager role (full access)
        $managerPermissions = [
            'view_outlets',
            'create_outlets',
            'edit_outlets',
            'delete_outlets',
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'view_report_stocks',
            'create_report_stocks',
            'edit_report_stocks',
            'delete_report_stocks',
            'import_report_stocks',
            'view_purchase_requests',
            'create_purchase_requests',
            'edit_purchase_requests',
            'delete_purchase_requests',
            'generate_purchase_requests',
            'export_reports',
            'view_all_outlets',
            'manage_users',
        ];

        $managerRole->syncPermissions($managerPermissions);
    }
}
