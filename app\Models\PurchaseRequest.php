<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PurchaseRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_request_date',
    ];

    protected $casts = [
        'purchase_request_date' => 'date',
    ];

    public function details(): HasMany
    {
        return $this->hasMany(PurchaseRequestDetail::class);
    }

    // Scopes
    public function scopeForDate($query, $date)
    {
        return $query->where('purchase_request_date', $date);
    }

    // Helper methods
    public function getDisplayName(): string
    {
        return "Purchase Request - {$this->purchase_request_date->format('Y-m-d')}";
    }

    public function getTotalOutlets(): int
    {
        return $this->details()->distinct('outlet_id')->count('outlet_id');
    }

    public function getTotalProducts(): int
    {
        return $this->details()->count();
    }

    public function getTotalQuantity(): int
    {
        return $this->details()->sum('purchase_quantity');
    }

    // Get unique outlets for this purchase request date
    public function getOutlets()
    {
        return \App\Models\Outlet::whereIn('id',
            $this->details()->distinct('outlet_id')->pluck('outlet_id')
        )->get();
    }

    // Get statistics for this purchase request date
    public function getOutletStats()
    {
        return $this->details()
            ->selectRaw('outlet_id, COUNT(*) as product_count, SUM(purchase_quantity) as total_quantity')
            ->with('outlet')
            ->groupBy('outlet_id')
            ->get();
    }
}
