<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ReportStockBulkImport;
use App\Models\Product;
use App\Models\Outlet;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Illuminate\Support\Facades\DB;

$filePath = $argv[1] ?? 'test_import_small.xlsx';
$reportDate = $argv[2] ?? '2025-09-07';

if (!file_exists($filePath)) {
    echo "File not found: {$filePath}\n";
    exit(1);
}

echo "\n=== Report Stock Bulk Import Test (Small) ===\n";
echo "File: {$filePath}\n";
echo "Date: {$reportDate}\n";
echo "File size: " . round(filesize($filePath) / 1024, 2) . " KB\n";

// Test database connection
try {
    DB::connection()->getPdo();
    echo "Database: Connected\n";
} catch (\Exception $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Get initial counts
$initialCounts = [
    'products' => Product::count(),
    'outlets' => Outlet::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stocks' => ReportStock::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

echo "\nInitial counts:\n";
foreach ($initialCounts as $table => $count) {
    echo "  {$table}: {$count}\n";
}

echo "\nStarting import...\n";

$startTime = microtime(true);
$startMemory = memory_get_usage();

try {
    $import = new ReportStockBulkImport($reportDate);
    Excel::import($import, $filePath);
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage();
    $peakMemory = memory_get_peak_usage();
    
    echo "Import completed successfully!\n";
    
    // Get final counts
    $finalCounts = [
        'products' => Product::count(),
        'outlets' => Outlet::count(),
        'outlet_products' => OutletProduct::count(),
        'report_stocks' => ReportStock::count(),
        'report_stock_details' => ReportStockDetail::count(),
    ];
    
    echo "\n=== Import Statistics ===\n";
    echo "Processed rows: " . $import->getProcessedRows() . "\n";
    echo "Products created: " . $import->getProductsCreated() . "\n";
    echo "Outlet products created: " . $import->getOutletProductsCreated() . "\n";
    echo "Outlet products updated: " . $import->getOutletProductsUpdated() . "\n";
    echo "Report stocks created: " . $import->getReportStocksCreated() . "\n";
    echo "Report stocks updated: " . $import->getReportStocksUpdated() . "\n";
    echo "Errors: " . count($import->getErrors()) . "\n";
    
    if (!empty($import->getErrors())) {
        echo "\nErrors:\n";
        foreach ($import->getErrors() as $error) {
            echo "  - {$error}\n";
        }
    }
    
    echo "\n=== Performance Metrics ===\n";
    $duration = $endTime - $startTime;
    echo "Duration: " . round($duration, 2) . " seconds\n";
    echo "Memory used: " . round(($endMemory - $startMemory) / 1024 / 1024, 2) . " MB\n";
    echo "Peak memory: " . round($peakMemory / 1024 / 1024, 2) . " MB\n";
    echo "Processing speed: " . round($import->getProcessedRows() / $duration, 2) . " rows/second\n";
    
    echo "\n=== Database Changes ===\n";
    foreach ($initialCounts as $table => $initial) {
        $final = $finalCounts[$table];
        $change = $final - $initial;
        echo "  {$table}: {$initial} -> {$final} ({$change})\n";
    }
    
    // Check report stock details for the specific date
    $reportStock = ReportStock::where('report_date', $reportDate)->first();
    if ($reportStock) {
        $detailsCount = ReportStockDetail::where('report_stock_id', $reportStock->id)->count();
        echo "\nReport stock details for {$reportDate}: {$detailsCount}\n";
    }
    
    echo "\n=== Test PASSED ===\n";
    
} catch (\Exception $e) {
    echo "Import FAILED: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    if (method_exists($e, 'getTrace')) {
        echo "Trace:\n";
        foreach ($e->getTrace() as $i => $trace) {
            $file = $trace['file'] ?? 'unknown';
            $line = $trace['line'] ?? 'unknown';
            $function = $trace['function'] ?? 'unknown';
            echo "#{$i} {$file}({$line}): {$function}\n";
        }
    }
    
    echo "\n=== Test FAILED ===\n";
    exit(1);
}
