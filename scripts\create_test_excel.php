<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Models\Outlet;

$outputPath = $argv[1] ?? 'test_import_data.xlsx';
$rowCount = (int) ($argv[2] ?? 100);

echo "Creating test Excel file with {$rowCount} rows...\n";

// Get some outlets from database
$outlets = Outlet::limit(5)->get();
if ($outlets->isEmpty()) {
    echo "No outlets found in database. Please seed outlets first.\n";
    exit(1);
}

$spreadsheet = new Spreadsheet();
$worksheet = $spreadsheet->getActiveSheet();

// Set headers (based on the real Excel structure)
$headers = [
    'A' => 'GAB',
    'B' => 'OUTLET',
    'C' => 'KODE',
    'D' => 'NAMA PRODUK',
    'E' => 'PRT',
    'F' => 'BARCODE',
    'G' => 'PACK',
    'H' => 'QTY',
    'I' => 'SAT',
    'J' => 'SALDO'
];

// Write headers
foreach ($headers as $col => $header) {
    $worksheet->setCellValue($col . '1', $header);
}

// Sample product data
$products = [
    ['name' => 'PARACETAMOL 500MG TAB', 'barcode' => '8999999000001', 'unit' => 'TAB'],
    ['name' => 'AMOXICILLIN 500MG CAP', 'barcode' => '8999999000002', 'unit' => 'CAP'],
    ['name' => 'VITAMIN C 100MG TAB', 'barcode' => '8999999000003', 'unit' => 'TAB'],
    ['name' => 'ASPIRIN 100MG TAB', 'barcode' => '8999999000004', 'unit' => 'TAB'],
    ['name' => 'IBUPROFEN 400MG TAB', 'barcode' => '8999999000005', 'unit' => 'TAB'],
    ['name' => 'CETIRIZINE 10MG TAB', 'barcode' => '8999999000006', 'unit' => 'TAB'],
    ['name' => 'OMEPRAZOLE 20MG CAP', 'barcode' => '8999999000007', 'unit' => 'CAP'],
    ['name' => 'METFORMIN 500MG TAB', 'barcode' => '8999999000008', 'unit' => 'TAB'],
    ['name' => 'SIMVASTATIN 20MG TAB', 'barcode' => '8999999000009', 'unit' => 'TAB'],
    ['name' => 'LOSARTAN 50MG TAB', 'barcode' => '8999999000010', 'unit' => 'TAB'],
];

$paretos = ['SM', 'FM', 'BB'];

// Generate test data
for ($row = 2; $row <= $rowCount + 1; $row++) {
    $outlet = $outlets->random();
    $product = $products[array_rand($products)];
    $pareto = $paretos[array_rand($paretos)];
    $pack = rand(1, 10);
    $qty = rand(0, 100);

    $worksheet->setCellValue('A' . $row, $outlet->code . $product['barcode']); // GAB
    $worksheet->setCellValue('B' . $row, $outlet->code); // OUTLET
    $worksheet->setCellValue('C' . $row, $product['barcode']); // KODE
    $worksheet->setCellValue('D' . $row, $product['name']); // NAMA PRODUK
    $worksheet->setCellValue('E' . $row, $pareto); // PRT
    $worksheet->setCellValue('F' . $row, $product['barcode']); // BARCODE
    $worksheet->setCellValue('G' . $row, $pack . ',00'); // PACK
    $worksheet->setCellValue('H' . $row, $qty); // QTY
    $worksheet->setCellValue('I' . $row, $product['unit']); // SAT
    $worksheet->setCellValue('J' . $row, rand(1000, 50000)); // SALDO
}

// Save the file
$writer = new Xlsx($spreadsheet);
$writer->save($outputPath);

echo "Test Excel file created: {$outputPath}\n";
echo "Rows: {$rowCount}\n";
echo "Outlets used: " . $outlets->pluck('code')->implode(', ') . "\n";
echo "Products: " . count($products) . "\n";
