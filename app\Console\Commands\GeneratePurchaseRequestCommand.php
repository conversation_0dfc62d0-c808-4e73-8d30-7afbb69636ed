<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ReportStock;
use App\Services\ReportStockPurchaseService;
use Carbon\Carbon;

class GeneratePurchaseRequestCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'purchase:generate 
                            {--date= : Report date (Y-m-d format)}
                            {--threshold=10 : Low stock threshold}
                            {--multiplier=2 : Purchase multiplier}
                            {--force : Force regenerate existing requests}';

    /**
     * The console command description.
     */
    protected $description = 'Generate purchase requests from report stock data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Purchase Request Generation...');
        
        // Get parameters
        $date = $this->option('date') ? Carbon::parse($this->option('date')) : now();
        $threshold = (int) $this->option('threshold');
        $multiplier = (int) $this->option('multiplier');
        $force = $this->option('force');
        
        $this->info("📅 Report Date: {$date->format('Y-m-d')}");
        $this->info("⚠️ Low Stock Threshold: {$threshold}");
        $this->info("📈 Purchase Multiplier: {$multiplier}");
        $this->info("🔄 Force Regenerate: " . ($force ? 'Yes' : 'No'));
        
        // Find report stock for the date
        $reportStock = ReportStock::where('report_date', $date->toDateString())->first();
        
        if (!$reportStock) {
            $this->error("❌ No report stock found for date: {$date->format('Y-m-d')}");
            return 1;
        }
        
        $this->info("✅ Found report stock (ID: {$reportStock->id})");
        
        // Check if already generated
        if ($reportStock->is_generated && !$force) {
            $this->warn("⚠️ Purchase requests already generated for this date. Use --force to regenerate.");
            return 0;
        }
        
        // Generate purchase requests
        $service = new ReportStockPurchaseService();
        
        $options = [
            'low_stock_threshold' => $threshold,
            'purchase_multiplier' => $multiplier,
            'force_regenerate' => $force
        ];
        
        $this->info("🔄 Generating purchase requests...");
        
        $results = $service->generatePurchaseRequest($reportStock, $options);
        
        // Display results
        if ($results['success']) {
            $this->info("🎉 Purchase Request Generation Completed Successfully!");
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Outlets', $results['total_outlets']],
                    ['Purchase Requests Created', count($results['purchase_requests'])],
                    ['Total Products', $results['total_products']],
                ]
            );
            
            // Show details for each purchase request
            if (count($results['purchase_requests']) > 0) {
                $this->info("\n📋 Purchase Request Details:");
                foreach ($results['purchase_requests'] as $pr) {
                    $this->line("  • {$pr->outlet->name}: {$pr->getTotalProducts()} products, {$pr->getTotalQuantity()} total quantity");
                }
            }
            
        } else {
            $this->error("❌ Purchase Request Generation Failed!");
            foreach ($results['errors'] as $error) {
                $this->error("  • {$error}");
            }
            return 1;
        }
        
        // Show low stock summary
        if ($this->confirm('Would you like to see the low stock summary?', true)) {
            $this->showLowStockSummary($reportStock, $options);
        }
        
        return 0;
    }
    
    /**
     * Show low stock summary
     */
    protected function showLowStockSummary(ReportStock $reportStock, array $options)
    {
        $service = new ReportStockPurchaseService();
        $summary = $service->getLowStockSummary($reportStock, $options);
        
        $this->info("\n📊 Low Stock Summary:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Low Stock Items', $summary['total_low_stock_items']],
                ['Outlets Affected', $summary['outlets_affected']],
                ['Products Affected', $summary['products_affected']],
            ]
        );
        
        if (count($summary['items_by_outlet']) > 0) {
            $this->info("\n🏪 Breakdown by Outlet:");
            
            foreach ($summary['items_by_outlet'] as $outletData) {
                $this->line("\n📍 {$outletData['outlet']->name}:");
                $this->line("   Low Stock: {$outletData['low_stock_count']} items");
                $this->line("   Out of Stock: {$outletData['out_of_stock_count']} items");
                
                if ($this->option('verbose')) {
                    $tableData = [];
                    foreach ($outletData['items'] as $item) {
                        $status = $item['current_quantity'] == 0 ? 'OUT' : 
                                 ($item['current_quantity'] <= 5 ? 'CRITICAL' : 'LOW');
                        
                        $tableData[] = [
                            $item['product']->name,
                            $item['current_quantity'],
                            $item['suggested_purchase'],
                            $status
                        ];
                    }
                    
                    $this->table(
                        ['Product', 'Current', 'Suggested', 'Status'],
                        $tableData
                    );
                }
            }
        }
    }
}
