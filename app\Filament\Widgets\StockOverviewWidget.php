<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Models\OutletProduct;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class StockOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();

        // Get outlet-specific data based on user role
        if ($user->hasRole('admin') && $user->outlet_id) {
            $outletId = $user->outlet_id;

            // Count products available in this outlet
            $totalProducts = OutletProduct::where('outlet_id', $outletId)->count();

            // Count stock reports for this outlet (count distinct report dates)
            $totalReports = ReportStockDetail::where('outlet_id', $outletId)
                ->join('report_stocks', 'report_stock_details.report_stock_id', '=', 'report_stocks.id')
                ->distinct('report_stocks.report_date')
                ->count('report_stocks.report_date');

            // Get latest stock report details for analysis
            $latestReports = ReportStockDetail::where('outlet_id', $outletId)
                ->whereHas('reportStock', function ($query) {
                    $query->whereDate('report_date', '>=', now()->subDays(30));
                })
                ->with(['product', 'reportStock'])
                ->get();

        } else {
            // Manager can see all outlets
            $totalProducts = Product::count();
            $totalReports = ReportStock::count();

            // Get latest stock report details for analysis
            $latestReports = ReportStockDetail::whereHas('reportStock', function ($query) {
                    $query->whereDate('report_date', '>=', now()->subDays(30));
                })
                ->with(['product', 'reportStock'])
                ->get();
        }

        // Calculate low stock and out of stock
        $lowStockCount = 0;
        $outOfStockCount = 0;

        foreach ($latestReports as $reportDetail) {
            if (!$reportDetail->product) continue;

            // Get outlet product for buffer information
            $outletProduct = OutletProduct::where('outlet_id', $reportDetail->outlet_id)
                ->where('product_id', $reportDetail->product_id)
                ->first();

            if ($reportDetail->quantity <= 0) {
                $outOfStockCount++;
            } elseif ($outletProduct && $reportDetail->quantity < $outletProduct->min_buffer) {
                $lowStockCount++;
            }
        }

        return [
            Stat::make('Total Products', number_format($totalProducts))
                ->description('Products in system')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary'),

            Stat::make('Stock Reports', number_format($totalReports))
                ->description('Total stock reports')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info'),

            Stat::make('Low Stock Items', number_format($lowStockCount))
                ->description('Below minimum buffer')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning'),

            Stat::make('Out of Stock', number_format($outOfStockCount))
                ->description('Zero quantity items')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
