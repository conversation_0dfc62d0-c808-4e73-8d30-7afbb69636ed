<?php

namespace App\Filament\Resources\ImportHistories\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ImportHistoriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('filename')
                    ->label('File Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('user.name')
                    ->label('Imported By')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('reportStock.outlet.name')
                    ->label('Outlet')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('reportStock.report_date')
                    ->label('Report Date')
                    ->date()
                    ->sortable(),
                TextColumn::make('file_size')
                    ->label('File Size')
                    ->sortable(),
                TextColumn::make('total_rows')
                    ->label('Total Rows')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('processed_rows')
                    ->label('Processed')
                    ->numeric()
                    ->sortable()
                    ->color('success'),
                TextColumn::make('error_rows')
                    ->label('Errors')
                    ->numeric()
                    ->sortable()
                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),
                TextColumn::make('success_rate')
                    ->label('Success Rate')
                    ->suffix('%')
                    ->sortable()
                    ->color(fn ($state) => $state >= 90 ? 'success' : ($state >= 70 ? 'warning' : 'danger')),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'processing' => 'warning',
                        'completed' => 'success',
                        'failed' => 'danger',
                    }),
                TextColumn::make('duration')
                    ->label('Duration')
                    ->sortable(),
                TextColumn::make('started_at')
                    ->label('Started At')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
