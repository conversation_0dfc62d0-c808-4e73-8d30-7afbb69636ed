<?php

namespace App\Filament\Resources\OutletResource\Pages;

use App\Filament\Resources\OutletResource\OutletResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Notifications\Notification;

class EditOutlet extends EditRecord
{
    protected static string $resource = OutletResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make()
                ->label('View')
                ->icon('heroicon-m-eye')
                ->color('info'),

            DeleteAction::make()
                ->label('Delete')
                ->icon('heroicon-m-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Delete Outlet')
                ->modalDescription(fn () => "Are you sure you want to delete the outlet '{$this->record->name}'? This action cannot be undone.")
                ->modalSubmitActionLabel('Yes, delete it')
                ->before(function () {
                    // Check if outlet has users or products
                    if ($this->record->users()->count() > 0) {
                        throw new \Exception("Cannot delete outlet '{$this->record->name}' because it has {$this->record->users()->count()} user(s) assigned to it.");
                    }
                    if ($this->record->products()->count() > 0) {
                        throw new \Exception("Cannot delete outlet '{$this->record->name}' because it has {$this->record->products()->count()} product(s) associated with it.");
                    }
                }),
        ];
    }

    public function getTitle(): string
    {
        return "Edit {$this->record->name}";
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Outlet updated successfully')
            ->body('The outlet information has been updated.')
            ->duration(5000);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure code is uppercase
        $data['code'] = strtoupper($data['code']);
        
        return $data;
    }

    protected function afterSave(): void
    {
        // Log the update (if activity log is needed in the future)
        // activity()
        //     ->performedOn($this->record)
        //     ->causedBy(auth()->user())
        //     ->log('Outlet updated');
    }
}
