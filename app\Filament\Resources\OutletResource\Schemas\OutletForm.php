<?php

namespace App\Filament\Resources\OutletResource\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class OutletForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Outlet Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter outlet name')
                    ->helperText('The display name for this outlet (e.g., "Apotek Keluarga Pusat")')
                    ->columnSpanFull(),

                TextInput::make('code')
                    ->label('Outlet Code')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter outlet code')
                    ->helperText('Unique identifier for this outlet (e.g., "AKP001")')
                    ->unique(ignoreRecord: true)
                    ->rules([
                        function () {
                            return function (string $attribute, $value, \Closure $fail) {
                                if (!preg_match('/^[A-Z0-9]+$/', $value)) {
                                    $fail('The outlet code must contain only uppercase letters and numbers.');
                                }
                            };
                        },
                    ])
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        // Auto-uppercase the code
                        $set('code', strtoupper($state));
                    })
                    ->columnSpanFull(),
            ]);
    }
}
