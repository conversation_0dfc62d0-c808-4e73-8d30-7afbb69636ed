<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Product;

// Update products with proper buffer values
$products = Product::all();

foreach ($products as $product) {
    $product->update([
        'min_buffer' => rand(20, 50),
        'max_buffer' => rand(100, 200),
    ]);
    echo 'Updated product: ' . $product->name . ' - Min: ' . $product->min_buffer . ', Max: ' . $product->max_buffer . PHP_EOL;
}

echo 'Updated all product buffers' . PHP_EOL;
