<?php

namespace App\Exports;

use App\Models\PurchaseRequestDetail;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class PurchaseRequestLinesExport implements FromQuery, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithMapping
{
    protected $outletId;

    public function __construct($outletId = null)
    {
        $this->outletId = $outletId;
    }

    public function query()
    {
        $query = PurchaseRequestDetail::query()
            ->with(['product', 'outlet', 'purchaseRequest']);

        if ($this->outletId) {
            $query->where('outlet_id', $this->outletId);
        }

        return $query->orderBy('created_at', 'desc');
    }

    public function map($purchaseRequest): array
    {
        // Get outlet product configuration for pareto and buffer data
        $outletProduct = $purchaseRequest->product->outletProducts()
            ->where('outlet_id', $purchaseRequest->outlet_id)
            ->first();

        return [
            $purchaseRequest->outlet->name ?? 'Unknown',
            $purchaseRequest->request_date->format('Y-m-d'),
            $purchaseRequest->product->name,
            $purchaseRequest->product->barcode,
            $purchaseRequest->product->pack_quantity,
            $purchaseRequest->purchase_quantity,
            $purchaseRequest->product->unit,
            $outletProduct->outlet_pareto ?? 'N/A',
            $outletProduct->rumus_pareto ?? 'N/A',
            $outletProduct->min_buffer ?? 0,
            $outletProduct->max_buffer ?? 0,
            $purchaseRequest->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function headings(): array
    {
        return [
            'Outlet',
            'Request Date',
            'Product Name',
            'Barcode',
            'Pack Quantity',
            'Purchase Quantity',
            'Unit',
            'Outlet Pareto',
            'Rumus Pareto',
            'Min Buffer',
            'Max Buffer',
            'Created At',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F59E0B'], // Amber color
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25, // outlet
            'B' => 15, // request_date
            'C' => 35, // product_name
            'D' => 20, // barcode
            'E' => 15, // pack_quantity
            'F' => 18, // purchase_quantity
            'G' => 15, // unit
            'H' => 15, // outlet_pareto
            'I' => 15, // rumus_pareto
            'J' => 12, // min_buffer
            'K' => 12, // max_buffer
            'L' => 20, // created_at
        ];
    }

    public function title(): string
    {
        if ($this->outletId) {
            $outlet = \App\Models\Outlet::find($this->outletId);
            return 'Purchase Requests - ' . ($outlet ? $outlet->name : 'Unknown Outlet') . ' - ' . now()->format('Y-m-d');
        }
        return 'Purchase Requests - All Outlets - ' . now()->format('Y-m-d');
    }
}
