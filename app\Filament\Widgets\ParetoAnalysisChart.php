<?php

namespace App\Filament\Widgets;

use App\Models\OutletProduct;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class ParetoAnalysisChart extends ChartWidget
{
    protected static ?int $sort = 5;

    public function getHeading(): ?string
    {
        return 'Product Pareto Analysis';
    }

    protected function getData(): array
    {
        $user = Auth::user();

        // Get outlet products based on user role
        $query = OutletProduct::query();
        if ($user->hasRole('admin') && $user->outlet_id) {
            $query->where('outlet_id', $user->outlet_id);
        }

        // Count products by pareto category
        $fastMoving = (clone $query)->where('outlet_pareto', 'FM')->count();
        $slowMoving = (clone $query)->where('outlet_pareto', 'SM')->count();
        $badMoving = (clone $query)->where('outlet_pareto', 'BM')->count();
        $unclassified = (clone $query)->whereNull('outlet_pareto')->count();

        return [
            'datasets' => [
                [
                    'label' => 'Products by Pareto Category',
                    'data' => [$fastMoving, $slowMoving, $badMoving, $unclassified],
                    'backgroundColor' => [
                        '#10b981', // Green for Fast Moving
                        '#f59e0b', // Amber for Slow Moving
                        '#ef4444', // Red for Bad Moving
                        '#6b7280', // Gray for Unclassified
                    ],
                    'borderColor' => [
                        '#059669',
                        '#d97706',
                        '#dc2626',
                        '#4b5563',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => ['Fast Moving (FM)', 'Slow Moving (SM)', 'Bad Moving (BM)', 'Unclassified'],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'maintainAspectRatio' => false,
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
