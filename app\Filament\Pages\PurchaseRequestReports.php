<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Support\Icons\Heroicon;
use BackedEnum;
use App\Models\PurchaseRequest;
use App\Models\Outlet;
use App\Models\PurchaseRequestDetail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PurchaseRequestReports extends Page
{
    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedChartBar;

    protected static ?string $navigationLabel = 'Purchase Request Reports';

    protected static ?string $title = 'Purchase Request Reports';

    public ?string $view_type = null;
    public ?string $date = null;
    public ?string $outlet_id = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->view_type = request()->get('view', 'dates');
        $this->date = request()->get('date');
        $this->outlet_id = request()->get('outlet');

        $this->loadReportData();
    }

    public function getTitle(): string
    {
        return match ($this->view_type) {
            'outlets' => 'Outlets - ' . \Carbon\Carbon::parse($this->date)->format('M j, Y'),
            'products' => 'Products - ' . Outlet::find($this->outlet_id)?->name . ' (' . \Carbon\Carbon::parse($this->date)->format('M j, Y') . ')',
            default => 'Purchase Request Reports',
        };
    }

    public function getHeading(): string
    {
        return match ($this->view_type) {
            'outlets' => 'Outlets with Purchase Requests on ' . \Carbon\Carbon::parse($this->date)->format('M j, Y'),
            'products' => 'Products Requested by ' . Outlet::find($this->outlet_id)?->name . ' on ' . \Carbon\Carbon::parse($this->date)->format('M j, Y'),
            default => 'Purchase Request Reports by Date',
        };
    }

    protected function loadReportData(): void
    {
        try {
            $this->reportData = match ($this->view_type) {
                'outlets' => $this->getOutletsData(),
                'products' => $this->getProductsData(),
                default => $this->getDatesData(),
            };
        } catch (\Exception $e) {
            // Fallback to simple data if complex query fails
            $this->reportData = $this->getSimpleDatesData();
        }
    }

    protected function getDatesData(): array
    {
        $query = PurchaseRequest::query()
            ->select('purchase_request_date')
            ->selectRaw('COUNT(*) as total_requests')
            ->selectRaw('(SELECT COUNT(DISTINCT outlet_id) FROM purchase_request_details WHERE purchase_request_details.purchase_request_id = purchase_requests.id) as total_outlets')
            ->selectRaw('(SELECT COUNT(*) FROM purchase_request_details WHERE purchase_request_details.purchase_request_id = purchase_requests.id) as total_products')
            ->groupBy('purchase_request_date')
            ->orderBy('purchase_request_date', 'desc');

        $user = Auth::user();
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->whereHas('details', function ($q) use ($user) {
                $q->where('outlet_id', $user->outlet_id);
            });
        }

        return $query->get()->toArray();
    }

    protected function getSimpleDatesData(): array
    {
        $query = PurchaseRequest::query()
            ->select('purchase_request_date')
            ->selectRaw('COUNT(*) as total_requests')
            ->selectRaw('0 as total_outlets') // Simplified for now
            ->selectRaw('0 as total_products') // Simplified for now
            ->groupBy('purchase_request_date')
            ->orderBy('purchase_request_date', 'desc');

        $user = Auth::user();
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->whereHas('details', function ($q) use ($user) {
                $q->where('outlet_id', $user->outlet_id);
            });
        }

        return $query->get()->toArray();
    }

    protected function getOutletsData(): array
    {
        if (!$this->date) {
            return [];
        }

        $query = Outlet::query()
            ->whereHas('purchaseRequestDetails', function ($query) {
                $query->whereHas('purchaseRequest', function ($q) {
                    $q->where('purchase_request_date', $this->date);
                });
            })
            ->addSelect([
                'products_count' => PurchaseRequestDetail::query()
                    ->whereHas('purchaseRequest', function ($q) {
                        $q->where('purchase_request_date', $this->date);
                    })
                    ->whereColumn('outlet_id', 'outlets.id')
                    ->selectRaw('COUNT(*)'),
                'total_quantity' => PurchaseRequestDetail::query()
                    ->whereHas('purchaseRequest', function ($q) {
                        $q->where('purchase_request_date', $this->date);
                    })
                    ->whereColumn('outlet_id', 'outlets.id')
                    ->selectRaw('SUM(purchase_quantity)'),
            ]);

        $user = Auth::user();
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->where('id', $user->outlet_id);
        }

        return $query->get()->toArray();
    }

    protected function getProductsData(): array
    {
        if (!$this->date || !$this->outlet_id) {
            return [];
        }

        $query = PurchaseRequestDetail::query()
            ->with(['product', 'outlet', 'purchaseRequest'])
            ->whereHas('purchaseRequest', function ($query) {
                $query->where('purchase_request_date', $this->date);
            })
            ->where('outlet_id', $this->outlet_id);

        $user = Auth::user();
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->where('outlet_id', $user->outlet_id);
        }

        return $query->get()->toArray();
    }

    public function viewOutlets(string $date): void
    {
        $this->redirect(static::getUrl(['view' => 'outlets', 'date' => $date]));
    }

    public function viewProducts(string $date, int $outletId): void
    {
        $this->redirect(static::getUrl(['view' => 'products', 'date' => $date, 'outlet' => $outletId]));
    }

    public function backToDates(): void
    {
        $this->redirect(static::getUrl());
    }

    public function backToOutlets(): void
    {
        $this->redirect(static::getUrl(['view' => 'outlets', 'date' => $this->date]));
    }

    protected function getViewData(): array
    {
        return [
            'view_type' => $this->view_type,
            'date' => $this->date,
            'outlet_id' => $this->outlet_id,
            'reportData' => $this->reportData,
            'heading' => $this->getHeading(),
        ];
    }

    public function getView(): string
    {
        return 'filament.pages.purchase-request-reports';
    }


}
