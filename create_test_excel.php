<?php

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Create new spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'OUTLET');
$sheet->setCellValue('B1', 'NAMA PRODUK');
$sheet->setCellValue('C1', 'PRT');
$sheet->setCellValue('D1', 'BARCODE');
$sheet->setCellValue('E1', 'PACK');
$sheet->setCellValue('F1', 'QTY');
$sheet->setCellValue('G1', 'SAT');

// Add sample data
$sheet->setCellValue('A2', 'AK01');
$sheet->setCellValue('B2', 'Test Product 1');
$sheet->setCellValue('C2', 'FM');
$sheet->setCellValue('D2', 'TEST001');
$sheet->setCellValue('E2', 10);
$sheet->setCellValue('F2', 25);
$sheet->setCellValue('G2', 'tablet');

$sheet->setCellValue('A3', 'AK01');
$sheet->setCellValue('B3', 'Test Product 2');
$sheet->setCellValue('C3', 'SM');
$sheet->setCellValue('D3', 'TEST002');
$sheet->setCellValue('E3', 1);
$sheet->setCellValue('F3', 15);
$sheet->setCellValue('G3', 'bottle');

// Save file
$writer = new Xlsx($spreadsheet);
$writer->save('test_import_manual.xlsx');

echo "Test Excel file created: test_import_manual.xlsx\n";
