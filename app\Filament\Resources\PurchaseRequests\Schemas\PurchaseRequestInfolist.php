<?php

namespace App\Filament\Resources\PurchaseRequests\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class PurchaseRequestInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Purchase Request Details')
                    ->schema([
                        TextEntry::make('outlet.name')
                            ->label('Outlet'),
                        TextEntry::make('request_date')
                            ->label('Request Date')
                            ->date(),
                        TextEntry::make('notes')
                            ->label('Notes')
                            ->placeholder('No notes provided'),
                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])
                    ->columns(2),
            ]);
    }
}
