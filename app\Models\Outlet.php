<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Outlet extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'outlet_products')
                    ->withPivot(['outlet_pareto', 'rumus_pareto', 'min_buffer', 'max_buffer'])
                    ->withTimestamps();
    }

    public function outletProducts(): HasMany
    {
        return $this->hasMany(OutletProduct::class);
    }

    public function reportStockDetails(): HasMany
    {
        return $this->hasMany(ReportStockDetail::class);
    }

    public function purchaseRequestDetails(): HasMany
    {
        return $this->hasMany(PurchaseRequestDetail::class);
    }

    // Activity Log Configuration (if needed in the future)
    // public function getActivitylogOptions(): LogOptions
    // {
    //     return LogOptions::defaults()
    //         ->logOnly(['name', 'code'])
    //         ->logOnlyDirty()
    //         ->dontSubmitEmptyLogs();
    // }

    // Scopes
    public function scopeWithCounts($query)
    {
        return $query->withCount(['users', 'products', 'reportStockDetails', 'purchaseRequestDetails']);
    }

    public function scopeHasUsers($query)
    {
        return $query->has('users');
    }

    public function scopeHasProducts($query)
    {
        return $query->has('products');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} ({$this->code})";
    }

    // Mutators
    public function setCodeAttribute($value): void
    {
        $this->attributes['code'] = strtoupper($value);
    }

    // Helper Methods
    public function canBeDeleted(): bool
    {
        return $this->users()->count() === 0 &&
               $this->products()->count() === 0 &&
               $this->reportStocks()->count() === 0 &&
               $this->purchaseRequests()->count() === 0;
    }

    public function getDeletionBlockers(): array
    {
        $blockers = [];

        if ($this->users()->count() > 0) {
            $blockers[] = "Has {$this->users()->count()} user(s) assigned";
        }

        if ($this->products()->count() > 0) {
            $blockers[] = "Has {$this->products()->count()} product(s)";
        }

        if ($this->reportStocks()->count() > 0) {
            $blockers[] = "Has {$this->reportStocks()->count()} stock report(s)";
        }

        if ($this->purchaseRequests()->count() > 0) {
            $blockers[] = "Has {$this->purchaseRequests()->count()} purchase request(s)";
        }

        return $blockers;
    }
}
