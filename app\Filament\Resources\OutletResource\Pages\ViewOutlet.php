<?php

namespace App\Filament\Resources\OutletResource\Pages;

use App\Filament\Resources\OutletResource\OutletResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;

class ViewOutlet extends ViewRecord
{
    protected static string $resource = OutletResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make()
                ->label('Edit')
                ->icon('heroicon-m-pencil-square')
                ->color('warning'),

            DeleteAction::make()
                ->label('Delete')
                ->icon('heroicon-m-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Delete Outlet')
                ->modalDescription(fn () => "Are you sure you want to delete the outlet '{$this->record->name}'? This action cannot be undone.")
                ->modalSubmitActionLabel('Yes, delete it')
                ->before(function () {
                    // Check if outlet has users or products
                    if ($this->record->users()->count() > 0) {
                        throw new \Exception("Cannot delete outlet '{$this->record->name}' because it has {$this->record->users()->count()} user(s) assigned to it.");
                    }
                    if ($this->record->products()->count() > 0) {
                        throw new \Exception("Cannot delete outlet '{$this->record->name}' because it has {$this->record->products()->count()} product(s) associated with it.");
                    }
                }),
        ];
    }

    public function getTitle(): string
    {
        return $this->record->name;
    }
}
