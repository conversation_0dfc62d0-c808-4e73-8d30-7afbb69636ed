<?php

namespace App\Filament\Resources\OutletResource\Pages;

use App\Filament\Resources\OutletResource\OutletResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\CreateAction;

class ListOutlets extends ListRecords
{
    protected static string $resource = OutletResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Create Outlet')
                ->icon('heroicon-m-plus')
                ->color('primary'),
        ];
    }

    public function getTitle(): string
    {
        return 'Outlets';
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets here if needed
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            // Add widgets here if needed
        ];
    }
}
