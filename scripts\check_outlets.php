<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Outlet;

echo "=== Check Outlets ===\n\n";

$outlets = Outlet::take(10)->get();
echo "Total outlets: " . Outlet::count() . "\n\n";

echo "First 10 outlets:\n";
foreach ($outlets as $outlet) {
    echo "ID: {$outlet->id}, Code: '{$outlet->code}', Name: '{$outlet->name}'\n";
}

echo "\n=== Check Complete ===\n";
