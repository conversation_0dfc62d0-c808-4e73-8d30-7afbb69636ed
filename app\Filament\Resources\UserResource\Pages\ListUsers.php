<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource\UserResource;
use App\Filament\Resources\UserResource\Widgets\UserStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus')
                ->label('Create User'),
            
            Action::make('reset_passwords')
                ->label('Reset Passwords')
                ->icon('heroicon-o-key')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Reset User Passwords')
                ->modalDescription('This will reset all user passwords to "password123". Users will need to change their passwords after login.')
                ->modalSubmitActionLabel('Reset All Passwords')
                ->action(function () {
                    $count = User::query()->update([
                        'password' => Hash::make('password123')
                    ]);
                    
                    Notification::make()
                        ->title('Passwords Reset')
                        ->body("Successfully reset passwords for {$count} users to 'password123'")
                        ->warning()
                        ->persistent()
                        ->send();
                })
                ->visible(fn (): bool => app()->environment('local')), // Only show in local environment
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            UserStatsWidget::class,
        ];
    }
}
