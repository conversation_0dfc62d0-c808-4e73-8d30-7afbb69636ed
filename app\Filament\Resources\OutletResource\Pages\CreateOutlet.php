<?php

namespace App\Filament\Resources\OutletResource\Pages;

use App\Filament\Resources\OutletResource\OutletResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateOutlet extends CreateRecord
{
    protected static string $resource = OutletResource::class;

    public function getTitle(): string
    {
        return 'Create Outlet';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Outlet created successfully')
            ->body('The outlet has been created and is now available in the system.')
            ->duration(5000);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure code is uppercase
        $data['code'] = strtoupper($data['code']);
        
        return $data;
    }

    protected function afterCreate(): void
    {
        // Log the creation (if activity log is needed in the future)
        // activity()
        //     ->performedOn($this->record)
        //     ->causedBy(auth()->user())
        //     ->log('Outlet created');
    }
}
