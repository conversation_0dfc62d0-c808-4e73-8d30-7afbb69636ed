<?php

namespace App\Exports;

use App\Models\ReportStock;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class StockLinesExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected $reportStock;

    public function __construct(ReportStock $reportStock)
    {
        $this->reportStock = $reportStock;
    }

    public function collection()
    {
        // Get all details for this report stock
        $details = $this->reportStock->details()->with(['product', 'outlet'])->get();

        return $details->map(function ($detail) {
            // Get outlet product for pareto information
            $outletProduct = $detail->product->outlets()->where('outlet_id', $detail->outlet_id)->first();

            return [
                'barcode' => $detail->product->barcode,
                'quantity' => $detail->quantity,
                'nama_product' => $detail->product->name,
                'satuan' => $detail->product->unit,
                'pack' => $detail->product->pack_quantity,
                'outlet_pareto' => $outletProduct?->pivot->outlet_pareto ?? 'FM',
                'outlet_name' => $detail->outlet->name,
                'imported_at' => $detail->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Barcode',
            'Quantity',
            'Product Name',
            'Unit',
            'Packaging',
            'Outlet Pareto',
            'Outlet Name',
            'Imported At',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '059669'], // Green color
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // barcode
            'B' => 12, // quantity
            'C' => 35, // nama_product
            'D' => 15, // satuan
            'E' => 12, // pack
            'F' => 15, // outlet_pareto
            'G' => 20, // imported_at
        ];
    }

    public function title(): string
    {
        return 'Stock Lines - ' . $this->reportStock->report_date->format('Y-m-d');
    }
}
