<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Models\Outlet;
use App\Filament\Resources\ReportStocks\Pages\OutletSelectionByDate;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

try {
    echo "🔍 Debugging Outlet Selection Page...\n\n";
    
    // Login as manager
    $manager = User::where('email', '<EMAIL>')->first();
    Auth::login($manager);
    echo "✅ Logged in as manager\n";
    
    // Test date
    $testDate = '2025-09-02';
    $reportDate = Carbon::parse($testDate);
    echo "📅 Testing date: {$testDate}\n";
    
    // Check if report stock exists for this date
    $reportStock = ReportStock::where('report_date', $reportDate)->first();
    if (!$reportStock) {
        echo "❌ No ReportStock found for date {$testDate}\n";
        
        // Show available dates
        $availableDates = ReportStock::pluck('report_date')->map(fn($d) => $d->format('Y-m-d'));
        echo "📅 Available dates: " . $availableDates->implode(', ') . "\n";
        return;
    }
    
    echo "✅ ReportStock found: ID {$reportStock->id}\n";
    
    // Check report stock details for this date
    $details = ReportStockDetail::whereHas('reportStock', function ($q) use ($reportDate) {
        $q->where('report_date', $reportDate);
    })->get();
    
    echo "📦 Found {$details->count()} report stock details\n";
    
    if ($details->count() > 0) {
        echo "   Details breakdown:\n";
        foreach ($details as $detail) {
            echo "   - Outlet ID: {$detail->outlet_id}, Product ID: {$detail->product_id}, Qty: {$detail->quantity}\n";
        }
    }
    
    // Test the query used in OutletSelectionByDate
    echo "\n🔍 Testing OutletSelectionByDate query...\n";
    
    $outletsQuery = Outlet::query()
        ->whereHas('reportStockDetails', function ($query) use ($reportDate) {
            $query->whereHas('reportStock', function ($q) use ($reportDate) {
                $q->where('report_date', $reportDate);
            });
        })
        ->orderBy('name');
    
    echo "📊 SQL Query: " . $outletsQuery->toSql() . "\n";
    echo "📊 Bindings: " . json_encode($outletsQuery->getBindings()) . "\n";
    
    $outlets = $outletsQuery->get();
    echo "🏪 Found {$outlets->count()} outlets with reports\n";
    
    if ($outlets->count() > 0) {
        echo "   Outlets:\n";
        foreach ($outlets as $outlet) {
            echo "   - {$outlet->name} (ID: {$outlet->id})\n";
            
            // Get stats for this outlet
            $outletDetails = ReportStockDetail::where('outlet_id', $outlet->id)
                ->whereHas('reportStock', function ($q) use ($reportDate) {
                    $q->where('report_date', $reportDate);
                })
                ->get();
                
            echo "     Products: {$outletDetails->count()}\n";
            echo "     Total Qty: {$outletDetails->sum('quantity')}\n";
        }
    } else {
        echo "❌ No outlets found with reports for this date\n";
        
        // Debug: Check if outlets have reportStockDetails relation
        $allOutlets = Outlet::with('reportStockDetails')->get();
        echo "\n🔍 Debugging outlet relations:\n";
        foreach ($allOutlets as $outlet) {
            echo "   - {$outlet->name}: {$outlet->reportStockDetails->count()} details\n";
        }
    }
    
    // Test creating OutletSelectionByDate instance
    echo "\n🧪 Testing OutletSelectionByDate instantiation...\n";
    
    try {
        $page = new OutletSelectionByDate();
        $page->mount($testDate);
        echo "✅ OutletSelectionByDate mounted successfully\n";
        
        // Test table query
        $tableQuery = $page->getTableQuery();
        $tableResults = $tableQuery->get();
        echo "📋 Table query results: {$tableResults->count()} outlets\n";
        
    } catch (Exception $e) {
        echo "❌ Error creating OutletSelectionByDate: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
