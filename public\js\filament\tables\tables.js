(()=>{var g=({canTrackDeselectedRecords:u,currentSelectionLivewireProperty:o,maxSelectableRecords:l,selectsCurrentPageOnly:r,$wire:s})=>({checkboxClickController:null,collapsedGroups:[],isLoading:!1,selectedRecords:new Set,deselectedRecords:new Set,isTrackingDeselectedRecords:!1,shouldCheckUniqueSelection:!0,lastCheckedRecord:null,livewireId:null,entangledSelectedRecords:o?s.$entangle(o):null,init(){this.livewireId=this.$root.closest("[wire\\:id]")?.attributes["wire:id"].value,s.$on("deselectAllTableRecords",()=>this.deselectAllRecords()),o&&(l!==1?this.selectedRecords=new Set(this.entangledSelectedRecords):this.selectedRecords=new Set(this.entangledSelectedRecords?[this.entangledSelectedRecords]:[])),this.$nextTick(()=>this.watchForCheckboxClicks()),Livewire.hook("element.init",({component:e})=>{e.id===this.livewireId&&this.watchForCheckboxClicks()})},mountAction(...e){s.set("isTrackingDeselectedTableRecords",this.isTrackingDeselectedRecords,!1),s.set("selectedTableRecords",[...this.selectedRecords],!1),s.set("deselectedTableRecords",[...this.deselectedRecords],!1),s.mountAction(...e)},toggleSelectRecordsOnPage(){let e=this.getRecordsOnPage();if(this.areRecordsSelected(e)){this.deselectRecords(e);return}this.selectRecords(e)},toggleSelectRecords(e){this.areRecordsSelected(e)?this.deselectRecords(e):this.selectRecords(e)},getSelectedRecordsCount(){return this.isTrackingDeselectedRecords?(this.$refs.allSelectableRecordsCount?.value??this.deselectedRecords.size)-this.deselectedRecords.size:this.selectedRecords.size},getRecordsOnPage(){let e=[];for(let t of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])e.push(t.value);return e},selectRecords(e){l===1&&(this.deselectAllRecords(),e=e.slice(0,1));for(let t of e)if(!this.isRecordSelected(t)){if(this.isTrackingDeselectedRecords){this.deselectedRecords.delete(t);continue}this.selectedRecords.add(t)}this.updatedSelectedRecords()},deselectRecords(e){for(let t of e){if(this.isTrackingDeselectedRecords){this.deselectedRecords.add(t);continue}this.selectedRecords.delete(t)}this.updatedSelectedRecords()},updatedSelectedRecords(){if(l!==1){this.entangledSelectedRecords=[...this.selectedRecords];return}this.entangledSelectedRecords=[...this.selectedRecords][0]??null},toggleSelectedRecord(e){if(this.isRecordSelected(e)){this.deselectRecords([e]);return}this.selectRecords([e])},async selectAllRecords(){if(!u||r){this.isLoading=!0,this.selectedRecords=new Set(await s.getAllSelectableTableRecordKeys()),this.updatedSelectedRecords(),this.isLoading=!1;return}this.isTrackingDeselectedRecords=!0,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},canSelectAllRecords(){if(r){let i=this.getRecordsOnPage();return!this.areRecordsSelected(i)&&this.areRecordsToggleable(i)}let e=parseInt(this.$refs.allSelectableRecordsCount?.value);if(!e)return!1;let t=this.getSelectedRecordsCount();return e===t?!1:l===null||e<=l},deselectAllRecords(){this.isTrackingDeselectedRecords=!1,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},isRecordSelected(e){return this.isTrackingDeselectedRecords?!this.deselectedRecords.has(e):this.selectedRecords.has(e)},areRecordsSelected(e){return e.every(t=>this.isRecordSelected(t))},areRecordsToggleable(e){if(l===null||l===1)return!0;let t=e.filter(i=>this.isRecordSelected(i));return t.length===e.length?!0:this.getSelectedRecordsCount()+(e.length-t.length)<=l},toggleCollapseGroup(e){if(this.isGroupCollapsed(e)){this.collapsedGroups.splice(this.collapsedGroups.indexOf(e),1);return}this.collapsedGroups.push(e)},isGroupCollapsed(e){return this.collapsedGroups.includes(e)},resetCollapsedGroups(){this.collapsedGroups=[]},watchForCheckboxClicks(){this.checkboxClickController&&this.checkboxClickController.abort(),this.checkboxClickController=new AbortController;let{signal:e}=this.checkboxClickController;this.$root?.addEventListener("click",t=>t.target?.matches(".fi-ta-record-checkbox")&&this.handleCheckboxClick(t,t.target),{signal:e})},handleCheckboxClick(e,t){if(!this.lastChecked){this.lastChecked=t;return}if(e.shiftKey){let i=Array.from(this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[]);if(!i.includes(this.lastChecked)){this.lastChecked=t;return}let d=i.indexOf(this.lastChecked),n=i.indexOf(t),a=[d,n].sort((c,R)=>c-R),h=[];for(let c=a[0];c<=a[1];c++)h.push(i[c].value);if(t.checked){if(!this.areRecordsToggleable(h)){t.checked=!1,this.deselectRecords([t.value]);return}this.selectRecords(h)}else this.deselectRecords(h)}this.lastChecked=t}});function f({columns:u,isLive:o}){return{error:void 0,isLoading:!1,columns:u,isLive:o,init(){if(!this.columns||this.columns.length===0){this.columns=[];return}},get groupedColumns(){let l={};return this.columns.filter(r=>r.type==="group").forEach(r=>{l[r.name]=this.calculateGroupedColumns(r)}),l},calculateGroupedColumns(l){if((l?.columns?.filter(i=>!i.isHidden)??[]).length===0)return{hidden:!0,checked:!1,disabled:!1,indeterminate:!1};let s=l.columns.filter(i=>!i.isHidden&&i.isToggleable!==!1);if(s.length===0)return{checked:!0,disabled:!0,indeterminate:!1};let e=s.filter(i=>i.isToggled).length,t=l.columns.filter(i=>!i.isHidden&&i.isToggleable===!1);return e===0&&t.length>0?{checked:!0,disabled:!1,indeterminate:!0}:e===0?{checked:!1,disabled:!1,indeterminate:!1}:e===s.length?{checked:!0,disabled:!1,indeterminate:!1}:{checked:!0,disabled:!1,indeterminate:!0}},getColumn(l,r=null){return r?this.columns.find(e=>e.type==="group"&&e.name===r)?.columns?.find(e=>e.name===l):this.columns.find(s=>s.name===l)},toggleGroup(l){let r=this.columns.find(d=>d.type==="group"&&d.name===l);if(!r?.columns)return;let s=this.calculateGroupedColumns(r);if(s.disabled)return;let t=r.columns.filter(d=>d.isToggleable!==!1).some(d=>d.isToggled),i=s.indeterminate?!0:!t;r.columns.filter(d=>d.isToggleable!==!1).forEach(d=>{d.isToggled=i}),this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},toggleColumn(l,r=null){let s=this.getColumn(l,r);!s||s.isToggleable===!1||(s.isToggled=!s.isToggled,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager())},reorderColumns(l){let r=l.map(s=>s.split("::"));this.reorderTopLevel(r),this.isLive&&this.applyTableColumnManager()},reorderGroupColumns(l,r){let s=this.columns.find(i=>i.type==="group"&&i.name===r);if(!s)return;let e=l.map(i=>i.split("::")),t=[];e.forEach(([i,d])=>{let n=s.columns.find(a=>a.name===d);n&&t.push(n)}),s.columns=t,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},reorderTopLevel(l){let r=this.columns,s=[];l.forEach(([e,t])=>{let i=r.find(d=>e==="group"?d.type==="group"&&d.name===t:e==="column"?d.type!=="group"&&d.name===t:!1);i&&s.push(i)}),this.columns=s},async applyTableColumnManager(){this.isLoading=!0;try{await this.$wire.call("applyTableColumnManager",this.columns),this.error=void 0}catch(l){this.error="Failed to update column visibility",console.error("Table toggle columns error:",l)}finally{this.isLoading=!1}}}}document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentTable",g),window.Alpine.data("filamentTableColumnManager",f)});})();
