<?php

namespace App\Filament\Resources\PurchaseRequests\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class PurchaseRequestsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('purchase_request_date')
                    ->label('Purchase Request Date')
                    ->date('l, F j, Y')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('primary')
                    ->badge()
                    ->description(fn ($record) => $record->purchase_request_date->diffForHumans()),

                TextColumn::make('total_outlets')
                    ->label('Outlets')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('info')
                    ->getStateUsing(fn ($record) => $record->getTotalOutlets())
                    ->description('Number of different outlets'),

                TextColumn::make('total_products')
                    ->label('Total Products')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(fn ($record) => $record->getTotalProducts())
                    ->description('Number of different products'),

                TextColumn::make('total_quantity')
                    ->label('Total Quantity')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state <= 10 => 'warning',
                        $state <= 50 => 'success',
                        default => 'primary',
                    })
                    ->getStateUsing(fn ($record) => $record->getTotalQuantity())
                    ->description('Total purchase quantity'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->created_at->diffForHumans()),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->updated_at->diffForHumans()),
            ])
            ->filters([
                // Add date range filter if needed
            ])
            ->recordActions([
                ViewAction::make()
                    ->label('View')
                    ->icon('heroicon-m-eye')
                    ->color('info'),

                EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-m-pencil-square')
                    ->color('warning'),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Requests')
                        ->modalDescription('Are you sure you want to delete the selected purchase requests? This action cannot be undone.')
                        ->modalSubmitActionLabel('Yes, delete them'),
                ]),
            ])
            ->defaultSort('purchase_request_date', 'desc')
            ->searchPlaceholder('Search by date...')
            ->emptyStateHeading('No purchase requests found')
            ->emptyStateDescription('Create your first purchase request to get started.')
            ->emptyStateIcon('heroicon-o-shopping-cart')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
