<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\OutletProduct;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use Carbon\Carbon;

class ReportStockNavigationTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $outlet;
    protected $products;
    protected $reportStocks;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create test user with manager role
        $this->user = User::factory()->create([
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
        ]);
        $this->user->assignRole('manager');

        // Create test outlets
        $this->outlet = Outlet::create([
            'name' => 'Test Outlet 1',
            'code' => 'TEST01',
        ]);

        $outlet2 = Outlet::create([
            'name' => 'Test Outlet 2', 
            'code' => 'TEST02',
        ]);

        // Create test products
        $this->products = collect([
            Product::create([
                'name' => 'Test Product 1',
                'barcode' => 'TEST001',
                'unit' => 'tablet',
                'pack_quantity' => 10,
            ]),
            Product::create([
                'name' => 'Test Product 2',
                'barcode' => 'TEST002',
                'unit' => 'bottle',
                'pack_quantity' => 1,
            ]),
            Product::create([
                'name' => 'Test Product 3',
                'barcode' => 'TEST003',
                'unit' => 'box',
                'pack_quantity' => 12,
            ]),
        ]);

        // Create outlet product configurations
        foreach ($this->products as $product) {
            OutletProduct::create([
                'outlet_id' => $this->outlet->id,
                'product_id' => $product->id,
                'outlet_pareto' => 'FM',
                'rumus_pareto' => 'FM',
                'min_buffer' => 10,
                'max_buffer' => 50,
            ]);
        }

        // Create test report stocks for different dates
        $dates = [
            Carbon::today(),
            Carbon::today()->subDays(1),
            Carbon::today()->subDays(2),
        ];

        foreach ($dates as $date) {
            foreach ($this->products as $index => $product) {
                ReportStock::create([
                    'outlet_id' => $this->outlet->id,
                    'product_id' => $product->id,
                    'report_date' => $date,
                    'quantity' => ($index + 1) * 10, // 10, 20, 30
                ]);
            }
        }

        // Create some report stocks for outlet2
        ReportStock::create([
            'outlet_id' => $outlet2->id,
            'product_id' => $this->products->first()->id,
            'report_date' => Carbon::today(),
            'quantity' => 25,
        ]);
    }

    /** @test */
    public function user_can_access_report_stock_main_page()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->assertSee('Report Stocks')
                    ->assertSee('View by Outlet')
                    ->assertSee('Download Template')
                    ->assertSee('Import Report Stocks');
        });
    }

    /** @test */
    public function user_can_navigate_to_outlet_selection()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('View by Outlet')
                    ->assertUrlIs('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet')
                    ->assertSee('Select an outlet to view available stock report dates')
                    ->assertSee('Test Outlet 1')
                    ->assertSee('TEST01');
        });
    }

    /** @test */
    public function outlet_selection_displays_correct_statistics()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Test Outlet 1')
                    ->assertSee('TEST01')
                    ->assertSee('Total Reports')
                    ->assertSee('Latest Report')
                    ->assertSee('Products');
        });
    }

    /** @test */
    public function user_can_navigate_from_outlet_to_date_selection()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->click('tr:first-child td:first-child') // Click first outlet row
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Test Outlet 1')
                    ->assertSee('TEST01')
                    ->assertSee('Select a date to view detailed stock report')
                    ->assertSee('Total Report Dates')
                    ->assertSee('Unique Products')
                    ->assertSee('Total Stock')
                    ->assertSee('Latest Report');
        });
    }

    /** @test */
    public function date_selection_displays_correct_breadcrumb()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Select Outlet')
                    ->assertSee('Select Date')
                    ->assertSee('View Details');
        });
    }

    /** @test */
    public function date_selection_shows_report_dates_with_statistics()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Products Reported')
                    ->assertSee('Total Stock')
                    ->assertSee('Out of Stock')
                    ->assertSee('Low Stock')
                    ->assertSee('Reported At');
        });
    }

    /** @test */
    public function user_can_navigate_from_date_to_stock_details()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->click('tr:first-child td:first-child') // Click first date row
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Stock Report Details')
                    ->assertSee('Test Outlet 1')
                    ->assertSee('Total Products')
                    ->assertSee('Total Stock')
                    ->assertSee('Out of Stock')
                    ->assertSee('Low Stock')
                    ->assertSee('Good Stock');
        });
    }

    /** @test */
    public function stock_details_displays_product_information()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Test Product 1')
                    ->assertSee('TEST001')
                    ->assertSee('tablet')
                    ->assertSee('Stock Quantity')
                    ->assertSee('Pareto')
                    ->assertSee('Status')
                    ->assertSee('Pack Size');
        });
    }

    /** @test */
    public function stock_details_shows_correct_breadcrumb_and_navigation()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Select Outlet')
                    ->assertSee('Select Date') 
                    ->assertSee('View Details')
                    ->assertSee('Back to Dates')
                    ->assertSee('Back to Outlets');
        });
    }

    /** @test */
    public function user_can_navigate_back_from_stock_details_to_dates()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->clickLink('Back to Dates')
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Select a date to view detailed stock report');
        });
    }

    /** @test */
    public function user_can_navigate_back_from_stock_details_to_outlets()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->clickLink('Back to Outlets')
                    ->waitForLocation('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet');
        });
    }

    /** @test */
    public function user_can_navigate_back_from_dates_to_outlets()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->clickLink('Back to Outlets')
                    ->waitForLocation('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet');
        });
    }

    /** @test */
    public function stock_details_filters_work_correctly()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Stock Status')
                    ->assertSee('Pareto Category')
                    ->select('tableFilters.stock_status.value', 'good_stock')
                    ->pause(1000) // Wait for filter to apply
                    ->assertDontSee('Out of Stock');
        });
    }

    /** @test */
    public function search_functionality_works_in_stock_details()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->type('tableSearch', 'Test Product 1')
                    ->pause(1000) // Wait for search to apply
                    ->assertSee('Test Product 1')
                    ->assertDontSee('Test Product 2');
        });
    }

    /** @test */
    public function responsive_design_works_on_mobile_viewport()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Test Outlet 1')
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Total Report Dates');
        });
    }
}
