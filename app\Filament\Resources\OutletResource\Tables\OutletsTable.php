<?php

namespace App\Filament\Resources\OutletResource\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\TextInput;

class OutletsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->description(fn ($record) => "Code: {$record->code}")
                    ->copyable()
                    ->copyMessage('Outlet name copied!')
                    ->copyMessageDuration(1500),

                TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->copyable()
                    ->copyMessage('Outlet code copied!')
                    ->copyMessageDuration(1500),

                TextColumn::make('users_count')
                    ->label('Users')
                    ->counts('users')
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state === 0 => 'gray',
                        $state <= 5 => 'success',
                        $state <= 10 => 'warning',
                        default => 'danger',
                    })
                    ->description('Number of users assigned'),

                TextColumn::make('products_count')
                    ->label('Products')
                    ->counts('products')
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state === 0 => 'gray',
                        $state <= 50 => 'success',
                        $state <= 100 => 'warning',
                        default => 'danger',
                    })
                    ->description('Number of products'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->created_at->diffForHumans()),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->updated_at->diffForHumans()),
            ])
            ->filters([
                Filter::make('has_users')
                    ->label('Has Users')
                    ->query(fn (Builder $query): Builder => $query->has('users'))
                    ->toggle(),

                Filter::make('has_products')
                    ->label('Has Products')
                    ->query(fn (Builder $query): Builder => $query->has('products'))
                    ->toggle(),

                Filter::make('no_users')
                    ->label('No Users')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('users'))
                    ->toggle(),

                Filter::make('created_recently')
                    ->label('Created Recently')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(7)))
                    ->toggle(),
            ])
            ->actions([
                ViewAction::make()
                    ->label('View')
                    ->icon('heroicon-m-eye')
                    ->color('info'),

                EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-m-pencil-square')
                    ->color('warning'),

                DeleteAction::make()
                    ->label('Delete')
                    ->icon('heroicon-m-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Outlet')
                    ->modalDescription(fn ($record) => "Are you sure you want to delete the outlet '{$record->name}'? This action cannot be undone.")
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->before(function ($record) {
                        // Check if outlet has users or products
                        if ($record->users()->count() > 0) {
                            throw new \Exception("Cannot delete outlet '{$record->name}' because it has {$record->users()->count()} user(s) assigned to it.");
                        }
                        if ($record->products()->count() > 0) {
                            throw new \Exception("Cannot delete outlet '{$record->name}' because it has {$record->products()->count()} product(s) associated with it.");
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Outlets')
                        ->modalDescription('Are you sure you want to delete the selected outlets? This action cannot be undone.')
                        ->modalSubmitActionLabel('Yes, delete them')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                if ($record->users()->count() > 0 || $record->products()->count() > 0) {
                                    throw new \Exception("Cannot delete outlet '{$record->name}' because it has associated users or products.");
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->searchPlaceholder('Search outlets by name or code...')
            ->emptyStateHeading('No outlets found')
            ->emptyStateDescription('Create your first outlet to get started.')
            ->emptyStateIcon('heroicon-o-building-storefront')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
