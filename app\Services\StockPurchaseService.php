<?php

namespace App\Services;

use App\Models\ReportStock;
use App\Models\Product;
use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestLine;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class StockPurchaseService
{
    /**
     * Generate purchase request from stock report data
     */
    public function generatePurchaseRequest(ReportStock $reportStock): PurchaseRequest
    {
        // Create purchase request header
        $purchaseRequest = PurchaseRequest::create([
            'purchase_request_date' => now()->toDateString(),
        ]);

        $totalItems = 0;

        // Get all stock report details for this date
        $stockDetails = $reportStock->details()->with(['product', 'outlet'])->get();

        foreach ($stockDetails as $stockDetail) {
            $product = $stockDetail->product;

            if (!$product) {
                continue;
            }

            // Get outlet product for buffer information
            $outletProduct = $product->outlets()
                ->where('outlet_id', $stockDetail->outlet_id)
                ->first();

            if (!$outletProduct) {
                continue;
            }

            $purchaseQuantity = $this->calculatePurchaseQuantity(
                $stockDetail->quantity,
                $outletProduct->pivot->rumus_pareto,
                $outletProduct->pivot->min_buffer,
                $outletProduct->pivot->max_buffer,
                $product->pack_quantity
            );

            if ($purchaseQuantity > 0) {
                \App\Models\PurchaseRequestDetail::create([
                    'purchase_request_id' => $purchaseRequest->id,
                    'outlet_id' => $stockDetail->outlet_id,
                    'product_id' => $product->id,
                    'purchase_quantity' => $purchaseQuantity,
                ]);

                $totalItems++;
            }
        }

        $reportStock->update(['is_generated' => true]);

        return $purchaseRequest;
    }

    /**
     * Calculate purchase quantity based on Pareto formula and stock levels
     */
    private function calculatePurchaseQuantity(
        float $currentStock,
        ?string $paretoFormula,
        float $minBuffer,
        float $maxBuffer,
        ?int $packaging
    ): float {
        // If no pareto formula or buffers are set, skip
        if (!$paretoFormula || $minBuffer <= 0 || $maxBuffer <= 0 || $packaging <= 0) {
            return 0;
        }

        if($currentStock <= $minBuffer) {
            switch ($paretoFormula) {
                case 'FM': // Fast moving
                    // Fast moving: target closer to max buffer
                    $purchaseQuantity = ceil(($maxBuffer - $currentStock)/$packaging) * $packaging;
                    break;
                    
                default:
                    // Slow moving: target closer to min buffer
                    $purchaseQuantity = floor(($maxBuffer - $currentStock)/$packaging) * $packaging;
                    break;
            }
    
            return $purchaseQuantity;
        }else{
            return 0;
        }


        // // Base calculation: if current stock is above minimum buffer, we might not need to purchase
        // if ($currentStock >= $minBuffer) {
        //     // Only purchase if we're below 80% of min buffer for fast moving items
        //     if ($paretoFormula === 'FM' && $currentStock < ($minBuffer * 0.8)) {
        //         $targetStock = $this->calculateTargetStock($paretoFormula, $minBuffer, $maxBuffer, $outletPareto);
        //         return $this->applyRounding($targetStock - $currentStock, $paretoFormula);
        //     }
        //     return 0; // No need to purchase
        // }

        // // Calculate target stock based on Pareto formula
        // $targetStock = $this->calculateTargetStock($paretoFormula, $minBuffer, $maxBuffer, $outletPareto);
        
        // // Purchase quantity is the difference between target and current stock
        // $purchaseQuantity = $targetStock - $currentStock;

        // // Apply rounding based on Pareto formula
        // return $this->applyRounding($purchaseQuantity, $paretoFormula);
    }

    /**
     * Calculate target stock based on Pareto formula and outlet category
     */
    private function calculateTargetStock(string $paretoFormula, float $minBuffer, float $maxBuffer, ?string $outletPareto): float
    {
        $baseTarget = ($minBuffer + $maxBuffer) / 2;

        switch ($paretoFormula) {
            case 'FM': // Fast Moving
                // Fast moving: target closer to max buffer
                $target = $maxBuffer * 0.9;
                break;
                
            case 'SM': // Slow Moving
            default:
                // Slow moving: target closer to min buffer
                $target = $minBuffer + (($maxBuffer - $minBuffer) * 0.3);
                break;
        }

        // Adjust based on outlet pareto category
        switch ($outletPareto) {
            case 'A': // High priority outlet
                return $target * 1.2; // 20% more stock
                
            case 'B': // Medium priority outlet
                return $target; // Standard stock
                
            case 'C': // Low priority outlet
            default:
                return $target * 0.8; // 20% less stock
        }
    }

    /**
     * Apply rounding based on Pareto formula
     */
    private function applyRounding(float $quantity, string $paretoFormula): float
    {
        if ($quantity <= 0) {
            return 0;
        }

        switch ($paretoFormula) {
            case 'FM': // Fast Moving
                // Fast moving: round up to ensure availability
                return ceil($quantity);
                
            case 'SM': // Slow Moving
            default:
                // Slow moving: round to nearest whole number
                return round($quantity);
        }
    }

    /**
     * Get purchase recommendations for a stock report
     */
    public function getPurchaseRecommendations(ReportStock $reportStock): Collection
    {
        $stockDetails = $reportStock->details()->with(['product', 'outlet'])->get();

        return $stockDetails->map(function ($stockDetail) {
            $product = $stockDetail->product;

            if (!$product) {
                return null;
            }

            // Get outlet product for buffer information
            $outletProduct = $product->outlets()
                ->where('outlet_id', $stockDetail->outlet_id)
                ->first();

            if (!$outletProduct) {
                return null;
            }

            $purchaseQuantity = $this->calculatePurchaseQuantity(
                $stockDetail->quantity,
                $outletProduct->pivot->rumus_pareto,
                $outletProduct->pivot->min_buffer,
                $outletProduct->pivot->max_buffer,
                $product->pack_quantity
            );

            return [
                'product' => $product,
                'outlet' => $stockDetail->outlet,
                'current_stock' => $stockDetail->quantity,
                'min_buffer' => $outletProduct->pivot->min_buffer,
                'max_buffer' => $outletProduct->pivot->max_buffer,
                'pareto_formula' => $outletProduct->pivot->rumus_pareto,
                'outlet_pareto' => $outletProduct->pivot->outlet_pareto,
                'recommended_quantity' => $purchaseQuantity,
                'status' => $this->getStockStatus($stockDetail->quantity, $outletProduct->pivot->min_buffer, $outletProduct->pivot->max_buffer),
            ];
        })->filter()->values();
    }

    /**
     * Get stock status based on current stock and buffers
     */
    private function getStockStatus(float $currentStock, float $minBuffer, float $maxBuffer): string
    {
        if ($currentStock <= 0) {
            return 'out_of_stock';
        } elseif ($currentStock < $minBuffer) {
            return 'below_minimum';
        } elseif ($currentStock < $minBuffer * 1.2) {
            return 'low_stock';
        } elseif ($currentStock > $maxBuffer) {
            return 'overstock';
        } else {
            return 'normal';
        }
    }

    /**
     * Check if a stock report needs purchase recommendations
     */
    public function needsPurchaseRecommendations(ReportStock $reportStock): bool
    {
        $recommendations = $this->getPurchaseRecommendations($reportStock);
        return $recommendations->where('recommended_quantity', '>', 0)->count() > 0;
    }
}
