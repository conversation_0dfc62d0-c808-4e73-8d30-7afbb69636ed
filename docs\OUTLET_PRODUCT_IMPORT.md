# Outlet Product Import Documentation

## Overview
Fitur import outlet product memungkinkan Anda untuk mengimpor konfigurasi produk outlet dalam jumlah besar menggunakan file Excel. Fitur ini juga dapat membuat produk baru secara otomatis jika produk dengan barcode tertentu belum ada dalam sistem.

## Format Excel

### Kolom yang Di<PERSON>

| Kolom | Nama Field | Tipe | Wajib | Deskripsi |
|-------|------------|------|-------|-----------|
| A | outlet_code | String | Ya | Kode outlet yang sudah ada di sistem |
| B | barcode | String | Ya | Barcode produk (akan dibuat jika belum ada) |
| C | product_name | String | Ya | Nama produk |
| D | unit | String | Tidak | Satuan produk (default: 'pcs') |
| E | pack_quantity | Integer | Tidak | Jumlah per kemasan (default: 1) |
| F | outlet_pareto | String | Tidak | Kategori pareto outlet (FM/SM/BM, default: 'FM') |
| G | rumus_pareto | String | Tidak | Rumus pareto (FM/SM/BM, default: 'FM') |
| H | min_buffer | Integer | Tidak | Buffer minimum (default: 10) |
| I | max_buffer | Integer | Tidak | Buffer maksimum (default: 50) |

### Contoh Data

```
outlet_code | barcode | product_name    | unit   | pack_quantity | outlet_pareto | rumus_pareto | min_buffer | max_buffer
OUTLET001   | PROD001 | Sample Product 1| tablet | 10           | FM           | FM          | 5          | 20
OUTLET001   | PROD002 | Sample Product 2| bottle | 1            | SM           | SM          | 3          | 15
OUTLET002   | PROD003 | Sample Product 3| box    | 12           | BM           | BM          | 2          | 10
```

## Cara Menggunakan

### 1. Download Template
1. Buka halaman **Outlet Product Configurations**
2. Klik tombol **"Download Template"**
3. File template Excel akan diunduh dengan format yang benar

### 2. Isi Data
1. Buka file template yang sudah diunduh
2. Hapus baris contoh (baris 2-4)
3. Isi data sesuai dengan format yang ditentukan
4. Pastikan:
   - `outlet_code` sesuai dengan kode outlet yang ada di sistem
   - `barcode` unik untuk setiap produk
   - `product_name` tidak kosong
   - `outlet_pareto` dan `rumus_pareto` hanya berisi: FM, SM, atau BM

### 3. Import Data
1. Klik tombol **"Import Outlet Products"**
2. Upload file Excel yang sudah diisi
3. Klik **"Import"**
4. Tunggu proses selesai dan lihat notifikasi hasil

## Fitur Otomatis

### 1. Pembuatan Produk Baru
- Jika produk dengan barcode tertentu belum ada, sistem akan membuat produk baru
- Data produk diambil dari kolom: `product_name`, `unit`, `pack_quantity`

### 2. Update Produk Existing
- Jika produk sudah ada, sistem akan mengupdate data jika ada perbedaan
- Field yang diupdate: `name`, `unit`, `pack_quantity`

### 3. Konfigurasi Outlet Product
- Sistem akan membuat atau mengupdate konfigurasi outlet product
- Jika kombinasi outlet + produk sudah ada, data akan diupdate
- Jika belum ada, akan dibuat konfigurasi baru

## Validasi Data

### Validasi Wajib
- `outlet_code`: Harus ada dan outlet harus exist di sistem
- `barcode`: Harus ada dan tidak boleh kosong
- `product_name`: Harus ada dan tidak boleh kosong

### Validasi Opsional
- `pack_quantity`: Harus berupa angka positif (min: 1)
- `outlet_pareto`: Hanya boleh FM, SM, atau BM
- `rumus_pareto`: Hanya boleh FM, SM, atau BM
- `min_buffer`: Harus berupa angka non-negatif
- `max_buffer`: Harus berupa angka non-negatif

## Error Handling

### Jenis Error yang Mungkin Terjadi
1. **Outlet tidak ditemukan**: Outlet dengan kode tertentu tidak ada di sistem
2. **Data tidak valid**: Format data tidak sesuai dengan validasi
3. **File corrupt**: File Excel rusak atau tidak bisa dibaca
4. **Permission error**: User tidak memiliki akses untuk membuat/update data

### Cara Mengatasi Error
1. Periksa kembali format data sesuai template
2. Pastikan semua outlet code sudah ada di sistem
3. Pastikan file Excel tidak corrupt
4. Hubungi administrator jika ada masalah permission

## Tips Penggunaan

1. **Gunakan Template**: Selalu download template terbaru untuk memastikan format yang benar
2. **Backup Data**: Backup data sebelum melakukan import besar-besaran
3. **Test dengan Data Kecil**: Coba import dengan beberapa baris data dulu
4. **Periksa Log**: Perhatikan notifikasi hasil import untuk memastikan semua data berhasil
5. **Unique Barcode**: Pastikan barcode unik untuk menghindari konflik data

## Batasan

- Maximum file size: 10MB
- Maximum rows: 1000 baris per import
- Supported formats: .xlsx, .xls
- Batch processing: 100 rows per batch untuk performa optimal
