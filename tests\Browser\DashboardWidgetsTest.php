<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use App\Models\User;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\PurchaseRequest;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class DashboardWidgetsTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'OutletSeeder']);
        $this->artisan('db:seed', ['--class' => 'UserSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    public function test_stock_overview_widget_displays_correctly()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Total Products')
                    ->assertSee('Stock Reports')
                    ->assertSee('Low Stock Items')
                    ->assertSee('Out of Stock');
        });
    }

    public function test_purchase_requests_overview_widget_for_manager()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Total Requests')
                    ->assertSee('This Month')
                    ->assertSee('Today')
                    ->assertSee('Total Items');
        });
    }

    public function test_stock_trends_chart_is_visible()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Stock Reports Trend')
                    ->waitFor('canvas', 10); // Wait for chart to load
        });
    }

    public function test_purchase_request_trends_chart_for_manager()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Purchase Requests Trend')
                    ->waitFor('canvas', 10); // Wait for chart to load
        });
    }

    public function test_pareto_analysis_chart_displays()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Product Pareto Analysis')
                    ->waitFor('canvas', 10); // Wait for chart to load
        });
    }

    public function test_outlet_comparison_chart_for_manager_only()
    {
        // Test that manager can see outlet comparison
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Outlet Comparison')
                    ->waitFor('canvas', 10); // Wait for chart to load
        });

        // Test that admin cannot see outlet comparison
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertDontSee('Outlet Comparison'); // Admin shouldn't see this widget
        });
    }

    public function test_recent_activities_widget_displays()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Recent Stock Reports')
                    ->assertSee('Outlet') // Should show table headers
                    ->assertSee('Report Date');
        });
    }

    public function test_widget_data_accuracy_for_admin()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create some test data
            Product::factory()->count(5)->create(['outlet_id' => $admin->outlet_id]);
            ReportStock::factory()->count(2)->create(['outlet_id' => $admin->outlet_id]);
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Total Products'); // Should show products count
        });
    }

    public function test_widget_data_accuracy_for_manager()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            // Create some test data across outlets
            Product::factory()->count(10)->create();
            PurchaseRequest::factory()->count(3)->create();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Total Products')
                    ->assertSee('Total Requests');
        });
    }

    public function test_widgets_are_responsive()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->resize(1200, 800) // Desktop view
                    ->assertSee('Total Products')
                    ->resize(768, 1024) // Tablet view
                    ->assertSee('Total Products')
                    ->resize(375, 667); // Mobile view
        });
    }

    public function test_widget_loading_performance()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $startTime = microtime(true);
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->waitFor('[data-widget="stock-overview"]', 10)
                    ->waitFor('canvas', 10); // Wait for charts to load
            
            $endTime = microtime(true);
            $loadTime = $endTime - $startTime;
            
            // Assert that dashboard loads within reasonable time (10 seconds)
            $this->assertLessThan(10, $loadTime, 'Dashboard should load within 10 seconds');
        });
    }

    public function test_widget_interactivity()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->waitFor('canvas', 10) // Wait for charts to load
                    ->mouseover('canvas') // Hover over chart to test interactivity
                    ->pause(1000); // Pause to see hover effects
        });
    }

    public function test_widget_error_handling()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Test with no data
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Total Products')
                    ->assertSee('0'); // Should show 0 when no data
        });
    }

    public function test_account_widget_displays_user_info()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Welcome to Laravel') // Account widget greeting
                    ->assertSee('Admin User'); // User name
        });
    }
}
