<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Product;

// Update products with pareto formulas
$products = Product::all();

foreach ($products as $product) {
    $paretoFormulas = ['FM', 'SM'];
    $product->update([
        'rumus_pareto' => $paretoFormulas[array_rand($paretoFormulas)],
    ]);
    echo 'Updated product: ' . $product->name . ' - Pareto Formula: ' . $product->rumus_pareto . PHP_EOL;
}

echo 'Updated all product pareto formulas' . PHP_EOL;
