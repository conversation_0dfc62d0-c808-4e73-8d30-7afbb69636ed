<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use App\Models\User;
use App\Models\Outlet;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class FilamentResourcesTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'OutletSeeder']);
        $this->artisan('db:seed', ['--class' => 'UserSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    public function test_admin_can_login_and_access_dashboard()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->visit('/admin/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password')
                    ->press('Sign in')
                    ->waitForLocation('/admin')
                    ->assertSee('Dashboard')
                    ->assertSee('Admin User');
        });
    }

    public function test_manager_can_login_and_access_dashboard()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/admin/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password')
                    ->press('Sign in')
                    ->waitForLocation('/admin')
                    ->assertSee('Dashboard')
                    ->assertSee('Manager User');
        });
    }

    public function test_admin_can_access_products_resource()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Products')
                    ->assertSee('New product')
                    ->assertSee('Export to Excel');
        });
    }

    public function test_admin_can_create_product()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('@create-button')
                    ->waitForText('Create product')
                    ->type('name', 'Test Product')
                    ->type('barcode', '1234567890999')
                    ->type('unit', 'tablet')
                    ->type('packaging', '10')
                    ->type('min_buffer', '20')
                    ->type('max_buffer', '100')
                    ->press('Create')
                    ->waitForText('Test Product')
                    ->assertSee('Test Product');
        });
    }

    public function test_admin_can_access_report_stocks_resource()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/report-stocks')
                    ->assertSee('Report Stocks')
                    ->assertSee('New report stock');
        });
    }

    public function test_manager_can_access_purchase_requests_resource()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Purchase Requests')
                    ->assertSee('New purchase request')
                    ->assertSee('Export to Excel');
        });
    }

    public function test_manager_can_create_purchase_request()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            $outlet = Outlet::first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->click('@create-button')
                    ->waitForText('Create purchase request')
                    ->select('outlet_id', $outlet->id)
                    ->type('notes', 'Test purchase request')
                    ->press('Create')
                    ->waitForText('Test purchase request')
                    ->assertSee('Test purchase request');
        });
    }

    public function test_unauthorized_user_cannot_access_admin()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/admin')
                    ->assertPathIs('/admin/login')
                    ->assertSee('Sign in to your account');
        });
    }

    public function test_dashboard_widgets_are_visible()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Total Products')
                    ->assertSee('Stock Reports')
                    ->assertSee('Low Stock Items')
                    ->assertSee('Out of Stock');
        });
    }
}
