<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "=== Testing Foreign Key Constraint Fix ===\n\n";

// Enable query logging for debugging
DB::enableQueryLog();

// Set up custom log handler to capture debug output
$logMessages = [];
Log::listen(function ($level, $message, $context) use (&$logMessages) {
    $logMessages[] = [
        'level' => $level,
        'message' => $message,
        'context' => $context,
    ];
});

// Create test outlet if not exists
$outlet = Outlet::firstOrCreate(['code' => 'TEST001'], ['name' => 'Test Outlet']);
echo "Using outlet: {$outlet->code} - {$outlet->name}\n";

// Create test data that should trigger the foreign key issue
$testRows = collect([
    [
        'outlet' => 'TEST001',
        'nama_produk' => 'Test Product New 1',
        'prt' => 'A',
        'barcode' => 'TEST_BARCODE_001',
        'pack' => '1',
        'qty' => 100,
        'sat' => 'PCS',
    ],
    [
        'outlet' => 'TEST001',
        'nama_produk' => 'Test Product New 2',
        'prt' => 'B',
        'barcode' => 'TEST_BARCODE_002',
        'pack' => '2',
        'qty' => 50,
        'sat' => 'BOX',
    ],
]);

echo "Test data prepared with " . $testRows->count() . " rows\n\n";

// Get initial counts
$initialCounts = [
    'products' => Product::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

echo "Initial counts:\n";
foreach ($initialCounts as $table => $count) {
    echo "  {$table}: {$count}\n";
}
echo "\n";

// Test the import
$reportDate = '2025-09-12';
echo "Testing import with report date: {$reportDate}\n";

try {
    $import = new ReportStockBulkImport($reportDate);
    $import->collection($testRows);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Get final counts
    $finalCounts = [
        'products' => Product::count(),
        'outlet_products' => OutletProduct::count(),
        'report_stock_details' => ReportStockDetail::count(),
    ];
    
    echo "Final counts:\n";
    foreach ($finalCounts as $table => $count) {
        $change = $count - $initialCounts[$table];
        $changeStr = $change > 0 ? " (+{$change})" : ($change < 0 ? " ({$change})" : "");
        echo "  {$table}: {$count}{$changeStr}\n";
    }
    echo "\n";
    
    // Show import statistics
    echo "Import Statistics:\n";
    $summary = $import->getImportSummary();
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "  {$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    echo "\n";
    
    // Verify the data was inserted correctly
    echo "Verification:\n";
    
    // Check products
    $newProducts = Product::whereIn('barcode', ['TEST_BARCODE_001', 'TEST_BARCODE_002'])->get();
    echo "  Products created: " . $newProducts->count() . "\n";
    foreach ($newProducts as $product) {
        echo "    - {$product->barcode}: {$product->name} (ID: {$product->id})\n";
    }
    
    // Check outlet products
    $outletProducts = OutletProduct::where('outlet_id', $outlet->id)
        ->whereIn('product_id', $newProducts->pluck('id'))
        ->get();
    echo "  Outlet products created: " . $outletProducts->count() . "\n";
    foreach ($outletProducts as $op) {
        echo "    - Product ID {$op->product_id}, Pareto: {$op->outlet_pareto}\n";
    }
    
    // Check report stock details
    $reportStock = ReportStock::where('report_date', $reportDate)->first();
    if ($reportStock) {
        $reportDetails = ReportStockDetail::where('report_stock_id', $reportStock->id)
            ->where('outlet_id', $outlet->id)
            ->whereIn('product_id', $newProducts->pluck('id'))
            ->get();
        echo "  Report stock details created: " . $reportDetails->count() . "\n";
        foreach ($reportDetails as $detail) {
            echo "    - Product ID {$detail->product_id}, Quantity: {$detail->quantity}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";

    // Show log messages
    echo "Log Messages:\n";
    foreach ($logMessages as $log) {
        if ($log['level'] === 'debug' || $log['level'] === 'info' || $log['level'] === 'error') {
            echo "  [{$log['level']}] {$log['message']}\n";
            if (!empty($log['context'])) {
                echo "    Context: " . json_encode($log['context'], JSON_PRETTY_PRINT) . "\n";
            }
        }
    }
    echo "\n";

    // Show some debug info
    echo "Debug Information:\n";
    
    // Check if products were created
    $testProducts = Product::whereIn('barcode', ['TEST_BARCODE_001', 'TEST_BARCODE_002'])->get();
    echo "  Products found: " . $testProducts->count() . "\n";
    foreach ($testProducts as $product) {
        echo "    - {$product->barcode}: ID {$product->id}\n";
    }
    
    // Show recent queries
    $queries = DB::getQueryLog();
    echo "\n  Recent queries (" . count($queries) . " total):\n";
    foreach (array_slice($queries, -5) as $query) {
        echo "    - " . $query['query'] . "\n";
        if (!empty($query['bindings'])) {
            echo "      Bindings: " . implode(', ', $query['bindings']) . "\n";
        }
    }
}

// Clean up test data
echo "\nCleaning up test data...\n";
try {
    // Delete test report stock details
    $reportStock = ReportStock::where('report_date', $reportDate)->first();
    if ($reportStock) {
        ReportStockDetail::where('report_stock_id', $reportStock->id)
            ->where('outlet_id', $outlet->id)
            ->delete();
        
        // Delete report stock if no details left
        if ($reportStock->details()->count() === 0) {
            $reportStock->delete();
        }
    }
    
    // Delete test outlet products
    OutletProduct::where('outlet_id', $outlet->id)
        ->whereHas('product', function($q) {
            $q->whereIn('barcode', ['TEST_BARCODE_001', 'TEST_BARCODE_002']);
        })
        ->delete();
    
    // Delete test products
    Product::whereIn('barcode', ['TEST_BARCODE_001', 'TEST_BARCODE_002'])->delete();
    
    // Delete test outlet if it was created for this test
    if ($outlet->code === 'TEST001' && $outlet->users()->count() === 0 && $outlet->outletProducts()->count() === 0) {
        $outlet->delete();
    }
    
    echo "✅ Test data cleaned up successfully\n";
    
} catch (\Exception $e) {
    echo "⚠️  Warning: Could not clean up all test data: " . $e->getMessage() . "\n";
}

echo "\n=== Test Completed ===\n";
