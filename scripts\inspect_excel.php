<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;

class ExcelInspector implements ToCollection, WithHeadingRow
{
    public $headers = [];
    public $sampleRows = [];
    
    public function collection(Collection $rows)
    {
        // Get first few rows for inspection
        $this->sampleRows = $rows->take(5)->toArray();
        
        // Get headers from first row
        if ($rows->isNotEmpty()) {
            $this->headers = array_keys($rows->first()->toArray());
        }
    }
}

$path = $argv[1] ?? 'D:\laragon\www\apotek-keluarga\REKAP SOH OUTLET 07092025.xlsx';

if (!file_exists($path)) {
    echo "File not found: {$path}\n";
    exit(1);
}

echo "=== Excel File Inspector ===\n";
echo "File: {$path}\n";
echo "Size: " . round(filesize($path) / 1024 / 1024, 2) . " MB\n\n";

$inspector = new ExcelInspector();

try {
    Excel::import($inspector, $path);
    
    echo "Headers found:\n";
    foreach ($inspector->headers as $index => $header) {
        echo "  [{$index}] '{$header}'\n";
    }
    
    echo "\nSample data (first 5 rows):\n";
    foreach ($inspector->sampleRows as $rowIndex => $row) {
        echo "\nRow " . ($rowIndex + 1) . ":\n";
        foreach ($row as $key => $value) {
            $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "'{$value}'" : $value);
            echo "  {$key}: {$displayValue}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
