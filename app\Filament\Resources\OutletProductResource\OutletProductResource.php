<?php

namespace App\Filament\Resources\OutletProductResource;

use App\Filament\Resources\OutletProductResource\Pages;
use App\Filament\Resources\OutletProductResource\Schemas\OutletProductForm;
use App\Filament\Resources\OutletProductResource\Tables\OutletProductsTable;
use App\Models\OutletProduct;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class OutletProductResource extends Resource
{
    protected static ?string $model = OutletProduct::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBuildingOffice2;

    protected static ?string $navigationLabel = 'Outlet Products';

    protected static ?string $modelLabel = 'Outlet Product';

    protected static ?string $pluralModelLabel = 'Outlet Products';

    protected static ?int $navigationSort = 3;

    public static function form(Schema $schema): Schema
    {
        return OutletProductForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return OutletProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOutletProducts::route('/'),
            'create' => Pages\CreateOutletProduct::route('/create'),
            'view' => Pages\ViewOutletProduct::route('/{record}'),
            'edit' => Pages\EditOutletProduct::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return Auth::user()?->hasPermissionTo('view_outlets') ?? false;
    }

    public static function canCreate(): bool
    {
        return Auth::user()?->hasPermissionTo('create_outlets') ?? false;
    }

    public static function canEdit($record): bool
    {
        return Auth::user()?->hasPermissionTo('edit_outlets') ?? false;
    }

    public static function canDelete($record): bool
    {
        return Auth::user()?->hasPermissionTo('delete_outlets') ?? false;
    }

    public static function canDeleteAny(): bool
    {
        return Auth::user()?->hasPermissionTo('delete_outlets') ?? false;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['outlet', 'product'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
