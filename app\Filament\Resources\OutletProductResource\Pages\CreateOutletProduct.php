<?php

namespace App\Filament\Resources\OutletProductResource\Pages;

use App\Filament\Resources\OutletProductResource\OutletProductResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateOutletProduct extends CreateRecord
{
    protected static string $resource = OutletProductResource::class;

    public function getTitle(): string
    {
        return 'Create Outlet Product Configuration';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Configuration created successfully')
            ->body('The outlet product configuration has been created.')
            ->duration(5000);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure numeric fields are properly cast
        $data['min_buffer'] = (int) ($data['min_buffer'] ?? 0);
        $data['max_buffer'] = (int) ($data['max_buffer'] ?? 0);
        
        return $data;
    }
}
