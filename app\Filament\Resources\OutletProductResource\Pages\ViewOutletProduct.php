<?php

namespace App\Filament\Resources\OutletProductResource\Pages;

use App\Filament\Resources\OutletProductResource\OutletProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;

class ViewOutletProduct extends ViewRecord
{
    protected static string $resource = OutletProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make()
                ->label('Edit')
                ->icon('heroicon-m-pencil-square')
                ->color('warning'),

            DeleteAction::make()
                ->label('Delete')
                ->icon('heroicon-m-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Delete Configuration')
                ->modalDescription(fn () => "Are you sure you want to delete this outlet product configuration?")
                ->modalSubmitActionLabel('Yes, delete it'),
        ];
    }

    public function getTitle(): string
    {
        return "Configuration: {$this->record->product->name} @ {$this->record->outlet->name}";
    }
}
