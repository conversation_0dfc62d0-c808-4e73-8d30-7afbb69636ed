<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ReportStock extends Model
{
    use HasFactory;

    protected $fillable = [
        'report_date',
        'is_generated',
    ];

    protected $casts = [
        'report_date' => 'date',
        'is_generated' => 'boolean',
    ];

    public function details(): HasMany
    {
        return $this->hasMany(ReportStockDetail::class);
    }

    // Get unique outlets for this report date
    public function getOutlets()
    {
        return Outlet::whereIn('id',
            $this->details()->distinct('outlet_id')->pluck('outlet_id')
        )->get();
    }

    // Scopes
    public function scopeForDate($query, $date)
    {
        return $query->where('report_date', $date);
    }


    // Helper methods
    public function getDisplayName(): string
    {
        return "Report - {$this->report_date->format('Y-m-d')}";
    }

    public function getTotalOutlets(): int
    {
        return $this->details()->distinct('outlet_id')->count('outlet_id');
    }

    public function getTotalProducts(): int
    {
        return $this->details()->count();
    }

    public function getTotalQuantity(): int
    {
        return $this->details()->sum('quantity');
    }

    // Get statistics for this report date
    public function getOutletStats()
    {
        return $this->details()
            ->selectRaw('outlet_id, COUNT(*) as product_count, SUM(quantity) as total_quantity')
            ->with('outlet')
            ->groupBy('outlet_id')
            ->get();
    }
}
