<?php

namespace App\Filament\Resources\PurchaseRequests\RelationManagers;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DissociateAction;
use Filament\Actions\DissociateBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class PurchaseRequestLinesRelationManager extends RelationManager
{
    protected static string $relationship = 'details';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('product_id')
                    ->label('Product')
                    ->relationship('product', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->getOptionLabelFromRecordUsing(fn ($record) => "{$record->name} ({$record->barcode})")
                    ->helperText('Select a product from the inventory'),
                TextInput::make('purchase_quantity')
                    ->label('Purchase Quantity')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->step(1)
                    ->helperText('Enter the quantity to purchase'),
            ]);
    }

    public function infolist(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('product.name')
                    ->label('Product Name'),
                TextEntry::make('product.barcode')
                    ->label('Product Barcode'),
                TextEntry::make('purchase_quantity')
                    ->label('Purchase Quantity')
                    ->numeric(),
                TextEntry::make('product.unit')
                    ->label('Unit'),
                TextEntry::make('product.packaging')
                    ->label('Packaging'),
                TextEntry::make('created_at')
                    ->label('Added At')
                    ->dateTime(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('product.barcode')
                    ->label('Barcode')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('purchase_quantity')
                    ->label('Quantity')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('product.unit')
                    ->label('Unit')
                    ->sortable(),
                TextColumn::make('product.pack_quantity')
                    ->label('Pack Size')
                    ->sortable()
                    ->numeric()
                    ->description('Units per pack'),
                TextColumn::make('created_at')
                    ->label('Added At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Add Product')
                    ->icon('heroicon-o-plus')
                    ->modalHeading('Add Product to Purchase Request')
                    ->modalDescription('Select a product and specify the quantity to purchase.')
                    ->modalSubmitActionLabel('Add Product')
                    ->using(function (array $data) {
                        $data['outlet_id'] = $this->ownerRecord->outlet_id;
                        return $this->getRelationship()->create($data);
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DissociateAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DissociateBulkAction::make(),
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
