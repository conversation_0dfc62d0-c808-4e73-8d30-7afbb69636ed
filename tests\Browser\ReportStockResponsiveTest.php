<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\OutletProduct;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use Carbon\Carbon;

class ReportStockResponsiveTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $outlet;
    protected $products;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create test user with manager role
        $this->user = User::factory()->create([
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
        ]);
        $this->user->assignRole('manager');

        // Create test outlet
        $this->outlet = Outlet::create([
            'name' => 'Test Outlet',
            'code' => 'TEST01',
        ]);

        // Create test products
        $this->products = collect([
            Product::create([
                'name' => 'Test Product 1',
                'barcode' => 'TEST001',
                'unit' => 'tablet',
                'pack_quantity' => 10,
            ]),
            Product::create([
                'name' => 'Test Product 2',
                'barcode' => 'TEST002',
                'unit' => 'bottle',
                'pack_quantity' => 1,
            ]),
        ]);

        // Create outlet product configurations
        foreach ($this->products as $product) {
            OutletProduct::create([
                'outlet_id' => $this->outlet->id,
                'product_id' => $product->id,
                'outlet_pareto' => 'FM',
                'rumus_pareto' => 'FM',
                'min_buffer' => 10,
                'max_buffer' => 50,
            ]);
        }

        // Create test report stocks
        foreach ($this->products as $index => $product) {
            ReportStock::create([
                'outlet_id' => $this->outlet->id,
                'product_id' => $product->id,
                'report_date' => Carbon::today(),
                'quantity' => ($index + 1) * 10,
            ]);
        }
    }

    /** @test */
    public function outlet_selection_is_responsive_on_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet')
                    ->assertSee('Test Outlet')
                    ->assertPresent('.grid') // Grid layout should be present
                    ->assertPresent('[data-testid="breadcrumb"]'); // Breadcrumb should be visible
        });
    }

    /** @test */
    public function outlet_selection_is_responsive_on_tablet()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet')
                    ->assertSee('Test Outlet')
                    ->assertPresent('.grid')
                    ->assertVisible('table'); // Table should be visible on tablet
        });
    }

    /** @test */
    public function date_selection_statistics_are_responsive()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Total Report Dates')
                    ->assertSee('Unique Products')
                    ->assertSee('Total Stock')
                    ->assertSee('Latest Report')
                    ->assertPresent('.grid'); // Statistics grid should be present
        });
    }

    /** @test */
    public function stock_details_table_is_horizontally_scrollable_on_mobile()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Test Product 1')
                    ->assertPresent('table')
                    ->assertPresent('.overflow-x-auto'); // Horizontal scroll container
        });
    }

    /** @test */
    public function navigation_buttons_are_touch_friendly()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertPresent('a:contains("Back to Outlets")')
                    ->click('a:contains("Back to Outlets")')
                    ->waitForLocation('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet');
        });
    }

    /** @test */
    public function breadcrumb_navigation_works_on_mobile()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Select Outlet')
                    ->assertSee('Select Date')
                    ->assertSee('View Details')
                    ->click('a:contains("Select Outlet")')
                    ->waitForLocation('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet');
        });
    }

    /** @test */
    public function statistics_cards_stack_properly_on_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertPresent('.grid-cols-1') // Should use single column on mobile
                    ->assertSee('Total Report Dates')
                    ->assertSee('Unique Products');
        });
    }

    /** @test */
    public function desktop_layout_shows_all_columns()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(1920, 1080) // Desktop viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Outlet Code')
                    ->assertSee('Outlet Name')
                    ->assertSee('Total Reports')
                    ->assertSee('Latest Report')
                    ->assertSee('Products');
        });
    }

    /** @test */
    public function tablet_layout_maintains_readability()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->resize(768, 1024) // iPad viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Test Product 1')
                    ->assertSee('Stock Quantity')
                    ->assertSee('Pareto')
                    ->assertSee('Status')
                    ->assertVisible('table');
        });
    }

    /** @test */
    public function import_modal_is_responsive()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->assertSee('Report Date')
                    ->assertSee('Excel File')
                    ->assertPresent('input[type="date"]')
                    ->assertPresent('input[type="file"]');
        });
    }

    /** @test */
    public function filters_are_accessible_on_mobile()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->resize(375, 667) // Mobile viewport
                    ->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertPresent('select[name*="stock_status"]')
                    ->assertPresent('select[name*="pareto"]')
                    ->assertPresent('input[name*="search"]');
        });
    }

    /** @test */
    public function page_loads_quickly_on_slow_connection()
    {
        $this->browse(function (Browser $browser) {
            // Simulate slow 3G connection
            $browser->driver->getCommandExecutor()->execute([
                'cmd' => 'Network.enable',
                'params' => []
            ]);
            
            $browser->driver->getCommandExecutor()->execute([
                'cmd' => 'Network.emulateNetworkConditions',
                'params' => [
                    'offline' => false,
                    'latency' => 100,
                    'downloadThroughput' => 750 * 1024 / 8, // 750 kbps
                    'uploadThroughput' => 250 * 1024 / 8,   // 250 kbps
                ]
            ]);

            $startTime = microtime(true);
            
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->assertSee('Stock Reports by Outlet');
            
            $loadTime = microtime(true) - $startTime;
            
            // Page should load within 5 seconds even on slow connection
            $this->assertLessThan(5, $loadTime, 'Page took too long to load on slow connection');
        });
    }

    /** @test */
    public function keyboard_navigation_works()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    ->keys('body', '{tab}') // Tab to first focusable element
                    ->keys('body', '{enter}') // Press enter
                    ->pause(1000) // Wait for navigation
                    ->assertPathIs('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates');
        });
    }

    /** @test */
    public function high_contrast_mode_is_readable()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    // Simulate high contrast mode by checking color contrast
                    ->assertPresent('.text-gray-900') // Dark text should be present
                    ->assertPresent('.bg-white') // Light background should be present
                    ->assertSee('Test Outlet'); // Content should still be visible
        });
    }

    /** @test */
    public function zoom_level_150_percent_maintains_usability()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets')
                    // Simulate 150% zoom by reducing viewport
                    ->resize(1280, 720) // Smaller viewport simulates zoom
                    ->assertSee('Stock Reports by Outlet')
                    ->assertSee('Test Outlet')
                    ->assertPresent('table')
                    ->click('tr:first-child td:first-child')
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee('Total Report Dates');
        });
    }

    /** @test */
    public function print_styles_work_correctly()
    {
        $reportDate = Carbon::today()->format('Y-m-d');
        
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Test Product 1')
                    // Check that print-specific elements are present
                    ->assertPresent('table')
                    ->assertPresent('.text-gray-900'); // Ensure text is dark for printing
        });
    }
}
