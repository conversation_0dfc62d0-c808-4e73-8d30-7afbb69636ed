<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use App\Models\User;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\PurchaseRequest;
use App\Models\Outlet;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class CrudOperationsTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'OutletSeeder']);
        $this->artisan('db:seed', ['--class' => 'UserSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    // Product CRUD Tests
    public function test_admin_can_create_product()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('New product')
                    ->waitForText('Create product')
                    ->type('name', 'Test Product CRUD')
                    ->type('barcode', '1111111111111')
                    ->type('unit', 'tablet')
                    ->type('packaging', '10')
                    ->select('outlet_pareto', 'A')
                    ->select('rumus_pareto', 'FM')
                    ->type('min_buffer', '25')
                    ->type('max_buffer', '150')
                    ->press('Create')
                    ->waitForText('Test Product CRUD')
                    ->assertSee('Test Product CRUD');
        });
    }

    public function test_admin_can_edit_product()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create a product first
            $product = Product::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'name' => 'Product to Edit',
                'barcode' => '2222222222222'
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Product to Edit')
                    ->click('@edit-' . $product->id)
                    ->waitForText('Edit product')
                    ->clear('name')
                    ->type('name', 'Edited Product Name')
                    ->press('Save changes')
                    ->waitForText('Edited Product Name')
                    ->assertSee('Edited Product Name');
        });
    }

    public function test_admin_can_delete_product()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create a product first
            $product = Product::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'name' => 'Product to Delete',
                'barcode' => '3333333333333'
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Product to Delete')
                    ->check('table tbody tr:first-child input[type="checkbox"]') // Select product
                    ->click('Delete selected')
                    ->waitForText('Are you sure')
                    ->press('Delete')
                    ->waitUntilMissing('Product to Delete')
                    ->assertDontSee('Product to Delete');
        });
    }

    // Report Stock CRUD Tests
    public function test_admin_can_create_report_stock()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/report-stocks')
                    ->click('New report stock')
                    ->waitForText('Create report stock')
                    ->press('Create')
                    ->waitForLocation('/admin/report-stocks')
                    ->assertSee(now()->format('M d, Y')); // Should see today's date
        });
    }

    public function test_admin_can_edit_report_stock()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create a report stock first
            $reportStock = ReportStock::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'report_date' => now()->subDay()
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/report-stocks')
                    ->click('@edit-' . $reportStock->id)
                    ->waitForText('Edit report stock')
                    ->type('report_date', now()->format('Y-m-d'))
                    ->press('Save changes')
                    ->waitForLocation('/admin/report-stocks')
                    ->assertSee(now()->format('M d, Y'));
        });
    }

    // Purchase Request CRUD Tests (Manager only)
    public function test_manager_can_create_purchase_request()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            $outlet = Outlet::first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->click('New purchase request')
                    ->waitForText('Create purchase request')
                    ->select('outlet_id', $outlet->id)
                    ->type('notes', 'Test purchase request from CRUD test')
                    ->press('Create')
                    ->waitForText('Test purchase request from CRUD test')
                    ->assertSee('Test purchase request from CRUD test');
        });
    }

    public function test_manager_can_edit_purchase_request()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            // Create a purchase request first
            $purchaseRequest = PurchaseRequest::factory()->create([
                'outlet_id' => 1,
                'request_date' => now(),
                'notes' => 'Original notes'
            ]);
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Original notes')
                    ->click('@edit-' . $purchaseRequest->id)
                    ->waitForText('Edit purchase request')
                    ->clear('notes')
                    ->type('notes', 'Updated notes from CRUD test')
                    ->press('Save changes')
                    ->waitForText('Updated notes from CRUD test')
                    ->assertSee('Updated notes from CRUD test');
        });
    }

    public function test_manager_can_view_purchase_request_details()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            // Create a purchase request first
            $purchaseRequest = PurchaseRequest::factory()->create([
                'outlet_id' => 1,
                'request_date' => now(),
                'notes' => 'Detailed view test'
            ]);
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->click('@view-' . $purchaseRequest->id)
                    ->waitForText('Purchase Request Details')
                    ->assertSee('Detailed view test')
                    ->assertSee('Purchase Request Lines');
        });
    }

    // Validation Tests
    public function test_product_creation_validation()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('New product')
                    ->waitForText('Create product')
                    ->press('Create') // Try to create without required fields
                    ->waitForText('The name field is required')
                    ->assertSee('The name field is required')
                    ->assertSee('The barcode field is required');
        });
    }

    public function test_duplicate_barcode_validation()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create a product with a specific barcode
            Product::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'barcode' => '4444444444444'
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('New product')
                    ->waitForText('Create product')
                    ->type('name', 'Duplicate Barcode Test')
                    ->type('barcode', '4444444444444') // Same barcode
                    ->type('unit', 'tablet')
                    ->type('packaging', '10')
                    ->type('min_buffer', '20')
                    ->type('max_buffer', '100')
                    ->press('Create')
                    ->waitForText('The barcode has already been taken')
                    ->assertSee('The barcode has already been taken');
        });
    }

    // Search and Filter Tests
    public function test_product_search_functionality()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Create a searchable product
            Product::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'name' => 'Searchable Product Test',
                'barcode' => '5555555555555'
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->type('search', 'Searchable Product')
                    ->pause(1000) // Wait for search to process
                    ->assertSee('Searchable Product Test');
        });
    }

    public function test_product_filter_functionality()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('Filter')
                    ->waitForText('Outlet Pareto')
                    ->select('outlet_pareto', 'A')
                    ->press('Apply')
                    ->pause(1000); // Wait for filter to apply
        });
    }
}
