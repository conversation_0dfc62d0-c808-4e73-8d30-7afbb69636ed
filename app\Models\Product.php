<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'barcode',
        'unit',
        'pack_quantity',
    ];

    protected $casts = [
        'pack_quantity' => 'integer',
    ];

    // Many-to-many relationship with outlets through outlet_products pivot table
    public function outlets(): BelongsToMany
    {
        return $this->belongsToMany(Outlet::class, 'outlet_products')
                    ->withPivot(['outlet_pareto', 'rumus_pareto', 'min_buffer', 'max_buffer'])
                    ->withTimestamps();
    }

    // Relationship to report stock details
    public function reportStockDetails(): HasMany
    {
        return $this->hasMany(ReportStockDetail::class);
    }

    // Relationship to purchase request details
    public function purchaseRequestDetails(): HasMany
    {
        return $this->hasMany(PurchaseRequestDetail::class);
    }

    // Relationship to outlet products (pivot records)
    public function outletProducts(): HasMany
    {
        return $this->hasMany(OutletProduct::class);
    }

    // Scopes
    public function scopeForOutlet($query, $outletId)
    {
        return $query->whereHas('outlets', function ($q) use ($outletId) {
            $q->where('outlet_id', $outletId);
        });
    }

    // Helper methods
    public function getOutletPareto($outletId): ?string
    {
        $outletProduct = $this->outletProducts()->where('outlet_id', $outletId)->first();
        return $outletProduct?->outlet_pareto;
    }

    public function getMinBuffer($outletId): int
    {
        $outletProduct = $this->outletProducts()->where('outlet_id', $outletId)->first();
        return $outletProduct?->min_buffer ?? 0;
    }

    public function getMaxBuffer($outletId): int
    {
        $outletProduct = $this->outletProducts()->where('outlet_id', $outletId)->first();
        return $outletProduct?->max_buffer ?? 0;
    }
}
