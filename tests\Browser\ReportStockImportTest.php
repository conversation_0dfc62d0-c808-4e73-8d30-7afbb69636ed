<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\ReportStock;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ReportStockImportTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $outlet;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create test user with manager role
        $this->user = User::factory()->create([
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
        ]);
        $this->user->assignRole('manager');

        // Create test outlet
        $this->outlet = Outlet::create([
            'name' => 'Test Outlet',
            'code' => 'TEST01',
        ]);
    }

    protected function createTestExcelFile()
    {
        // Create a simple CSV file for testing (easier than Excel)
        $csvContent = "outlet_code,barcode,product_name,unit,pack_quantity,quantity\n";
        $csvContent .= "TEST01,IMPORT001,Import Test Product 1,tablet,10,25\n";
        $csvContent .= "TEST01,IMPORT002,Import Test Product 2,bottle,1,15\n";
        $csvContent .= "TEST01,IMPORT003,Import Test Product 3,box,12,8\n";

        $filename = 'test_import_' . time() . '.csv';
        $path = storage_path('app/public/test_files/' . $filename);
        
        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        file_put_contents($path, $csvContent);
        
        return $path;
    }

    /** @test */
    public function user_can_access_import_functionality()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->assertSee('Import Report Stocks')
                    ->assertSee('Download Template');
        });
    }

    /** @test */
    public function user_can_download_template()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Download Template')
                    ->pause(2000); // Wait for download to start
            
            // Note: We can't easily test file download in browser tests
            // This test just ensures the link is clickable and doesn't error
        });
    }

    /** @test */
    public function user_can_open_import_modal()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->assertSee('Report Date')
                    ->assertSee('Excel File')
                    ->assertSee('Upload Excel file with stock report data');
        });
    }

    /** @test */
    public function import_modal_has_correct_form_fields()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->assertPresent('input[type="date"]') // Report date field
                    ->assertPresent('input[type="file"]') // File upload field
                    ->assertSee('Import'); // Submit button
        });
    }

    /** @test */
    public function user_can_select_report_date()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', '2024-01-15')
                    ->assertInputValue('data.report_date', '2024-01-15');
        });
    }

    /** @test */
    public function import_form_validation_works()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->press('Import')
                    ->waitForText('The excel file field is required')
                    ->assertSee('The excel file field is required');
        });
    }

    /** @test */
    public function user_can_upload_file_and_submit_import()
    {
        $testFile = $this->createTestExcelFile();
        
        $this->browse(function (Browser $browser) use ($testFile) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', now()->format('Y-m-d'))
                    ->attach('data.excel_file', $testFile)
                    ->press('Import')
                    ->waitForText('Import completed successfully', 10)
                    ->assertSee('Import completed successfully')
                    ->assertSee('Processed rows')
                    ->assertSee('Products created')
                    ->assertSee('Stock reports created');
        });

        // Clean up test file
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }

    /** @test */
    public function import_creates_products_and_stock_reports()
    {
        $testFile = $this->createTestExcelFile();
        
        $initialProductCount = Product::count();
        $initialStockCount = ReportStock::count();
        
        $this->browse(function (Browser $browser) use ($testFile) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', now()->format('Y-m-d'))
                    ->attach('data.excel_file', $testFile)
                    ->press('Import')
                    ->waitForText('Import completed successfully', 10);
        });

        // Verify data was created
        $this->assertGreaterThan($initialProductCount, Product::count());
        $this->assertGreaterThan($initialStockCount, ReportStock::count());
        
        // Verify specific products were created
        $this->assertDatabaseHas('products', ['barcode' => 'IMPORT001']);
        $this->assertDatabaseHas('products', ['barcode' => 'IMPORT002']);
        $this->assertDatabaseHas('products', ['barcode' => 'IMPORT003']);

        // Clean up test file
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }

    /** @test */
    public function import_handles_invalid_outlet_code()
    {
        // Create CSV with invalid outlet code
        $csvContent = "outlet_code,barcode,product_name,unit,pack_quantity,quantity\n";
        $csvContent .= "INVALID,IMPORT001,Import Test Product 1,tablet,10,25\n";

        $filename = 'test_invalid_' . time() . '.csv';
        $path = storage_path('app/public/test_files/' . $filename);
        
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        file_put_contents($path, $csvContent);
        
        $this->browse(function (Browser $browser) use ($path) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', now()->format('Y-m-d'))
                    ->attach('data.excel_file', $path)
                    ->press('Import')
                    ->waitForText('Import completed successfully', 10)
                    ->assertSee('Errors encountered');
        });

        // Clean up test file
        if (file_exists($path)) {
            unlink($path);
        }
    }

    /** @test */
    public function imported_data_appears_in_navigation_flow()
    {
        $testFile = $this->createTestExcelFile();
        $reportDate = now()->format('Y-m-d');
        
        // First, import the data
        $this->browse(function (Browser $browser) use ($testFile, $reportDate) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', $reportDate)
                    ->attach('data.excel_file', $testFile)
                    ->press('Import')
                    ->waitForText('Import completed successfully', 10);
        });

        // Then verify it appears in the navigation flow
        $this->browse(function (Browser $browser) use ($reportDate) {
            $browser->visit('/admin/report-stocks/outlets')
                    ->assertSee('Test Outlet')
                    ->click('tr:contains("Test Outlet") td:first-child')
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates')
                    ->assertSee(now()->format('M j, Y')) // Today's date should appear
                    ->click('tr:first-child td:first-child')
                    ->waitForLocation('/admin/report-stocks/outlets/' . $this->outlet->id . '/dates/' . $reportDate . '/details')
                    ->assertSee('Import Test Product 1')
                    ->assertSee('IMPORT001');
        });

        // Clean up test file
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }

    /** @test */
    public function user_can_cancel_import_modal()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->press('Cancel')
                    ->waitUntilMissing('.fi-modal')
                    ->assertDontSee('Import Report Stocks');
        });
    }

    /** @test */
    public function import_modal_closes_after_successful_import()
    {
        $testFile = $this->createTestExcelFile();
        
        $this->browse(function (Browser $browser) use ($testFile) {
            $browser->loginAs($this->user)
                    ->visit('/admin/report-stocks')
                    ->clickLink('Import Report Stocks')
                    ->waitForText('Import Report Stocks')
                    ->type('data.report_date', now()->format('Y-m-d'))
                    ->attach('data.excel_file', $testFile)
                    ->press('Import')
                    ->waitForText('Import completed successfully', 10)
                    ->waitUntilMissing('.fi-modal', 5)
                    ->assertDontSee('Upload Excel file with stock report data');
        });

        // Clean up test file
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }

    protected function tearDown(): void
    {
        // Clean up any remaining test files
        $testDir = storage_path('app/public/test_files');
        if (is_dir($testDir)) {
            $files = glob($testDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        
        parent::tearDown();
    }
}
