<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use App\Models\User;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\PurchaseRequest;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ComprehensiveCrudTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected $manager;
    protected $admin;
    protected $outlet;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        $permissions = [
            'view_outlets', 'create_outlets', 'edit_outlets', 'delete_outlets',
            'view_products', 'create_products', 'edit_products', 'delete_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks', 'delete_report_stocks',
            'import_report_stocks', 'view_purchase_requests', 'create_purchase_requests',
            'edit_purchase_requests', 'delete_purchase_requests', 'generate_purchase_requests',
            'export_reports', 'view_all_outlets', 'manage_users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);

        // Assign permissions to roles
        $adminPermissions = [
            'view_products', 'create_products', 'edit_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks',
            'import_report_stocks', 'export_reports',
        ];
        $adminRole->syncPermissions($adminPermissions);

        $managerPermissions = [
            'view_outlets', 'create_outlets', 'edit_outlets', 'delete_outlets',
            'view_products', 'create_products', 'edit_products', 'delete_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks', 'delete_report_stocks',
            'import_report_stocks', 'view_purchase_requests', 'create_purchase_requests',
            'edit_purchase_requests', 'delete_purchase_requests', 'generate_purchase_requests',
            'export_reports', 'view_all_outlets', 'manage_users',
        ];
        $managerRole->syncPermissions($managerPermissions);

        // Create outlet
        $this->outlet = Outlet::create(['name' => 'Test Outlet', 'code' => 'TEST001']);

        // Create product
        $this->product = Product::create([
            'name' => 'Test Product',
            'barcode' => 'TEST123456',
            'unit' => 'pcs',
            'pack_quantity' => 10,
        ]);

        // Create manager user
        $this->manager = User::factory()->create([
            'username' => 'test_manager',
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        $this->manager->assignRole('manager');

        // Create admin user
        $this->admin = User::factory()->create([
            'username' => 'test_admin',
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'outlet_id' => $this->outlet->id,
        ]);
        $this->admin->assignRole('admin');
    }

    /**
     * Test complete Product CRUD operations
     */
    public function test_product_crud_operations()
    {
        $this->browse(function (Browser $browser) {
            // Test Product Creation
            $browser->loginAs($this->manager)
                    ->visit('/admin/products')
                    ->assertSee('Products')
                    ->clickLink('Create')
                    ->assertPathIs('/admin/products/create')
                    ->type('name', 'Playwright Test Product')
                    ->type('barcode', 'PLW123456')
                    ->type('unit', 'bottles')
                    ->type('pack_quantity', '12')
                    ->press('Create')
                    ->waitForText('Product created successfully', 10)
                    ->assertPathIs('/admin/products')
                    ->assertSee('Playwright Test Product')
                    ->assertSee('PLW123456')
                    ->screenshot('product-created');

            // Test Product Editing
            $browser->clickLink('Edit')
                    ->assertSee('Edit')
                    ->clear('name')
                    ->type('name', 'Updated Playwright Product')
                    ->press('Save changes')
                    ->waitForText('Product updated successfully', 10)
                    ->assertSee('Updated Playwright Product')
                    ->screenshot('product-updated');
        });

        // Verify in database
        $this->assertDatabaseHas('products', [
            'name' => 'Updated Playwright Product',
            'barcode' => 'PLW123456',
            'unit' => 'bottles',
            'pack_quantity' => 12,
        ]);
    }

    /**
     * Test OutletProduct CRUD operations
     */
    public function test_outlet_product_crud_operations()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlet-products')
                    ->assertSee('Outlet Product')
                    ->clickLink('Create Configuration')
                    ->assertSee('Create Outlet Product Configuration')
                    ->select('outlet_id', $this->outlet->id)
                    ->select('product_id', $this->product->id)
                    ->select('outlet_pareto', 'A')
                    ->select('rumus_pareto', 'FM')
                    ->type('min_buffer', '5')
                    ->type('max_buffer', '50')
                    ->press('Create')
                    ->waitForText('Configuration created successfully', 10)
                    ->assertSee($this->outlet->name)
                    ->assertSee($this->product->name)
                    ->assertSee('A')
                    ->assertSee('FM')
                    ->screenshot('outlet-product-created');
        });

        // Verify in database
        $this->assertDatabaseHas('outlet_products', [
            'outlet_id' => $this->outlet->id,
            'product_id' => $this->product->id,
            'outlet_pareto' => 'A',
            'rumus_pareto' => 'FM',
            'min_buffer' => 5,
            'max_buffer' => 50,
        ]);
    }

    /**
     * Test ReportStock CRUD operations
     */
    public function test_report_stock_crud_operations()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/report-stocks')
                    ->assertSee('Report Stocks')
                    ->clickLink('Create')
                    ->assertSee('Create Report Stock')
                    ->select('outlet_id', $this->outlet->id)
                    ->select('product_id', $this->product->id)
                    ->type('quantity', '25')
                    ->press('Create')
                    ->waitForText('Report stock created successfully', 10)
                    ->assertSee($this->outlet->name)
                    ->assertSee($this->product->name)
                    ->assertSee('25')
                    ->screenshot('report-stock-created');
        });

        // Verify in database
        $this->assertDatabaseHas('report_stocks', [
            'outlet_id' => $this->outlet->id,
            'product_id' => $this->product->id,
            'quantity' => 25,
        ]);
    }

    /**
     * Test PurchaseRequest CRUD operations
     */
    public function test_purchase_request_crud_operations()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Purchase Requests')
                    ->clickLink('Create')
                    ->assertSee('Create Purchase Request')
                    ->select('outlet_id', $this->outlet->id)
                    ->select('product_id', $this->product->id)
                    ->type('purchase_quantity', '100')
                    ->press('Create')
                    ->waitForText('Purchase request created successfully', 10)
                    ->assertSee($this->outlet->name)
                    ->assertSee($this->product->name)
                    ->assertSee('100')
                    ->screenshot('purchase-request-created');
        });

        // Verify in database
        $this->assertDatabaseHas('purchase_requests', [
            'outlet_id' => $this->outlet->id,
            'product_id' => $this->product->id,
            'purchase_quantity' => 100,
        ]);
    }

    /**
     * Test search functionality across all resources
     */
    public function test_search_functionality()
    {
        // Create additional test data
        Product::create([
            'name' => 'Searchable Product Alpha',
            'barcode' => 'SEARCH001',
            'unit' => 'pcs',
            'pack_quantity' => 1,
        ]);

        Product::create([
            'name' => 'Searchable Product Beta',
            'barcode' => 'SEARCH002',
            'unit' => 'pcs',
            'pack_quantity' => 1,
        ]);

        $this->browse(function (Browser $browser) {
            // Test Product search
            $browser->loginAs($this->manager)
                    ->visit('/admin/products')
                    ->type('input[placeholder*="Search"]', 'Alpha')
                    ->pause(1000)
                    ->assertSee('Searchable Product Alpha')
                    ->assertDontSee('Searchable Product Beta')
                    ->clear('input[placeholder*="Search"]')
                    ->type('input[placeholder*="Search"]', 'SEARCH002')
                    ->pause(1000)
                    ->assertSee('Searchable Product Beta')
                    ->assertDontSee('Searchable Product Alpha')
                    ->screenshot('product-search');
        });
    }

    /**
     * Test responsive design on mobile
     */
    public function test_responsive_design()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->resize(375, 667) // iPhone size
                    ->visit('/admin/products')
                    ->assertSee('Products')
                    ->visit('/admin/outlets')
                    ->assertSee('Outlets')
                    ->visit('/admin/outlet-products')
                    ->assertSee('Outlet Product')
                    ->screenshot('mobile-responsive');
        });
    }

    /**
     * Test admin user restrictions
     */
    public function test_admin_user_restrictions()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/outlets')
                    ->assertSee('403')
                    ->visit('/admin/users')
                    ->assertSee('403')
                    ->screenshot('admin-restrictions');
        });
    }
}
