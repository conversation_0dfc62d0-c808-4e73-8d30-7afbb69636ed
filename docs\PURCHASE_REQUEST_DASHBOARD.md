# Purchase Request Dashboard - Drill-Down Navigation

## Overview

Dashboard baru untuk Purchase Request dengan navigasi drill-down hierarkis:
**Purchase Dates → Outlets → Products**

## Fitur Utama

### 🗓️ **Level 1: Purchase Dates**
- Menampilkan semua tanggal yang memiliki purchase request
- Statistik per tanggal:
  - Total requests
  - Jumlah outlet yang terlibat
  - Total produk di semua request
- Klik pada tanggal untuk melihat outlet

### 🏪 **Level 2: Outlets** 
- Menampilkan outlet yang memiliki purchase request pada tanggal yang dipilih
- Statistik per outlet:
  - Jumlah request
  - Total produk
  - Total quantity
- Klik pada outlet untuk melihat produk

### 🛒 **Level 3: Products**
- Menampilkan detail produk dalam purchase request
- Informasi lengkap:
  - Nama produk & barcode
  - Unit & pack quantity
  - Purchase quantity
  - Total value (quantity × pack)
  - Waktu request dibuat

## Navigasi

### Breadcrumb Navigation
- Menampilkan path navigasi saat ini
- Visual indicator untuk level aktif

### Back Button
- Tersedia di level 2 dan 3
- Ke<PERSON><PERSON> ke level sebelumnya

### Quick Actions
- **Create**: Buat purchase request baru
- **List View**: Kembali ke tampilan list tradisional
- **Back**: Navigasi mundur

## User Experience

### Progressive Disclosure
- Informasi ditampilkan bertahap sesuai kebutuhan
- Mengurangi cognitive load

### Visual Hierarchy
- Color coding untuk berbagai jenis informasi
- Badge dan icon untuk quick recognition

### Contextual Information
- Help text di setiap level
- Quick stats di header
- Breadcrumb untuk orientasi

## Technical Implementation

### Files Created/Modified

1. **`PurchaseRequestDashboard.php`** - Main dashboard page
2. **`purchase-request-dashboard.blade.php`** - View template
3. **`PurchaseRequestResource.php`** - Added dashboard route
4. **`ListPurchaseRequests.php`** - Added dashboard link

### Key Components

#### State Management
```php
public ?string $selectedDate = null;
public ?string $selectedOutlet = null;
public string $currentView = 'dates';
```

#### Dynamic Table Switching
```php
public function table(Table $table): Table
{
    return match ($this->currentView) {
        'dates' => $this->getDatesTable($table),
        'outlets' => $this->getOutletsTable($table),
        'products' => $this->getProductsTable($table),
    };
}
```

#### Optimized Queries
- Menggunakan aggregate functions untuk performance
- Conditional queries berdasarkan user role
- Efficient joins dan relationships

### Database Queries

#### Dates Level
```sql
SELECT request_date, 
       COUNT(*) as total_requests, 
       COUNT(DISTINCT outlet_id) as total_outlets
FROM purchase_requests 
GROUP BY request_date
ORDER BY request_date DESC
```

#### Outlets Level
```sql
SELECT outlet_id, 
       COUNT(*) as request_count
FROM purchase_requests 
WHERE request_date = ?
GROUP BY outlet_id
```

#### Products Level
```sql
SELECT * FROM purchase_request_details prd
JOIN purchase_requests pr ON prd.purchase_request_id = pr.id
WHERE pr.request_date = ? AND pr.outlet_id = ?
```

## Performance Optimizations

### 1. Query Optimization
- Aggregate functions untuk menghitung statistik
- Selective loading dengan `with()` dan `withCount()`
- Indexed columns untuk faster filtering

### 2. Caching Strategy
- Query results dapat di-cache untuk data yang jarang berubah
- Browser caching untuk static assets

### 3. Pagination
- Built-in Filament table pagination
- Configurable page sizes

## User Roles & Permissions

### Admin Users
- Hanya melihat data outlet mereka sendiri
- Automatic filtering berdasarkan `outlet_id`

### Manager Users  
- Melihat semua outlet
- Full access ke semua data

## URL Structure

```
/admin/purchase-requests/dashboard
```

### State Parameters
- Dashboard menggunakan component state untuk navigation
- No URL parameters needed (single page app approach)

## Future Enhancements

### 1. Export Functionality
- Export data per level (dates, outlets, products)
- Multiple formats (Excel, PDF, CSV)

### 2. Advanced Filtering
- Date range picker
- Outlet multi-select
- Product category filters

### 3. Real-time Updates
- Live updates when new requests created
- WebSocket integration for real-time data

### 4. Analytics Integration
- Purchase trends analysis
- Outlet performance metrics
- Product popularity insights

### 5. Mobile Optimization
- Responsive design improvements
- Touch-friendly navigation
- Mobile-specific UI patterns

## Usage Examples

### Scenario 1: Manager Review
1. Manager opens dashboard
2. Sees all purchase dates
3. Clicks on specific date
4. Reviews which outlets have requests
5. Drills down to specific outlet
6. Reviews product details

### Scenario 2: Admin Daily Check
1. Admin opens dashboard
2. Sees only their outlet's dates
3. Clicks on today's date
4. Reviews products requested
5. Verifies quantities and details

### Scenario 3: Quick Statistics
1. User opens dashboard
2. Gets overview of all purchase activity
3. Quick stats show total requests, outlets, products
4. No need to drill down for high-level view

## Benefits

### For Users
- **Intuitive Navigation**: Natural drill-down flow
- **Better Overview**: Hierarchical data presentation
- **Faster Access**: Direct navigation to relevant data
- **Reduced Clutter**: Progressive information disclosure

### For Business
- **Better Insights**: Clear view of purchase patterns
- **Improved Efficiency**: Faster data access and review
- **Enhanced Control**: Better oversight of purchase activities
- **Data-Driven Decisions**: Easy access to purchase analytics

## Testing Checklist

- [ ] Date level displays correctly with accurate statistics
- [ ] Outlet level shows proper filtering by date
- [ ] Product level displays complete purchase details
- [ ] Navigation between levels works smoothly
- [ ] Back button functions correctly
- [ ] User role permissions are enforced
- [ ] Performance is acceptable with large datasets
- [ ] Mobile responsiveness works well
- [ ] Error handling for edge cases
- [ ] Integration with existing purchase request features
