<?php

namespace App\Imports;

use App\Models\DailyStock;
use App\Models\Product;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class DailyStockImport implements ToModel, WithHeadingRow, WithValidation
{
    protected $outletId;
    protected $stockDate;
    protected $userId;

    public function __construct($outletId, $stockDate, $userId)
    {
        $this->outletId = $outletId;
        $this->stockDate = $stockDate;
        $this->userId = $userId;
    }

    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        // Find product by code or name
        $product = Product::where('code', $row['product_code'])
            ->orWhere('name', $row['product_name'])
            ->first();

        if (!$product) {
            // Skip if product not found
            return null;
        }

        return new DailyStock([
            'product_id' => $product->id,
            'outlet_id' => $this->outletId,
            'user_id' => $this->userId,
            'stock_date' => $this->stockDate,
            'quantity' => $row['quantity'] ?? 0,
            'sales_quantity' => $row['sales_quantity'] ?? 0,
            'incoming_quantity' => $row['incoming_quantity'] ?? 0,
            'notes' => $row['notes'] ?? null,
        ]);
    }

    public function rules(): array
    {
        return [
            'product_code' => 'required_without:product_name|string',
            'product_name' => 'required_without:product_code|string',
            'quantity' => 'required|numeric|min:0',
            'sales_quantity' => 'nullable|numeric|min:0',
            'incoming_quantity' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'product_code.required_without' => 'Product code is required when product name is not provided.',
            'product_name.required_without' => 'Product name is required when product code is not provided.',
            'quantity.required' => 'Quantity is required.',
            'quantity.numeric' => 'Quantity must be a number.',
            'quantity.min' => 'Quantity must be at least 0.',
        ];
    }
}
