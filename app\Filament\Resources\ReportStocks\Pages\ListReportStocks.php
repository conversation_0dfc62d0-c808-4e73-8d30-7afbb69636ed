<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Imports\ReportStockBulkImport;
use App\Exports\ReportStockTemplateExport;
use App\Models\Outlet;
use App\Models\ReportStock;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use App\Services\ReportStockPurchaseService;
use App\Services\ReportStockTemplateService;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class ListReportStocks extends ListRecords implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ReportStockResource::class;

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('report_date')
                    ->label('Report Date')
                    ->date('M j, Y')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->badge()
                    ->color('primary')
                    ->description(fn ($record) => $record->report_date->diffForHumans()),

                TextColumn::make('total_outlets')
                    ->label('Outlets')
                    ->badge()
                    ->color('success')
                    ->getStateUsing(function ($record) {
                        return number_format($record->getTotalOutlets());
                    })
                    ->description('Outlets with reports'),

                TextColumn::make('total_products')
                    ->label('Products')
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(function ($record) {
                        return number_format($record->getTotalProducts());
                    })
                    ->description('Total product entries'),

                TextColumn::make('total_quantity')
                    ->label('Total Quantity')
                    ->badge()
                    ->color('info')
                    ->getStateUsing(function ($record) {
                        return number_format($record->getTotalQuantity());
                    })
                    ->description('Sum of all quantities'),

                TextColumn::make('is_generated')
                    ->label('Status')
                    ->badge()
                    ->getStateUsing(fn ($record) => $record->is_generated ? 'Generated' : 'Manual')
                    ->color(fn ($record) => $record->is_generated ? 'success' : 'warning'),
            ])
            ->actions([
                Action::make('generate_purchase_request')
                    ->label('Generate Purchase Request')
                    ->icon('heroicon-o-shopping-cart')
                    ->color('success')
                    ->visible(fn ($record) => !$record->is_generated)
                    ->requiresConfirmation()
                    ->modalHeading('Generate Purchase Request')
                    ->modalDescription(fn ($record) => "Generate purchase request for products with stock ≤ min_buffer on {$record->report_date->format('M j, Y')}?")
                    ->modalSubmitActionLabel('Generate')
                    ->action(function ($record) {
                        $this->generatePurchaseRequest($record);
                    }),

                Action::make('view_outlets')
                    ->label('View Details')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(fn ($record) => static::$resource::getUrl('outlets', ['date' => $record->report_date->format('Y-m-d')])),
            ])
            ->recordAction('view_outlets')
            ->recordUrl(fn ($record) => static::$resource::getUrl('outlets', ['date' => $record->report_date->format('Y-m-d')]))
            ->defaultSort('report_date', 'desc')
            ->searchPlaceholder('Search report dates...')
            ->emptyStateHeading('No Report Dates Found')
            ->emptyStateDescription('No stock reports are available.')
            ->emptyStateIcon('heroicon-o-building-storefront')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    protected function getTableQuery(): Builder
    {
        $user = Auth::user();

        // Base query for report stocks (dates)
        $query = ReportStock::query();

        // For admin users, scope to their outlet only
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->whereHas('details', function ($q) use ($user) {
                $q->where('outlet_id', $user->outlet_id);
            });
        }

        return $query->orderBy('report_date', 'desc');
    }

    /**
     * Generate purchase request for a report stock
     */
    public function generatePurchaseRequest(ReportStock $reportStock): void
    {
        try {
            $service = new ReportStockPurchaseService();

            // Generate purchase request with min buffer logic
            $results = $service->generatePurchaseRequest($reportStock, [
                'force_regenerate' => true
            ]);

            if ($results['success']) {
                // Mark report as generated
                $reportStock->update(['is_generated' => true]);

                $message = "Purchase request generated successfully!\n";
                $message .= "• Total Outlets: {$results['total_outlets']}\n";
                $message .= "• Purchase Requests Created: " . count($results['purchase_requests']) . "\n";
                $message .= "• Total Products: {$results['total_products']}";

                Notification::make()
                    ->title('Purchase Request Generated')
                    ->body($message)
                    ->success()
                    ->duration(5000)
                    ->send();

                // Refresh the table
                $this->dispatch('$refresh');

            } else {
                $errorMessage = "Failed to generate purchase request";
                if (!empty($results['errors'])) {
                    $errorMessage .= ":\n" . implode("\n", $results['errors']);
                }

                Notification::make()
                    ->title('Generation Failed')
                    ->body($errorMessage)
                    ->danger()
                    ->duration(8000)
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body("An error occurred: {$e->getMessage()}")
                ->danger()
                ->duration(8000)
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('download_template')
                ->label('Download Template')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action(function () {
                    return Excel::download(new ReportStockTemplateExport(), 'report_stock_template.xlsx');
                }),

            Action::make('download_template')
                ->label('Download Template')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action(function () {
                    $templateService = new ReportStockTemplateService();
                    $templatePath = $templateService->generateTemplateWithData();

                    return response()->download($templatePath, 'report_stock_template.xlsx')->deleteFileAfterSend();
                }),

            Action::make('import_report_stocks')
                ->label('Import Report Stocks')
                ->icon('heroicon-o-document-arrow-up')
                ->color('success')
                ->modalHeading('Import Report Stocks')
                ->modalDescription('Upload an Excel file to import stock reports. Products will be created if they don\'t exist.')
                ->modalSubmitActionLabel('Import')
                ->modalWidth('lg')
                ->fillForm(fn (): array => [
                    'report_date' => now()->toDateString(),
                ])
                ->form([
                    DatePicker::make('report_date')
                        ->label('Report Date')
                        ->required()
                        ->default(now())
                        ->helperText('All imported stock reports will use this date.'),

                    FileUpload::make('excel_file')
                        ->label('Excel File')
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'])
                        ->required()
                        ->disk('public')
                        ->directory('imports')
                        ->helperText('Upload Excel file with stock report data. Download template first for correct format.'),
                ])
                ->action(function (array $data) {
                    $this->processImport($data['excel_file'], $data['report_date']);
                }),

            CreateAction::make(),

            Action::make('generate_purchase_requests_page')
                ->label('Generate Purchase Requests')
                ->icon('heroicon-o-shopping-cart')
                ->color('success')
                ->url(fn (): string => static::$resource::getUrl('generate-purchase-request')),


        ];
    }

    public function getTitle(): string
    {
        return 'Stock Reports by Outlet';
    }

    public function getHeading(): string
    {
        return 'Stock Reports by Outlet';
    }

    public function getSubheading(): ?string
    {
        return 'Choose an outlet to view stock report dates and details.';
    }

    protected function processImport(string $filePath, string $reportDate): void
    {
        try {
            // Get the full file path
            $fullPath = Storage::disk('public')->path($filePath);

            if (!file_exists($fullPath)) {
                Notification::make()
                    ->title('File Not Found')
                    ->body('The uploaded file could not be found.')
                    ->danger()
                    ->send();
                return;
            }

            // Process the import
            $import = new ReportStockBulkImport($reportDate);
            Excel::import($import, $fullPath);

            // Get statistics
            $processedRows = $import->getProcessedRows();
            $productsCreated = $import->getProductsCreated();
            $errors = $import->getErrors();

            // Get additional statistics
            $outletProductsCreated = $import->getOutletProductsCreated();
            $outletProductsUpdated = $import->getOutletProductsUpdated();
            $reportStockDetailsCreated = $import->getReportStockDetailsCreated();
            $reportStockDetailsUpdated = $import->getReportStockDetailsUpdated();
            $skippedOutlets = $import->getSkippedOutlets();

            // Show success notification
            $message = "Import completed successfully!<br>";
            $message .= "• Report Date: {$reportDate}<br>";
            $message .= "• Processed rows: {$processedRows}<br>";
            $message .= "• Products created: {$productsCreated}<br>";
            $message .= "• Outlet products created: {$outletProductsCreated}<br>";
            $message .= "• Outlet products updated: {$outletProductsUpdated}<br>";
            $message .= "• Report stock details created: {$reportStockDetailsCreated}<br>";
            $message .= "• Report stock details updated: {$reportStockDetailsUpdated}";

            if (!empty($skippedOutlets)) {
                $message .= "<br><br>Skipped outlets (not found): " . implode(', ', array_slice($skippedOutlets, 0, 10));
                if (count($skippedOutlets) > 10) {
                    $message .= " and " . (count($skippedOutlets) - 10) . " more";
                }
            }

            if (!empty($errors)) {
                $message .= "<br><br>Errors encountered:<br>";
                foreach (array_slice($errors, 0, 5) as $error) {
                    $message .= "• Row {$error['row']}: {$error['error']}<br>";
                }
                if (count($errors) > 5) {
                    $message .= "... and " . (count($errors) - 5) . " more errors.";
                }
            }

            Notification::make()
                ->title('Import Completed')
                ->body($message)
                ->success()
                ->persistent()
                ->send();

            // Clean up the uploaded file
            Storage::disk('public')->delete($filePath);

            // Refresh the page to show new data
            $this->redirect(static::getUrl());

        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body('An error occurred during import: ' . $e->getMessage())
                ->danger()
                ->persistent()
                ->send();
        }
    }
}
