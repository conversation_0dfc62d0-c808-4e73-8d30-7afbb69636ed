<?php

namespace App\Filament\Resources\PurchaseRequests\Pages;

use App\Filament\Resources\PurchaseRequests\PurchaseRequestResource;
use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestDetail;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class CreatePurchaseRequest extends CreateRecord
{
    protected static string $resource = PurchaseRequestResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Purchase Request Created')
            ->body('The purchase request has been created successfully. You can now add products to it.');
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Extract purchase details from the form data
        $purchaseDetails = $data['purchase_details'] ?? [];
        $outletId = $data['outlet_id'] ?? null;
        unset($data['purchase_details']);
        unset($data['outlet_id']);

        // Create the purchase request
        $record = static::getModel()::create($data);

        // Create purchase request details if any
        if (!empty($purchaseDetails) && $outletId) {
            foreach ($purchaseDetails as $detail) {
                if (isset($detail['product_id']) && isset($detail['purchase_quantity'])) {
                    PurchaseRequestDetail::create([
                        'purchase_request_id' => $record->id,
                        'outlet_id' => $outletId,
                        'product_id' => $detail['product_id'],
                        'purchase_quantity' => $detail['purchase_quantity'],
                    ]);
                }
            }
        }

        return $record;
    }
}
