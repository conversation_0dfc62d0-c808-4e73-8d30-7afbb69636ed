<div class="overflow-hidden bg-white shadow-sm rounded-lg dark:bg-gray-800">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            Import History ({{ $importHistories->count() }} imports)
        </h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        File Name
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Imported By
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        File Size
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Rows
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Success Rate
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Duration
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Started At
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                @foreach($importHistories as $history)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ $history->filename }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $history->user->name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $history->file_size }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div class="flex flex-col">
                            <span class="text-green-600 dark:text-green-400">✓ {{ $history->processed_rows }} processed</span>
                            @if($history->error_rows > 0)
                                <span class="text-red-600 dark:text-red-400">✗ {{ $history->error_rows }} errors</span>
                            @endif
                            @if($history->skipped_rows > 0)
                                <span class="text-yellow-600 dark:text-yellow-400">⚠ {{ $history->skipped_rows }} skipped</span>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        @php
                            $successRate = $history->success_rate;
                            $colorClass = $successRate >= 90 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 
                                         ($successRate >= 70 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 
                                          'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100');
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $colorClass }}">
                            {{ number_format($successRate, 1) }}%
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        @php
                            $statusColors = [
                                'pending' => 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100',
                                'processing' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
                                'completed' => 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
                                'failed' => 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
                            ];
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$history->status] ?? $statusColors['pending'] }}">
                            {{ ucfirst($history->status) }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $history->duration ?? 'N/A' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $history->started_at?->format('M d, Y H:i') ?? 'N/A' }}
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    
    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 dark:bg-gray-900 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Total Imports: <span class="font-medium">{{ $importHistories->count() }}</span>
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Last Import: {{ $importHistories->first()?->started_at?->format('M d, Y H:i') ?? 'Never' }}
            </div>
        </div>
    </div>
</div>
