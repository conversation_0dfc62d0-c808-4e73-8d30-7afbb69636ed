# Report Stock Navigation Flow Documentation

## Overview
Sistem navigasi report stock telah didesain ulang dengan alur bertingkat yang intuitif untuk memudahkan pengguna dalam mengakses dan menganalisis data stok berdasarkan outlet dan tanggal tertentu.

## 🔄 Alur Navigasi Baru

### **Level 1: Outlet Selection**
**URL**: `/admin/report-stocks/outlets`

#### **Fitur:**
- **Daftar Outlet**: Menampilkan semua outlet yang memiliki laporan stok
- **Statistik per Outlet**:
  - Total Reports: Jumlah total laporan stok
  - Latest Report: Tanggal laporan terbaru
  - Products: Jumlah produk unik yang dilaporkan
- **Search & Filter**: Pencarian outlet berdasarkan nama atau kode
- **Role-based Access**: Admin hanya melihat outlet mereka sendiri

#### **Informasi yang Ditampilkan:**
- Kode outlet (dapat dicopy)
- Nama outlet dengan deskripsi kode
- Badge total laporan dengan warna primary
- Badge tanggal laporan terbaru dengan warna success
- Badge jumlah produk unik dengan warna warning

#### **Navigasi:**
- **Klik outlet** → Menuju ke Date Selection untuk outlet tersebut
- **Breadcrumb**: `Select Outlet` → Select Date → View Details

---

### **Level 2: Date Selection**
**URL**: `/admin/report-stocks/outlets/{outlet_id}/dates`

#### **Fitur:**
- **Header Outlet**: Informasi detail outlet yang dipilih
- **Summary Statistics**: Ringkasan data outlet
  - Total Report Dates
  - Unique Products
  - Total Stock
  - Latest Report
- **Daftar Tanggal**: Semua tanggal yang memiliki laporan stok
- **Statistik per Tanggal**:
  - Products Reported: Jumlah produk yang dilaporkan
  - Total Stock: Total kuantitas stok
  - Out of Stock: Produk dengan stok 0
  - Low Stock: Produk di bawah minimum buffer

#### **Informasi yang Ditampilkan:**
- Tanggal laporan (format: "Monday, Jan 15, 2024")
- Deskripsi relatif waktu (e.g., "2 days ago")
- Badge jumlah produk dengan warna primary
- Badge total stok dengan warna success
- Badge out of stock dengan warna danger
- Badge low stock dengan warna warning
- Waktu pembuatan laporan

#### **Navigasi:**
- **Back to Outlets**: Kembali ke outlet selection
- **Klik tanggal** → Menuju ke Stock Details untuk outlet dan tanggal tersebut
- **Breadcrumb**: Select Outlet → `Select Date` → View Details

---

### **Level 3: Stock Details**
**URL**: `/admin/report-stocks/outlets/{outlet_id}/dates/{date}/details`

#### **Fitur:**
- **Header Detail**: Informasi outlet dan tanggal yang dipilih
- **Summary Dashboard**: 5 widget statistik
  - Total Products
  - Total Stock
  - Out of Stock
  - Low Stock
  - Good Stock
- **Tabel Detail Produk**: Informasi lengkap setiap produk
- **Advanced Filters**:
  - Stock Status (Out of Stock, Low Stock, Warning, Good Stock)
  - Pareto Category (FM, SM, BM)

#### **Kolom Tabel:**
1. **Product**: Nama produk + barcode + unit
2. **Stock Quantity**: Kuantitas dengan color coding berdasarkan buffer
3. **Pareto**: Kategori pareto dengan badge berwarna
4. **Status**: Status stok (Out of Stock, Low Stock, Warning, Good Stock)
5. **Pack Size**: Jumlah item per kemasan
6. **Reported At**: Waktu pembuatan laporan

#### **Color Coding:**
- **Quantity**:
  - 🔘 Gray: Out of stock (quantity = 0)
  - 🔴 Red: Low stock (quantity < min_buffer)
  - 🟡 Yellow: Warning (quantity ≤ min_buffer × 2)
  - 🟢 Green: Good stock (quantity > min_buffer × 2)

- **Pareto**:
  - 🟢 Green: Fast Moving (FM)
  - 🟡 Yellow: Slow Moving (SM)
  - 🔴 Red: Bad Moving (BM)
  - 🔘 Gray: Not classified

#### **Navigasi:**
- **Back to Dates**: Kembali ke date selection untuk outlet yang sama
- **Back to Outlets**: Kembali ke outlet selection
- **Breadcrumb**: Select Outlet → Select Date → `View Details`

---

## 🎯 User Experience Improvements

### **1. Visual Design**
- **Consistent Color Scheme**: Setiap level menggunakan warna tema yang berbeda
  - Level 1 (Outlets): Blue theme
  - Level 2 (Dates): Green theme  
  - Level 3 (Details): Purple theme
- **Icon Integration**: Setiap level memiliki icon yang representatif
- **Card Layout**: Informasi disajikan dalam card yang clean dan modern

### **2. Navigation Elements**
- **Breadcrumb Navigation**: Selalu menunjukkan posisi user dalam alur
- **Back Buttons**: Multiple opsi untuk kembali ke level sebelumnya
- **Clickable Elements**: Jelas menunjukkan elemen yang dapat diklik

### **3. Information Architecture**
- **Progressive Disclosure**: Informasi ditampilkan secara bertahap
- **Context Preservation**: Informasi outlet dan tanggal selalu terlihat
- **Summary Statistics**: Ringkasan data di setiap level

### **4. Responsive Design**
- **Grid Layout**: Menggunakan responsive grid untuk berbagai ukuran layar
- **Mobile Friendly**: Optimized untuk penggunaan di mobile device
- **Touch Targets**: Ukuran tombol yang sesuai untuk touch interface

---

## 🔐 Access Control

### **Role-based Filtering**
- **Admin Users**: Hanya dapat melihat outlet mereka sendiri
- **Manager Users**: Dapat melihat semua outlet
- **Permission Check**: Setiap halaman memvalidasi permission user

### **Data Scoping**
- **Outlet Level**: Filter otomatis berdasarkan role user
- **Date Level**: Hanya menampilkan tanggal yang memiliki data
- **Product Level**: Hanya menampilkan produk yang ada di laporan

---

## 🚀 Performance Optimizations

### **1. Database Queries**
- **Eager Loading**: Pre-load relationships untuk mengurangi N+1 queries
- **Aggregation**: Menggunakan database aggregation untuk statistik
- **Indexing**: Optimal indexing pada kolom yang sering diquery

### **2. Caching Strategy**
- **Query Caching**: Cache hasil query yang sering diakses
- **View Caching**: Cache komponen view yang statis
- **Session Storage**: Simpan context navigation di session

### **3. Pagination**
- **Configurable Page Size**: User dapat memilih jumlah item per halaman
- **Lazy Loading**: Load data sesuai kebutuhan
- **Search Optimization**: Efficient search dengan database indexing

---

## 📱 Mobile Experience

### **Touch-Friendly Interface**
- **Large Touch Targets**: Minimum 44px untuk semua clickable elements
- **Swipe Gestures**: Support swipe untuk navigasi back
- **Responsive Tables**: Horizontal scroll untuk tabel di mobile

### **Progressive Web App Features**
- **Offline Support**: Cache data untuk akses offline
- **Push Notifications**: Notifikasi untuk laporan stok baru
- **Home Screen Install**: Dapat diinstall sebagai PWA

---

## 🔧 Technical Implementation

### **Route Structure**
```
/admin/report-stocks/outlets                           # Outlet Selection
/admin/report-stocks/outlets/{outlet}/dates            # Date Selection  
/admin/report-stocks/outlets/{outlet}/dates/{date}/details # Stock Details
```

### **Page Classes**
- `OutletSelection.php`: Halaman pemilihan outlet
- `DateSelection.php`: Halaman pemilihan tanggal
- `StockDetails.php`: Halaman detail stok

### **View Templates**
- `outlet-selection.blade.php`: Template outlet selection
- `date-selection.blade.php`: Template date selection
- `stock-details.blade.php`: Template stock details

### **Navigation Integration**
- Terintegrasi dengan Filament navigation system
- Support untuk breadcrumb dan back navigation
- URL-based navigation untuk bookmarking

---

## 📊 Analytics & Insights

### **Usage Tracking**
- **Page Views**: Track halaman yang paling sering diakses
- **Navigation Patterns**: Analisis pola navigasi user
- **Performance Metrics**: Monitor loading time setiap halaman

### **Business Intelligence**
- **Stock Analysis**: Insight tentang pola stok per outlet
- **Trend Analysis**: Analisis trend stok dari waktu ke waktu
- **Alert System**: Notifikasi untuk stok kritis

Alur navigasi baru ini memberikan pengalaman yang lebih terstruktur dan intuitif untuk menganalisis data stok berdasarkan outlet dan tanggal tertentu.
