<?php

namespace App\Filament\Resources\PurchaseRequests\Pages;

use App\Filament\Resources\PurchaseRequests\PurchaseRequestResource;
use App\Models\PurchaseRequest;
use App\Models\Outlet;
use App\Models\Product;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;

class PurchaseRequestDashboard extends Page implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    protected static string $resource = PurchaseRequestResource::class;
    
    public ?string $selectedDate = null;
    public ?string $selectedOutlet = null;
    public string $currentView = 'dates'; // dates, outlets, products

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->url(PurchaseRequestResource::getUrl('create')),
            Action::make('list_view')
                ->label('List View')
                ->icon('heroicon-o-list-bullet')
                ->color('info')
                ->url(PurchaseRequestResource::getUrl('index')),
            Action::make('back')
                ->label('Back')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->visible(fn () => $this->currentView !== 'dates')
                ->action(function () {
                    if ($this->currentView === 'products') {
                        $this->currentView = 'outlets';
                        $this->selectedOutlet = null;
                    } elseif ($this->currentView === 'outlets') {
                        $this->currentView = 'dates';
                        $this->selectedDate = null;
                    }
                    $this->dispatch('$refresh');
                }),
        ];
    }

    public function table(Table $table): Table
    {
        return match ($this->currentView) {
            'dates' => $this->getDatesTable($table),
            'outlets' => $this->getOutletsTable($table),
            'products' => $this->getProductsTable($table),
            default => $this->getDatesTable($table),
        };
    }

    protected function getDatesTable(Table $table): Table
    {
        return $table
            ->query(PurchaseRequest::query()->orderBy('purchase_request_date', 'desc'))
            ->columns([
                TextColumn::make('purchase_request_date')
                    ->label('Purchase Request Date')
                    ->date('l, F j, Y')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('primary'),

                TextColumn::make('total_outlets')
                    ->label('Outlets')
                    ->numeric()
                    ->badge()
                    ->color('info')
                    ->getStateUsing(fn ($record) => $record->getTotalOutlets())
                    ->description('Number of different outlets'),

                TextColumn::make('total_products')
                    ->label('Total Products')
                    ->numeric()
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(fn ($record) => $record->getTotalProducts())
                    ->description('Total products across all requests'),

                TextColumn::make('total_quantity')
                    ->label('Total Quantity')
                    ->numeric()
                    ->badge()
                    ->color('success')
                    ->getStateUsing(fn ($record) => $record->getTotalQuantity())
                    ->description('Total purchase quantity'),
            ])
            ->recordAction(
                fn ($record) => Action::make('view_outlets')
                    ->label('View Outlets')
                    ->icon('heroicon-o-building-storefront')
                    ->action(function () use ($record) {
                        $this->selectedDate = $record->purchase_request_date;
                        $this->currentView = 'outlets';
                        $this->dispatch('$refresh');
                    })
            )
            ->heading('Purchase Request Dates')
            ->description('Select a date to view outlets with purchase requests')
            ->emptyStateHeading('No purchase requests found')
            ->emptyStateDescription('Create your first purchase request to get started.')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    protected function getOutletsTable(Table $table): Table
    {
        return $table
            ->query(
                \App\Models\PurchaseRequestDetail::query()
                    ->whereHas('purchaseRequest', function ($query) {
                        $query->where('purchase_request_date', $this->selectedDate);
                    })
                    ->when(Auth::user()?->hasRole('admin') && Auth::user()->outlet_id, function ($query) {
                        $query->where('outlet_id', Auth::user()->outlet_id);
                    })
                    ->with(['outlet'])
            )
            ->columns([
                TextColumn::make('outlet.name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->color('primary'),

                TextColumn::make('outlet.code')
                    ->label('Outlet Code')
                    ->badge()
                    ->color('gray'),

                TextColumn::make('purchase_quantity')
                    ->label('Purchase Quantity')
                    ->numeric()
                    ->badge()
                    ->color('warning')
                    ->description('Quantity to purchase'),
            ])
            ->recordAction(
                fn ($record) => Action::make('view_products')
                    ->label('View Products')
                    ->icon('heroicon-o-shopping-cart')
                    ->action(function () use ($record) {
                        $this->selectedOutlet = $record->outlet_id;
                        $this->currentView = 'products';
                        $this->dispatch('$refresh');
                    })
            )
            ->heading("Outlets for " . \Carbon\Carbon::parse($this->selectedDate)->format('l, F j, Y'))
            ->description('Select an outlet to view products in purchase requests')
            ->emptyStateHeading('No outlets found')
            ->emptyStateDescription('No purchase requests found for this date.')
            ->emptyStateIcon('heroicon-o-building-storefront');
    }

    protected function getProductsTable(Table $table): Table
    {
        $outlet = Outlet::find($this->selectedOutlet);
        
        return $table
            ->query(
                \App\Models\PurchaseRequestDetail::query()
                    ->whereHas('purchaseRequest', function ($query) {
                        $query->where('purchase_request_date', $this->selectedDate);
                    })
                    ->where('outlet_id', $this->selectedOutlet)
                    ->with(['product', 'purchaseRequest'])
            )
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->color('primary'),

                TextColumn::make('product.barcode')
                    ->label('Barcode')
                    ->searchable()
                    ->copyable()
                    ->badge()
                    ->color('gray'),

                TextColumn::make('product.unit')
                    ->label('Unit')
                    ->badge()
                    ->color('info'),

                TextColumn::make('product.pack_quantity')
                    ->label('Pack Qty')
                    ->numeric()
                    ->badge()
                    ->color('secondary'),

                TextColumn::make('purchase_quantity')
                    ->label('Purchase Quantity')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('success')
                    ->weight('bold'),

                TextColumn::make('total_value')
                    ->label('Total Value')
                    ->numeric()
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(fn ($record) => $record->purchase_quantity * $record->product->pack_quantity)
                    ->description('Quantity × Pack Quantity'),

                TextColumn::make('purchaseRequest.created_at')
                    ->label('Requested At')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->description(fn ($record) => $record->purchaseRequest->created_at->diffForHumans()),
            ])
            ->heading("Products for {$outlet?->name} on " . \Carbon\Carbon::parse($this->selectedDate)->format('l, F j, Y'))
            ->description('Purchase request details for the selected outlet and date')
            ->emptyStateHeading('No products found')
            ->emptyStateDescription('No products in purchase requests for this outlet and date.')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }

    public function getTitle(): string
    {
        return match ($this->currentView) {
            'dates' => 'Purchase Request Dashboard',
            'outlets' => 'Outlets - ' . \Carbon\Carbon::parse($this->selectedDate)->format('M j, Y'),
            'products' => 'Products - ' . Outlet::find($this->selectedOutlet)?->name,
            default => 'Purchase Request Dashboard',
        };
    }

    public function getSubheading(): ?string
    {
        return match ($this->currentView) {
            'dates' => 'Select a date to view outlets with purchase requests',
            'outlets' => 'Select an outlet to view products in purchase requests',
            'products' => 'Purchase request details for the selected outlet and date',
            default => null,
        };
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add breadcrumb widget here if needed
        ];
    }

    public function getBreadcrumb(): string
    {
        $breadcrumb = 'Purchase Dates';

        if ($this->currentView !== 'dates') {
            $breadcrumb .= ' → ' . \Carbon\Carbon::parse($this->selectedDate)->format('M j, Y');
        }

        if ($this->currentView === 'products') {
            $breadcrumb .= ' → ' . Outlet::find($this->selectedOutlet)?->name;
        }

        return $breadcrumb;
    }
}
