<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use Illuminate\Support\Collection;

echo "Simple test starting...\n";

// Create test outlet
$outlet = Outlet::firstOrCreate(['code' => 'SIMPLE001'], ['name' => 'Simple Test Outlet']);

// Create test data
$testData = collect([
    [
        'outlet' => 'SIMPLE001',
        'nama_produk' => 'Simple Test Product',
        'prt' => 'A',
        'barcode' => 'SIMPLE_BARCODE_001',
        'pack' => '1',
        'qty' => 100,
        'sat' => 'PCS',
    ],
]);

echo "Test data created\n";

try {
    $import = new ReportStockBulkImport('2025-09-12');
    $import->collection($testData);
    echo "✅ Import successful!\n";
    
    $summary = $import->getImportSummary();
    echo "Products created: " . $summary['products_created'] . "\n";
    echo "Errors: " . $summary['errors_count'] . "\n";
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
}

echo "Test completed\n";
