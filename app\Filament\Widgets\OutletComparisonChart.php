<?php

namespace App\Filament\Widgets;

use App\Models\Outlet;
use App\Models\OutletProduct;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class OutletComparisonChart extends ChartWidget
{
    protected static ?int $sort = 6;

    public function getHeading(): ?string
    {
        return 'Outlet Comparison';
    }

    protected function getData(): array
    {
        // Only show for managers
        if (!Auth::user()?->hasRole('manager')) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $outlets = Outlet::all();

        $productCounts = [];
        $purchaseRequestCounts = [];
        $stockReportCounts = [];
        $outletNames = [];

        foreach ($outlets as $outlet) {
            $outletNames[] = $outlet->name;
            $productCounts[] = OutletProduct::where('outlet_id', $outlet->id)->count();
            $purchaseRequestCounts[] = $outlet->purchaseRequestDetails()->count();
            $stockReportCounts[] = $outlet->reportStockDetails()->count();
        }

        return [
            'datasets' => [
                [
                    'label' => 'Products',
                    'data' => $productCounts,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.5)',
                    'borderColor' => '#3b82f6',
                    'borderWidth' => 2,
                ],
                [
                    'label' => 'Purchase Requests',
                    'data' => $purchaseRequestCounts,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.5)',
                    'borderColor' => '#10b981',
                    'borderWidth' => 2,
                ],
                [
                    'label' => 'Stock Reports',
                    'data' => $stockReportCounts,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.5)',
                    'borderColor' => '#f59e0b',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $outletNames,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
            ],
            'maintainAspectRatio' => false,
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole('manager') ?? false;
    }
}
