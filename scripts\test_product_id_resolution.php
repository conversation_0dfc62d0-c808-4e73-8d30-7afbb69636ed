<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use Illuminate\Support\Facades\Log;

echo "=== Testing Product ID Resolution ===\n\n";

// Create test outlet
$outlet = Outlet::firstOrCreate(['code' => 'RESOLUTION001'], ['name' => 'Resolution Test Outlet']);
echo "Using outlet: {$outlet->code} (ID: {$outlet->id})\n\n";

// Clean up any existing test data
Product::where('barcode', 'RESOLUTION_BARCODE_001')->delete();

// Create test data
$testRows = collect([
    [
        'outlet' => 'RESOLUTION001',
        'nama_produk' => 'Resolution Test Product',
        'prt' => 'A',
        'barcode' => 'RESOLUTION_BARCODE_001',
        'pack' => '1',
        'qty' => 100,
        'sat' => 'PCS',
    ],
]);

echo "Test data prepared\n\n";

// Capture log messages
$logMessages = [];
Log::listen(function ($level, $message, $context) use (&$logMessages) {
    $logMessages[] = [
        'level' => $level,
        'message' => $message,
        'context' => $context,
    ];
});

try {
    $import = new ReportStockBulkImport('2025-09-12');
    $import->collection($testRows);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Show import statistics
    $summary = $import->getImportSummary();
    echo "Import Statistics:\n";
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "  {$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
}

// Show relevant log messages
echo "\nRelevant Log Messages:\n";
foreach ($logMessages as $log) {
    if (in_array($log['level'], ['debug', 'info', 'error']) && 
        (strpos($log['message'], 'product') !== false || 
         strpos($log['message'], 'outlet') !== false ||
         strpos($log['message'], 'Resolving') !== false ||
         strpos($log['message'], 'Invalid') !== false)) {
        
        echo "  [{$log['level']}] {$log['message']}\n";
        if (!empty($log['context'])) {
            $contextStr = json_encode($log['context'], JSON_UNESCAPED_SLASHES);
            if (strlen($contextStr) > 200) {
                $contextStr = substr($contextStr, 0, 200) . '...';
            }
            echo "    Context: {$contextStr}\n";
        }
    }
}

// Check what was actually created
echo "\nDatabase Check:\n";
$product = Product::where('barcode', 'RESOLUTION_BARCODE_001')->first();
if ($product) {
    echo "  ✅ Product created: ID {$product->id}, Barcode: {$product->barcode}\n";
} else {
    echo "  ❌ Product not found in database\n";
}

// Clean up
echo "\nCleaning up...\n";
Product::where('barcode', 'RESOLUTION_BARCODE_001')->delete();
if ($outlet->code === 'RESOLUTION001' && $outlet->outletProducts()->count() === 0) {
    $outlet->delete();
}
echo "✅ Cleanup completed\n";

echo "\n=== Test Completed ===\n";
