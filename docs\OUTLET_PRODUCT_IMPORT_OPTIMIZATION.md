# Outlet Product Import Optimization

## Overview
Optimasi untuk import data outlet product dalam jumlah besar (puluhan ribu records) dengan performa tinggi.

## Key Optimizations

### 1. **Batch Processing**
- Menggunakan `ToCollection` instead of `ToModel` untuk batch processing
- Chunk size: 1000 rows per chunk
- Batch size: 1000 records per batch insert

### 2. **Data Caching**
- Pre-load semua outlets, products, dan outlet products ke memory
- Menghindari N+1 query problem
- Cache lookup untuk validasi data

### 3. **Database Optimizations**
- Disable foreign key checks selama import
- Disable query logging
- Menggunakan database transactions
- Bulk insert operations

### 4. **Memory Management**
- Increased memory limit (512MB default)
- Extended execution time (5 minutes default)
- Memory usage monitoring

## Configuration

### Environment Variables
```env
# Outlet Products Import
IMPORT_OUTLET_PRODUCTS_CHUNK_SIZE=1000
IMPORT_OUTLET_PRODUCTS_BATCH_SIZE=1000
IMPORT_OUTLET_PRODUCTS_MEMORY_LIMIT=512M
IMPORT_OUTLET_PRODUCTS_MAX_TIME=300
IMPORT_OUTLET_PRODUCTS_LOGGING=false

# Database Optimization
IMPORT_DISABLE_FK_CHECKS=true
IMPORT_DISABLE_QUERY_LOG=true
IMPORT_USE_TRANSACTIONS=true
```

### Config File: `config/import.php`
```php
'outlet_products' => [
    'chunk_size' => env('IMPORT_OUTLET_PRODUCTS_CHUNK_SIZE', 1000),
    'batch_size' => env('IMPORT_OUTLET_PRODUCTS_BATCH_SIZE', 1000),
    'memory_limit' => env('IMPORT_OUTLET_PRODUCTS_MEMORY_LIMIT', '512M'),
    'max_execution_time' => env('IMPORT_OUTLET_PRODUCTS_MAX_TIME', 300),
    'enable_logging' => env('IMPORT_OUTLET_PRODUCTS_LOGGING', false),
],
```

## Usage

### Basic Import
```php
use App\Imports\OutletProductImport;
use Maatwebsite\Excel\Facades\Excel;

$import = new OutletProductImport();
Excel::import($import, 'outlet_products.xlsx');

// Get statistics
echo "Processed: " . $import->getProcessedRows();
echo "Products created: " . $import->getProductsCreated();
echo "Outlet products created: " . $import->getOutletProductsCreated();
echo "Errors: " . count($import->getErrors());
```

### With Optimization Service
```php
use App\Services\ImportOptimizationService;

$optimizationService = new ImportOptimizationService();

$result = $optimizationService->executeOptimizedImport(function() {
    $import = new OutletProductImport();
    Excel::import($import, 'large_file.xlsx');
    return $import;
}, 'outlet_products');
```

## Performance Benchmarks

### Expected Performance (on typical server):
- **1,000 rows**: ~2-3 seconds
- **5,000 rows**: ~8-12 seconds  
- **10,000 rows**: ~15-25 seconds
- **25,000 rows**: ~40-60 seconds
- **50,000 rows**: ~80-120 seconds

### Memory Usage:
- Base memory: ~50MB
- Per 1000 rows: ~5-10MB additional
- Peak for 50K rows: ~300-400MB

## File Format

### Required Columns:
- `outlet_code`: Outlet identifier (must exist in outlets table)
- `barcode`: Product barcode (unique identifier)
- `product_name`: Product name
- `unit`: Product unit (optional, default: 'Pcs')
- `pack_quantity`: Pack quantity (optional, default: 1)
- `outlet_pareto`: Outlet pareto category (FM/SM/BM, optional, default: 'FM')
- `rumus_pareto`: Formula pareto (FM/SM/BM, optional, default: 'FM')
- `min_buffer`: Minimum buffer stock (optional, default: 10)
- `max_buffer`: Maximum buffer stock (optional, default: 50)

### Example CSV:
```csv
outlet_code,barcode,product_name,unit,pack_quantity,outlet_pareto,rumus_pareto,min_buffer,max_buffer
AK01,PAR500001,Paracetamol 500mg,tablet,10,FM,FM,25,100
AK01,AMX500001,Amoxicillin 500mg,capsule,1,SM,SM,15,75
```

## Error Handling

### Common Errors:
1. **Outlet not found**: Outlet code doesn't exist in database
2. **Invalid data**: Missing required fields or invalid format
3. **Memory limit**: File too large for current memory settings
4. **Timeout**: Import takes longer than max execution time

### Error Logging:
- Errors are collected in `$import->getErrors()`
- Detailed logging available in Laravel logs
- Progress logging shows memory usage and timing

## Monitoring

### Progress Logging:
```php
// Enable detailed logging
config(['import.outlet_products.enable_logging' => true]);

// Check logs for progress updates
tail -f storage/logs/laravel.log | grep "Import progress"
```

### Memory Monitoring:
```php
$optimizationService = new ImportOptimizationService();
$memoryUsage = $optimizationService->getMemoryUsage();

echo "Current: " . $memoryUsage['current_formatted'];
echo "Peak: " . $memoryUsage['peak_formatted'];
```

## Testing

### Performance Test:
```bash
php test_outlet_product_import_performance.php
```

This will test import performance with different data sizes and provide benchmarks.

## Troubleshooting

### Memory Issues:
1. Increase `IMPORT_OUTLET_PRODUCTS_MEMORY_LIMIT`
2. Reduce chunk/batch sizes
3. Process file in smaller parts

### Timeout Issues:
1. Increase `IMPORT_OUTLET_PRODUCTS_MAX_TIME`
2. Reduce chunk/batch sizes
3. Use queue-based processing for very large files

### Performance Issues:
1. Check database indexes on `outlets.code` and `products.barcode`
2. Ensure adequate server resources
3. Consider using SSD storage for temporary files
4. Monitor database connection pool

## Best Practices

1. **Validate data before import**: Check file format and required outlets exist
2. **Backup before large imports**: Always backup database before major imports
3. **Monitor resources**: Watch memory and CPU usage during import
4. **Use staging environment**: Test large imports on staging first
5. **Schedule during off-peak**: Run large imports during low-traffic periods
