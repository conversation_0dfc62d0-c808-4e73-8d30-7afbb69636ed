<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use Laravel\Dusk\Browser;
use App\Models\User;
use App\Models\Outlet;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class AuthorizationTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'OutletSeeder']);
        $this->artisan('db:seed', ['--class' => 'UserSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    public function test_admin_can_only_see_their_outlet_data()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            $adminOutlet = $admin->outlet;
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee($adminOutlet->name) // Should see their outlet
                    ->assertDontSee('All Outlets'); // Should not see outlet filter for admin
        });
    }

    public function test_manager_can_see_all_outlets_data()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/products')
                    ->assertSee('Outlet') // Should see outlet column
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Outlet'); // Should see outlet information
        });
    }

    public function test_admin_cannot_access_manager_only_features()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/purchase-requests')
                    ->assertPathIs('/admin/purchase-requests') // Should be redirected or see 403
                    ->assertDontSee('New purchase request'); // Admin shouldn't see purchase request creation
        });
    }

    public function test_manager_can_access_all_resources()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/products')
                    ->assertSee('Products')
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Purchase Requests')
                    ->visit('/admin/report-stocks')
                    ->assertSee('Report Stocks');
        });
    }

    public function test_navigation_menu_differs_by_role()
    {
        // Test admin navigation
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Products')
                    ->assertSee('Report Stocks')
                    ->assertDontSee('Purchase Requests'); // Admin shouldn't see this in nav
        });

        // Test manager navigation
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Products')
                    ->assertSee('Purchase Requests')
                    ->assertSee('Report Stocks');
        });
    }

    public function test_outlet_filtering_for_admin()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            $adminOutlet = $admin->outlet;
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee($adminOutlet->name); // Should only see their outlet's products
        });
    }

    public function test_outlet_selection_for_manager()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests/create')
                    ->assertSee('Outlet') // Should see outlet selection dropdown
                    ->assertSee('Select an option'); // Should have outlet options
        });
    }

    public function test_dashboard_widgets_show_role_appropriate_data()
    {
        // Test admin dashboard
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Total Products') // Should see stats widgets
                    ->assertSee('Stock Reports')
                    ->assertSee('Low Stock Items');
        });

        // Test manager dashboard
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin')
                    ->assertSee('Total Products') // Should see stats widgets
                    ->assertSee('Total Requests') // Should see purchase request stats
                    ->assertSee('Outlet Comparison'); // Manager-only widget
        });
    }

    public function test_unauthorized_access_redirects_to_login()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/admin/products')
                    ->assertPathIs('/admin/login')
                    ->assertSee('Sign in to your account');
        });
    }

    public function test_user_profile_shows_correct_role()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin')
                    ->assertSee('Admin User') // Should show user name
                    ->assertSee($admin->outlet->name); // Should show outlet name
        });
    }

    public function test_permission_based_button_visibility()
    {
        // Test admin permissions
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Export to Excel') // Admin should see export
                    ->visit('/admin/report-stocks')
                    ->assertSee('Import Stock Data'); // Admin should see import
        });

        // Test manager permissions
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('New purchase request') // Manager should see create button
                    ->assertSee('Export to Excel'); // Manager should see export
        });
    }
}
