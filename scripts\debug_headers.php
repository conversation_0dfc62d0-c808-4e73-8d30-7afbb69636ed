<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;

class HeaderDebugger implements ToCollection, WithHeadingRow
{
    public function collection(Collection $rows)
    {
        echo "=== Header Debugging ===\n";
        echo "Total rows: " . $rows->count() . "\n";
        
        if ($rows->isNotEmpty()) {
            $firstRow = $rows->first();
            echo "\nFirst row keys (headers):\n";
            foreach ($firstRow->keys() as $key) {
                echo "  '{$key}'\n";
            }
            
            echo "\nFirst row data:\n";
            foreach ($firstRow as $key => $value) {
                $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "'{$value}'" : $value);
                echo "  {$key}: {$displayValue}\n";
            }
        }
    }
}

$filePath = $argv[1] ?? 'test_import_small.xlsx';

if (!file_exists($filePath)) {
    echo "File not found: {$filePath}\n";
    exit(1);
}

$debugger = new HeaderDebugger();
Excel::import($debugger, $filePath);
