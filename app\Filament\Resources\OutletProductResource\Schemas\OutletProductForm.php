<?php

namespace App\Filament\Resources\OutletProductResource\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Schemas\Schema;
use App\Models\Outlet;
use App\Models\Product;

class OutletProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('outlet_id')
                    ->label('Outlet')
                    ->options(Outlet::pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->preload()
                    ->placeholder('Select an outlet')
                    ->helperText('Choose the outlet for this product configuration'),

                Select::make('product_id')
                    ->label('Product')
                    ->options(Product::pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->preload()
                    ->placeholder('Select a product')
                    ->helperText('Choose the product to configure for this outlet'),

                Select::make('outlet_pareto')
                    ->label('Outlet Pareto')
                    ->options([
                        'FM' => 'FM (Fast Moving)',
                        'SM' => 'SM (Slow Moving)',
                        'BM' => 'BM (Bad Moving)',
                    ])
                    ->placeholder('Select pareto category')
                    ->helperText('Pareto classification for this product in this outlet'),

                Select::make('rumus_pareto')
                    ->label('Pareto Formula')
                    ->options([
                        'FM' => 'FM (Fast Moving)',
                        'SM' => 'SM (Slow Moving)',
                        'BM' => 'BM (Bad Moving)',
                    ])
                    ->placeholder('Select movement category')
                    ->helperText('Movement classification for inventory management'),

                TextInput::make('min_buffer')
                    ->label('Minimum Buffer')
                    ->numeric()
                    ->default(0)
                    ->minValue(0)
                    ->placeholder('Enter minimum buffer quantity')
                    ->helperText('Minimum stock level to maintain'),

                TextInput::make('max_buffer')
                    ->label('Maximum Buffer')
                    ->numeric()
                    ->default(0)
                    ->minValue(0)
                    ->placeholder('Enter maximum buffer quantity')
                    ->helperText('Maximum stock level to maintain'),
            ]);
    }
}
