<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;

echo "=== Test Optimized Import ===\n\n";

// Create test data
$testFile = 'test_optimized.xlsx';
$reportDate = '2025-09-12';

// Create larger test dataset
$testData = [
    ['OUTLET', 'BARCODE', 'NAMA_PRODUK', 'SAT', 'PACK', 'QTY', 'PRT']
];

// Generate 1000 test rows
for ($i = 1; $i <= 1000; $i++) {
    $testData[] = [
        'OUT' . str_pad(($i % 5) + 1, 3, '0', STR_PAD_LEFT), // 5 different outlets
        '123456789' . str_pad($i, 4, '0', STR_PAD_LEFT), // Unique barcodes
        'Product ' . $i,
        'PCS',
        '1,00',
        rand(0, 100), // Random quantity
        'A'
    ];
}

echo "Creating test Excel with " . (count($testData) - 1) . " rows...\n";

// Create Excel file
$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

foreach ($testData as $rowIndex => $rowData) {
    foreach ($rowData as $colIndex => $cellData) {
        $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
    }
}

$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
$writer->save($testFile);

echo "Test file created: {$testFile}\n\n";

// Get initial counts
$initialCounts = [
    'products' => DB::table('products')->count(),
    'outlets' => DB::table('outlets')->count(),
    'outlet_products' => DB::table('outlet_products')->count(),
    'report_stock_details' => DB::table('report_stock_details')->count(),
];

echo "Initial counts:\n";
foreach ($initialCounts as $table => $count) {
    echo "  {$table}: " . number_format($count) . "\n";
}

// Test import
echo "\nStarting optimized import...\n";
$startTime = microtime(true);
$memStart = memory_get_usage(true);

try {
    $import = new ReportStockBulkImport($reportDate);
    Excel::import($import, $testFile);
    
    $endTime = microtime(true);
    $memEnd = memory_get_usage(true);
    $duration = $endTime - $startTime;
    
    echo "Import completed successfully!\n\n";
    
    // Get final counts
    $finalCounts = [
        'products' => DB::table('products')->count(),
        'outlets' => DB::table('outlets')->count(),
        'outlet_products' => DB::table('outlet_products')->count(),
        'report_stock_details' => DB::table('report_stock_details')->count(),
    ];
    
    echo "=== Import Statistics ===\n";
    echo "Processed rows: " . number_format($import->getProcessedRows()) . "\n";
    echo "Products created: " . number_format($import->getProductsCreated()) . "\n";
    echo "Outlet products created: " . number_format($import->getOutletProductsCreated()) . "\n";
    echo "Outlet products updated: " . number_format($import->getOutletProductsUpdated()) . "\n";
    echo "Report stocks created: " . number_format($import->getReportStocksCreated()) . "\n";
    echo "Report stocks updated: " . number_format($import->getReportStocksUpdated()) . "\n";
    echo "Errors: " . count($import->getErrors()) . "\n\n";
    
    echo "=== Performance Metrics ===\n";
    echo "Duration: " . round($duration, 2) . " seconds\n";
    echo "Memory used: " . formatBytes($memEnd - $memStart) . "\n";
    echo "Peak memory: " . formatBytes(memory_get_peak_usage(true)) . "\n";
    
    if ($import->getProcessedRows() > 0) {
        $rowsPerSecond = $import->getProcessedRows() / $duration;
        echo "Processing speed: " . round($rowsPerSecond, 2) . " rows/second\n";
    }
    
    echo "\n=== Database Changes ===\n";
    foreach ($finalCounts as $table => $finalCount) {
        $initialCount = $initialCounts[$table];
        $change = $finalCount - $initialCount;
        $changeStr = $change > 0 ? "+{$change}" : ($change < 0 ? "{$change}" : "0");
        echo "  {$table}: " . number_format($initialCount) . " -> " . number_format($finalCount) . " ({$changeStr})\n";
    }
    
    echo "\n✅ Test PASSED\n";
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Cleanup
if (file_exists($testFile)) {
    unlink($testFile);
    echo "\nCleaned up test file: {$testFile}\n";
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== Test Complete ===\n";
