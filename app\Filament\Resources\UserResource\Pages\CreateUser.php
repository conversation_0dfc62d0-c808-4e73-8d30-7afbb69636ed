<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource\UserResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('User Created')
            ->body('The user has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If manager role is selected, ensure outlet_id is null
        if (isset($data['roles'])) {
            $roleNames = \Spatie\Permission\Models\Role::whereIn('id', $data['roles'])->pluck('name')->toArray();
            if (in_array('manager', $roleNames)) {
                $data['outlet_id'] = null;
            }
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        $user = $this->record;
        
        // Log user creation
        activity()
            ->performedOn($user)
            ->causedBy(auth()->user())
            ->log("User '{$user->name}' was created");
    }
}
