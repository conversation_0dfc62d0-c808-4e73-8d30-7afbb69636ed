# Purchase Request - New Multi-Step Flow

## Overview

Alur baru untuk Purchase Request telah dimodifikasi menjadi 3 langkah berurutan:
1. **P<PERSON><PERSON>han <PERSON>** - User memilih tanggal purchase request
2. **Pemilihan Outlet** - User memilih outlet (atau otomatis untuk admin)
3. **Pemilihan Produk** - User menambahkan produk berdasarkan tanggal dan outlet yang dipilih

## Alur Langkah-demi-Langkah

### Step 1: Purchase Date Selection
- User memilih tanggal untuk purchase request
- Tanggal maksimal 30 hari ke depan dari hari ini
- Default: tanggal hari ini
- <PERSON><PERSON>ka tanggal berubah, outlet dan produk akan di-reset

### Step 2: Outlet Selection
- **Untuk Admin Users**: Outlet otomatis sesuai dengan outlet yang di-assign
- **Untuk Manager Users**: Dropdown untuk memilih outlet
- Menampilkan tanggal yang sudah dipilih sebagai informasi
- Ketika outlet berubah, daftar produk akan di-reset

### Step 3: Product Selection
- Menampilkan ringkasan tanggal dan outlet yang dipilih
- Repeater untuk menambahkan multiple produk
- Produk yang ditampilkan hanya yang tersedia untuk outlet yang dipilih
- Auto-suggestion quantity berdasarkan:
  - Current stock pada tanggal yang dipilih
  - Min/Max buffer configuration
  - Formula: `max_buffer - current_stock` (jika current stock < min_buffer)

## Fitur-Fitur Baru

### 1. Smart Product Filtering
- Produk hanya menampilkan yang tersedia untuk outlet yang dipilih
- Berdasarkan relasi `outlet_products` table

### 2. Real-time Stock Information
- Menampilkan current stock berdasarkan tanggal dan outlet
- Status stock: Normal, Below Minimum, Out of Stock, Overstock
- Data diambil dari `report_stock_details` table

### 3. Intelligent Quantity Suggestion
- Auto-calculate suggested purchase quantity
- Berdasarkan current stock vs min/max buffer
- Mempertimbangkan pareto formula (FM = Fast Moving)

### 4. Enhanced Product Information Display
- Product details (barcode, unit, pack quantity)
- Buffer configuration (min/max buffer, pareto)
- Current stock status dengan color coding
- Stock availability untuk tanggal yang dipilih

## Technical Implementation

### Files Modified
1. `app/Filament/Resources/PurchaseRequests/Schemas/PurchaseRequestForm.php`
   - Converted to multi-section form
   - Added conditional visibility
   - Enhanced product information display

2. `app/Filament/Resources/PurchaseRequests/Pages/CreatePurchaseRequest.php`
   - Added custom record creation logic
   - Handle purchase_details array
   - Enhanced notifications

### Database Relations Used
- `outlets` ↔ `outlet_products` ↔ `products`
- `report_stocks` ↔ `report_stock_details`
- `purchase_requests` ↔ `purchase_request_details`

### Key Components
- **Section**: Multi-step form sections with collapsible UI
- **Conditional Visibility**: Steps only show when previous steps completed
- **Live Updates**: Real-time form updates when selections change
- **Smart Defaults**: Auto-populate based on stock data and buffer config

## User Experience Improvements

### 1. Progressive Disclosure
- Users only see relevant options based on previous selections
- Reduces cognitive load and prevents errors

### 2. Contextual Information
- Stock status and buffer information displayed inline
- Color-coded status indicators for quick assessment

### 3. Smart Suggestions
- Automatic quantity calculation based on business rules
- Reduces manual calculation errors

### 4. Data Validation
- Ensures outlet-product compatibility
- Prevents invalid combinations

## Business Logic

### Stock Status Calculation
```php
if ($currentStock <= 0) {
    $status = 'Out of Stock';
} elseif ($currentStock < $minBuffer) {
    $status = 'Below Minimum';
} elseif ($currentStock > $maxBuffer) {
    $status = 'Overstock';
} else {
    $status = 'Normal';
}
```

### Purchase Quantity Suggestion
```php
if ($currentStock < $minBuffer) {
    $suggestedQuantity = $maxBuffer - $currentStock;
}
```

## Future Enhancements

1. **Batch Product Addition**: Add multiple products at once based on stock reports
2. **Purchase History**: Show previous purchase patterns for the same date/outlet
3. **Approval Workflow**: Add approval steps for large quantities
4. **Export/Import**: Bulk operations for purchase requests
5. **Analytics**: Purchase request analytics and reporting

## Testing Checklist

- [ ] Date selection works correctly
- [ ] Outlet selection shows appropriate options based on user role
- [ ] Product filtering works for selected outlet
- [ ] Stock information displays correctly
- [ ] Quantity suggestions are accurate
- [ ] Form submission creates records properly
- [ ] Validation prevents invalid data entry
- [ ] UI is responsive and user-friendly
