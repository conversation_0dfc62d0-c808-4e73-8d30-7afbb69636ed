(()=>{var ee=Object.create,q=Object.defineProperty,te=Object.getPrototypeOf,re=Object.prototype.hasOwnProperty,ne=Object.getOwnPropertyNames,ie=Object.getOwnPropertyDescriptor,se=r=>q(r,"__esModule",{value:!0}),ae=(r,n)=>()=>(n||(n={exports:{}},r(n.exports,n)),n.exports),oe=(r,n,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let l of ne(n))!re.call(r,l)&&l!=="default"&&q(r,l,{get:()=>n[l],enumerable:!(s=ie(n,l))||s.enumerable});return r},fe=r=>oe(se(q(r!=null?ee(te(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),le=ae((r,n)=>{(function(s,l,g){if(!s)return;for(var d={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},w={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},b={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},x={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},P,y=1;y<20;++y)d[111+y]="f"+y;for(y=0;y<=9;++y)d[y+96]=y.toString();function S(e,t,a){if(e.addEventListener){e.addEventListener(t,a,!1);return}e.attachEvent("on"+t,a)}function G(e){if(e.type=="keypress"){var t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return d[e.which]?d[e.which]:w[e.which]?w[e.which]:String.fromCharCode(e.which).toLowerCase()}function N(e,t){return e.sort().join(",")===t.sort().join(",")}function V(e){var t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}function H(e){if(e.preventDefault){e.preventDefault();return}e.returnValue=!1}function F(e){if(e.stopPropagation){e.stopPropagation();return}e.cancelBubble=!0}function C(e){return e=="shift"||e=="ctrl"||e=="alt"||e=="meta"}function J(){if(!P){P={};for(var e in d)e>95&&e<112||d.hasOwnProperty(e)&&(P[d[e]]=e)}return P}function B(e,t,a){return a||(a=J()[e]?"keydown":"keypress"),a=="keypress"&&t.length&&(a="keydown"),a}function X(e){return e==="+"?["+"]:(e=e.replace(/\+{2}/g,"+plus"),e.split("+"))}function T(e,t){var a,h,k,A=[];for(a=X(e),k=0;k<a.length;++k)h=a[k],x[h]&&(h=x[h]),t&&t!="keypress"&&b[h]&&(h=b[h],A.push("shift")),C(h)&&A.push(h);return t=B(h,A,t),{key:h,modifiers:A,action:t}}function U(e,t){return e===null||e===l?!1:e===t?!0:U(e.parentNode,t)}function v(e){var t=this;if(e=e||l,!(t instanceof v))return new v(e);t.target=e,t._callbacks={},t._directMap={};var a={},h,k=!1,A=!1,D=!1;function E(i){i=i||{};var f=!1,p;for(p in a){if(i[p]){f=!0;continue}a[p]=0}f||(D=!1)}function I(i,f,p,o,c,m){var u,_,M=[],O=p.type;if(!t._callbacks[i])return[];for(O=="keyup"&&C(i)&&(f=[i]),u=0;u<t._callbacks[i].length;++u)if(_=t._callbacks[i][u],!(!o&&_.seq&&a[_.seq]!=_.level)&&O==_.action&&(O=="keypress"&&!p.metaKey&&!p.ctrlKey||N(f,_.modifiers))){var Z=!o&&_.combo==c,$=o&&_.seq==o&&_.level==m;(Z||$)&&t._callbacks[i].splice(u,1),M.push(_)}return M}function L(i,f,p,o){t.stopCallback(f,f.target||f.srcElement,p,o)||i(f,p)===!1&&(H(f),F(f))}t._handleKey=function(i,f,p){var o=I(i,f,p),c,m={},u=0,_=!1;for(c=0;c<o.length;++c)o[c].seq&&(u=Math.max(u,o[c].level));for(c=0;c<o.length;++c){if(o[c].seq){if(o[c].level!=u)continue;_=!0,m[o[c].seq]=1,L(o[c].callback,p,o[c].combo,o[c].seq);continue}_||L(o[c].callback,p,o[c].combo)}var M=p.type=="keypress"&&A;p.type==D&&!C(i)&&!M&&E(m),A=_&&p.type=="keydown"};function K(i){typeof i.which!="number"&&(i.which=i.keyCode);var f=G(i);if(f){if(i.type=="keyup"&&k===f){k=!1;return}t.handleKey(f,V(i),i)}}function Y(){clearTimeout(h),h=setTimeout(E,1e3)}function Q(i,f,p,o){a[i]=0;function c(O){return function(){D=O,++a[i],Y()}}function m(O){L(p,O,i),o!=="keyup"&&(k=G(O)),setTimeout(E,10)}for(var u=0;u<f.length;++u){var _=u+1===f.length,M=_?m:c(o||T(f[u+1]).action);z(f[u],M,o,i,u)}}function z(i,f,p,o,c){t._directMap[i+":"+p]=f,i=i.replace(/\s+/g," ");var m=i.split(" "),u;if(m.length>1){Q(i,m,f,p);return}u=T(i,p),t._callbacks[u.key]=t._callbacks[u.key]||[],I(u.key,u.modifiers,{type:u.action},o,i,c),t._callbacks[u.key][o?"unshift":"push"]({callback:f,modifiers:u.modifiers,action:u.action,seq:o,level:c,combo:i})}t._bindMultiple=function(i,f,p){for(var o=0;o<i.length;++o)z(i[o],f,p)},S(e,"keypress",K),S(e,"keydown",K),S(e,"keyup",K)}v.prototype.bind=function(e,t,a){var h=this;return e=e instanceof Array?e:[e],h._bindMultiple.call(h,e,t,a),h},v.prototype.unbind=function(e,t){var a=this;return a.bind.call(a,e,function(){},t)},v.prototype.trigger=function(e,t){var a=this;return a._directMap[e+":"+t]&&a._directMap[e+":"+t]({},e),a},v.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},v.prototype.stopCallback=function(e,t){var a=this;if((" "+t.className+" ").indexOf(" mousetrap ")>-1||U(t,a.target))return!1;if("composedPath"in e&&typeof e.composedPath=="function"){var h=e.composedPath()[0];h!==e.target&&(t=h)}return t.tagName=="INPUT"||t.tagName=="SELECT"||t.tagName=="TEXTAREA"||t.isContentEditable},v.prototype.handleKey=function(){var e=this;return e._handleKey.apply(e,arguments)},v.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(d[t]=e[t]);P=null},v.init=function(){var e=v(l);for(var t in e)t.charAt(0)!=="_"&&(v[t]=function(a){return function(){return e[a].apply(e,arguments)}}(t))},v.init(),s.Mousetrap=v,typeof n<"u"&&n.exports&&(n.exports=v),typeof define=="function"&&define.amd&&define(function(){return v})})(typeof window<"u"?window:null,typeof window<"u"?document:null)}),R=fe(le());(function(r){if(r){var n={},s=r.prototype.stopCallback;r.prototype.stopCallback=function(l,g,d,w){var b=this;return b.paused?!0:n[d]||n[w]?!1:s.call(b,l,g,d)},r.prototype.bindGlobal=function(l,g,d){var w=this;if(w.bind(l,g,d),l instanceof Array){for(var b=0;b<l.length;b++)n[l[b]]=!0;return}n[l]=!0},r.init()}})(typeof Mousetrap<"u"?Mousetrap:void 0);var ue=r=>{r.directive("mousetrap",(n,{modifiers:s,expression:l},{evaluate:g})=>{let d=()=>l?g(l):n.click();s=s.map(w=>w.replace(/--/g," ").replace(/-/g,"+").replace(/\bslash\b/g,"/")),s.includes("global")&&(s=s.filter(w=>w!=="global"),R.default.bindGlobal(s,w=>{w.preventDefault(),d()})),R.default.bind(s,w=>{w.preventDefault(),d()})})},W=ue;var j=()=>({isOpen:window.Alpine.$persist(!0).as("isOpen"),isOpenDesktop:window.Alpine.$persist(!0).as("isOpenDesktop"),collapsedGroups:window.Alpine.$persist(null).as("collapsedGroups"),init(){this.resizeObserver=null,this.setUpResizeObserver(),document.addEventListener("livewire:navigated",()=>{this.setUpResizeObserver()})},setUpResizeObserver(){this.resizeObserver&&this.resizeObserver.disconnect();let r=window.innerWidth;this.resizeObserver=new ResizeObserver(()=>{let n=window.innerWidth,s=r>=1024,l=n<1024,g=n>=1024;s&&l?(this.isOpenDesktop=this.isOpen,this.isOpen&&this.close()):!s&&g&&(this.isOpen=this.isOpenDesktop),r=n}),this.resizeObserver.observe(document.body),window.innerWidth<1024?this.isOpen&&(this.isOpenDesktop=!0,this.close()):this.isOpenDesktop=this.isOpen},groupIsCollapsed(r){return this.collapsedGroups.includes(r)},collapseGroup(r){this.collapsedGroups.includes(r)||(this.collapsedGroups=this.collapsedGroups.concat(r))},toggleCollapsedGroup(r){this.collapsedGroups=this.collapsedGroups.includes(r)?this.collapsedGroups.filter(n=>n!==r):this.collapsedGroups.concat(r)},close(){this.isOpen=!1,window.innerWidth>=1024&&(this.isOpenDesktop=!1)},open(){this.isOpen=!0,window.innerWidth>=1024&&(this.isOpenDesktop=!0)}});document.addEventListener("alpine:init",()=>{let r=localStorage.getItem("theme")??getComputedStyle(document.documentElement).getPropertyValue("--default-theme-mode");window.Alpine.store("theme",r==="dark"||r==="system"&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.addEventListener("theme-changed",n=>{let s=n.detail;localStorage.setItem("theme",s),s==="system"&&(s=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.Alpine.store("theme",s)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",n=>{localStorage.getItem("theme")==="system"&&window.Alpine.store("theme",n.matches?"dark":"light")}),window.Alpine.effect(()=>{window.Alpine.store("theme")==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")})});document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{let r=document.querySelector(".fi-main-sidebar .fi-sidebar-item.fi-active");if((!r||r.offsetParent===null)&&(r=document.querySelector(".fi-main-sidebar .fi-sidebar-group.fi-active")),!r||r.offsetParent===null)return;let n=document.querySelector(".fi-main-sidebar .fi-sidebar-nav");n&&n.scrollTo(0,r.offsetTop-window.innerHeight/2)},10)});window.setUpUnsavedDataChangesAlert=({body:r,livewireComponent:n,$wire:s})=>{window.addEventListener("beforeunload",l=>{window.jsMd5(JSON.stringify(s.data).replace(/\\/g,""))===s.savedDataHash||s?.__instance?.effects?.redirect||(l.preventDefault(),l.returnValue=!0)})};window.setUpSpaModeUnsavedDataChangesAlert=({body:r,resolveLivewireComponentUsing:n,$wire:s})=>{let l=()=>s?.__instance?.effects?.redirect?!1:window.jsMd5(JSON.stringify(s.data).replace(/\\/g,""))!==s.savedDataHash,g=()=>confirm(r);document.addEventListener("livewire:navigate",d=>{if(typeof n()<"u"){if(!l()||g())return;d.preventDefault()}}),window.addEventListener("beforeunload",d=>{l()&&(d.preventDefault(),d.returnValue=!0)})};window.setUpUnsavedActionChangesAlert=({resolveLivewireComponentUsing:r,$wire:n})=>{window.addEventListener("beforeunload",s=>{if(!(typeof r()>"u")&&(n.mountedActions?.length??0)&&!n?.__instance?.effects?.redirect){s.preventDefault(),s.returnValue=!0;return}})};document.addEventListener("alpine:init",()=>{window.Alpine.plugin(W),window.Alpine.store("sidebar",j())});})();
