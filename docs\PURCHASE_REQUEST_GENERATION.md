# 🛒 Purchase Request Generation Feature

## Overview

Fitur Generate Purchase Request memungkinkan sistem untuk secara otomatis membuat purchase request berdasarkan data report stock. Sistem akan menganalisis stock yang rendah dan menghasilkan purchase request dengan quantity yang tepat berdasarkan buffer settings atau threshold yang dikonfigurasi.

## 🎯 Features

### 1. **Automatic Purchase Request Generation**
- Generate purchase request berdasarkan report stock data
- Analisis stock rendah dengan threshold yang dapat dikonfigurasi
- Perhitungan quantity berdasarkan buffer settings atau default logic
- Support untuk multiple outlets dalam satu report

### 2. **Smart Calculation Logic**
- **Buffer-based**: Menggunakan min/max buffer dari OutletProduct settings
- **Threshold-based**: Default logic dengan configurable threshold dan multiplier
- **Zero stock handling**: Prioritas tinggi untuk produk yang habis
- **Flexible parameters**: Threshold dan multiplier dapat disesuaikan

### 3. **Low Stock Analysis**
- Summary lengkap produk dengan stock rendah
- Breakdown per outlet dengan statistik detail
- Kategorisasi: Out of Stock, Critical, Low Stock, Normal
- Suggested purchase quantity untuk setiap produk

### 4. **Multiple Access Methods**
- **Filament UI**: Actions di halaman Report Stocks
- **CLI Command**: Untuk automation dan batch processing
- **Service Class**: Untuk integration dengan sistem lain

## 🚀 Usage

### 1. Via Filament UI

#### Generate Purchase Request
1. Buka halaman **Report Stocks** (`/admin/report-stocks`)
2. Pilih report date yang ingin diproses
3. Klik action **"Generate Purchase Request"**
4. Konfigurasi parameters:
   - **Low Stock Threshold**: Batas stock rendah (default: 10)
   - **Purchase Multiplier**: Multiplier untuk target stock (default: 2)
   - **Force Regenerate**: Regenerate meskipun sudah ada (default: false)
5. Klik **Submit** untuk generate

#### View Low Stock Summary
1. Klik action **"View Low Stock Summary"** pada report date
2. Modal akan menampilkan:
   - Summary statistics (total items, outlets, products affected)
   - Breakdown detail per outlet
   - Status setiap produk (Out of Stock, Critical, Low Stock, Normal)
   - Suggested purchase quantity

### 2. Via CLI Command

```bash
# Basic usage
php artisan purchase:generate --date=2025-09-04

# With custom parameters
php artisan purchase:generate --date=2025-09-04 --threshold=15 --multiplier=3

# Force regenerate existing requests
php artisan purchase:generate --date=2025-09-04 --force

# Show help
php artisan purchase:generate --help
```

#### Command Parameters
- `--date`: Report date (Y-m-d format, default: today)
- `--threshold`: Low stock threshold (default: 10)
- `--multiplier`: Purchase multiplier (default: 2)
- `--force`: Force regenerate existing requests

### 3. Via Service Class

```php
use App\Services\ReportStockPurchaseService;

$service = new ReportStockPurchaseService();

// Generate purchase requests
$options = [
    'low_stock_threshold' => 10,
    'purchase_multiplier' => 2,
    'force_regenerate' => false
];

$results = $service->generatePurchaseRequest($reportStock, $options);

// Get low stock summary
$summary = $service->getLowStockSummary($reportStock, $options);
```

## 🧮 Calculation Logic

### 1. Buffer-based Calculation (Preferred)
Jika OutletProduct memiliki buffer settings:

```
if (current_stock <= min_buffer) {
    target_stock = max_buffer > 0 ? max_buffer : min_buffer * 2
    purchase_quantity = target_stock - current_stock
}
```

### 2. Threshold-based Calculation (Fallback)
Jika tidak ada buffer settings:

```
if (current_stock <= threshold) {
    target_stock = threshold * multiplier
    purchase_quantity = target_stock - current_stock
}
```

### 3. Examples

#### Example 1: Buffer-based
- Current Stock: 5
- Min Buffer: 10
- Max Buffer: 50
- **Result**: Purchase 45 units (50 - 5)

#### Example 2: Threshold-based
- Current Stock: 3
- Threshold: 10
- Multiplier: 2
- **Result**: Purchase 17 units (20 - 3)

## 📊 Data Structure

### Purchase Request Header
```php
PurchaseRequest {
    outlet_id: int
    request_date: date
    is_processed: boolean
}
```

### Purchase Request Details
```php
PurchaseRequestDetail {
    purchase_request_id: int
    outlet_id: int
    product_id: int
    purchase_quantity: int
}
```

## 🔧 Configuration

### Buffer Settings (OutletProduct)
```php
OutletProduct {
    outlet_id: int
    product_id: int
    min_buffer: int    // Minimum stock level
    max_buffer: int    // Maximum stock level
    outlet_pareto: string  // A, B, C classification
}
```

### Default Parameters
- **Low Stock Threshold**: 10 units
- **Purchase Multiplier**: 2x
- **Force Regenerate**: false

## 📈 Performance

### Optimizations Applied
- **Bulk Operations**: Efficient database queries
- **Caching**: Reduced redundant lookups
- **Batch Processing**: Memory-efficient processing
- **Transaction Safety**: Rollback on errors

### Performance Metrics
- **Average Execution Time**: ~8ms per summary
- **Memory Usage**: Optimized for large datasets
- **Database Queries**: Minimized with proper indexing

## 🛡️ Error Handling

### Common Errors
1. **"Purchase request already exists"**: Use force flag to regenerate
2. **"No report stock found"**: Ensure report date exists
3. **"No outlets with reports"**: Check report stock details
4. **Database constraints**: Handled with transactions

### Error Recovery
- **Transaction Rollback**: Automatic rollback on errors
- **Detailed Logging**: Error tracking and debugging
- **Graceful Degradation**: Partial success handling

## 🧪 Testing

### Test Coverage
- ✅ Service functionality
- ✅ CLI command
- ✅ Database integration
- ✅ Error scenarios
- ✅ Performance benchmarks

### Run Tests
```bash
# Test service functionality
php test_purchase_request_generation.php

# Test CLI command
php artisan purchase:generate --date=2025-09-04
```

## 🔮 Future Enhancements

### Planned Features
1. **Supplier Integration**: Auto-assign suppliers to purchase requests
2. **Cost Calculation**: Include product costs and budgets
3. **Approval Workflow**: Multi-level approval process
4. **Email Notifications**: Auto-notify relevant stakeholders
5. **Seasonal Adjustments**: Factor in seasonal demand patterns
6. **ABC Analysis**: Enhanced Pareto-based calculations

### API Integration
- REST API endpoints for external systems
- Webhook notifications for purchase request events
- Integration with ERP systems

## 📝 Changelog

### Version 1.0.0 (2025-09-04)
- ✅ Initial release
- ✅ Basic purchase request generation
- ✅ Low stock analysis
- ✅ Filament UI integration
- ✅ CLI command support
- ✅ Buffer-based calculations
- ✅ Force regeneration capability

---

**Developed by**: Apotek Keluarga Development Team  
**Last Updated**: September 4, 2025
