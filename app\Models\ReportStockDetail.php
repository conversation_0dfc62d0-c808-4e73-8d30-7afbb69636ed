<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\HasOutletScope;

class ReportStockDetail extends Model
{
    use HasFactory, HasOutletScope;

    protected $fillable = [
        'report_stock_id',
        'outlet_id',
        'product_id',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    public function reportStock(): BelongsTo
    {
        return $this->belongsTo(ReportStock::class);
    }

    public function outlet(): BelongsTo
    {
        return $this->belongsTo(Outlet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function outletProduct(): BelongsTo
    {
        return $this->belongsTo(OutletProduct::class, 'outlet_id', 'outlet_id')
                    ->where('product_id', $this->product_id);
    }

    // Scopes
    public function scopeForOutletAndDate($query, $outletId, $date)
    {
        return $query->whereHas('reportStock', function ($q) use ($outletId, $date) {
            $q->where('outlet_id', $outletId)
              ->where('report_date', $date);
        });
    }

    public function scopeForDate($query, $date)
    {
        return $query->whereHas('reportStock', function ($q) use ($date) {
            $q->where('report_date', $date);
        });
    }

    // Helper methods
    public function getDisplayName(): string
    {
        return "{$this->product->name} - {$this->outlet->name} ({$this->reportStock->report_date->format('Y-m-d')})";
    }
}
