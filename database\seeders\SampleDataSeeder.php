<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Outlet;
use App\Models\User;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\PurchaseRequest;
use Illuminate\Support\Facades\Hash;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create outlets

        for ($i = 1; $i < 32; $i++) {
            Outlet::create([
                'name' => "Apotek Keluarga {$i}",
                'code' => "AK" . str_pad($i, 2, "0", STR_PAD_LEFT),
            ]);
        }

        // Create manager user
        $manager = User::create([
            'username' => 'manager',
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'outlet_id' => null, // Manager can access all outlets
        ]);
        $manager->assignRole('manager');
    }
}
