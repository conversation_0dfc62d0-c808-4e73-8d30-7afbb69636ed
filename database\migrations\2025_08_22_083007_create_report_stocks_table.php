<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_stocks', function (Blueprint $table) {
            $table->id();
            $table->date('report_date');
            $table->boolean('is_generated')->default(false);
            $table->timestamps();

            $table->index(['report_date']);
            $table->unique(['report_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_stocks');
    }
};
