<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\ReportStock;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

echo "=== Test Notification Import ===\n\n";

// Create a test Excel file with sample data
$testFile = 'test_notification.xlsx';
$testData = [
    ['OUTLET', 'BARCODE', 'PRODUCT_NAME', 'UNIT', 'HPP', 'STOCK'],
    ['OUT001', '1234567890001', 'Test Product 1', 'PCS', 5000, 10],
    ['OUT001', '1234567890002', 'Test Product 2', 'BOX', 15000, 5],
    ['OUT002', '1234567890001', 'Test Product 1', 'PCS', 5000, 8],
    ['OUT002', '1234567890003', 'Test Product 3', 'STRIP', 2500, 20],
];

// Create Excel file
$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

foreach ($testData as $rowIndex => $rowData) {
    foreach ($rowData as $colIndex => $cellData) {
        $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
    }
}

$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
$writer->save($testFile);

echo "Created test Excel file: {$testFile}\n";

// Test the import process
$reportDate = '2025-09-12';

// Create or get report stock
$reportStock = ReportStock::firstOrCreate(
    ['report_date' => $reportDate],
    ['is_generated' => false]
);

echo "Report Stock ID: {$reportStock->id}\n";
echo "Report Date: {$reportDate}\n\n";

// Get initial counts
$initialDetails = $reportStock->details()->count();
$initialOutlets = $reportStock->details()->distinct('outlet_id')->count();
$initialProducts = $reportStock->details()->distinct('product_id')->count();

echo "Initial counts:\n";
echo "  Details: {$initialDetails}\n";
echo "  Outlets: {$initialOutlets}\n";
echo "  Products: {$initialProducts}\n\n";

// Perform import
echo "Starting import...\n";
$import = new ReportStockBulkImport($reportStock->id);

try {
    Excel::import($import, $testFile);
    echo "Import completed successfully!\n\n";
    
    // Get final counts (simulate what the notification would show)
    $reportStock->refresh();
    $totalDetails = $reportStock->details()->count();
    $totalOutlets = $reportStock->details()->distinct('outlet_id')->count();
    $totalProducts = $reportStock->details()->distinct('product_id')->count();
    $totalQuantity = $reportStock->details()->sum('quantity');

    // Simulate the notification message
    echo "=== Notification Preview ===\n";
    $message = "✅ Import completed successfully!\n\n";
    
    $message .= "📊 Import Summary:\n";
    $message .= "• Report Date: " . \Carbon\Carbon::parse($reportDate)->format('M j, Y') . "\n";
    $message .= "• Stock details imported: " . number_format($totalDetails) . "\n";
    $message .= "• Outlets involved: " . number_format($totalOutlets) . "\n";
    $message .= "• Unique products: " . number_format($totalProducts) . "\n";
    $message .= "• Total stock quantity: " . number_format($totalQuantity) . "\n\n";

    $message .= "🏪 Data Processing:\n";
    $message .= "• Products and outlet relationships have been created/updated as needed\n";
    $message .= "• Stock quantities have been imported for the specified date\n";
    $message .= "• All data has been validated and processed successfully\n\n";

    $message .= "✨ Import completed without errors!\n";
    $message .= "💡 You can now view the imported stock data in the reports section.";

    echo $message . "\n\n";

    echo "=== Final Statistics ===\n";
    echo "Details: {$initialDetails} -> {$totalDetails} (+" . ($totalDetails - $initialDetails) . ")\n";
    echo "Outlets: {$initialOutlets} -> {$totalOutlets} (+" . ($totalOutlets - $initialOutlets) . ")\n";
    echo "Products: {$initialProducts} -> {$totalProducts} (+" . ($totalProducts - $initialProducts) . ")\n";
    echo "Total Quantity: " . number_format($totalQuantity) . "\n";

} catch (\Exception $e) {
    echo "Import failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Cleanup
if (file_exists($testFile)) {
    unlink($testFile);
    echo "\nCleaned up test file: {$testFile}\n";
}

echo "\n=== Test Complete ===\n";
