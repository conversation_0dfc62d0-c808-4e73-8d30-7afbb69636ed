<?php

namespace App\Services;

use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestDetail;
use App\Models\OutletProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Service for generating purchase requests from report stock data
 *
 * This service handles the generation of purchase requests based on stock reports
 * and outlet product buffer settings. It follows the new database structure where:
 * - PurchaseRequest contains only purchase_request_date
 * - PurchaseRequestDetail contains outlet_id, product_id, and purchase_quantity
 *
 * @package App\Services
 */
class ReportStockPurchaseService
{
    /**
     * Generate purchase request from report stock
     */
    public function generatePurchaseRequest(ReportStock $reportStock, array $options = []): array
    {
        $results = [
            'success' => false,
            'purchase_requests' => [],
            'total_outlets' => 0,
            'total_products' => 0,
            'errors' => []
        ];

        try {
            DB::beginTransaction();

            // Get all outlets that have report stock details for this date
            $outlets = $this->getOutletsWithReportStock($reportStock);
            $results['total_outlets'] = $outlets->count();

            foreach ($outlets as $outlet) {
                try {
                    $purchaseRequest = $this->generatePurchaseRequestForOutlet(
                        $reportStock, 
                        $outlet, 
                        $options
                    );
                    
                    if ($purchaseRequest) {
                        $results['purchase_requests'][] = $purchaseRequest;
                        $results['total_products'] += $purchaseRequest->getTotalProducts();
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Error for outlet {$outlet->name}: " . $e->getMessage();
                    Log::error('Purchase request generation failed for outlet', [
                        'outlet_id' => $outlet->id,
                        'report_stock_id' => $reportStock->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Mark report stock as processed if successful
            if (count($results['purchase_requests']) > 0) {
                $reportStock->update(['is_generated' => true]);
                $results['success'] = true;
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            $results['errors'][] = "General error: " . $e->getMessage();
            Log::error('Purchase request generation failed', [
                'report_stock_id' => $reportStock->id,
                'error' => $e->getMessage()
            ]);
        }

        return $results;
    }

    /**
     * Generate purchase request for specific outlet
     */
    protected function generatePurchaseRequestForOutlet(
        ReportStock $reportStock,
        $outlet,
        array $options = []
    ): ?PurchaseRequest {
        // Check if purchase request already exists for this date
        $existingRequest = PurchaseRequest::where('purchase_request_date', $reportStock->report_date)
            ->first();

        if ($existingRequest) {
            if (!($options['force_regenerate'] ?? false)) {
                // If not forcing regeneration, just add items to existing request
                $purchaseRequest = $existingRequest;
            } else {
                // Delete existing request and its details for regeneration
                $existingRequest->details()->delete();
                $existingRequest->delete();
                $purchaseRequest = null;
            }
        } else {
            $purchaseRequest = null;
        }

        // Get report stock details for this outlet
        $reportDetails = ReportStockDetail::where('outlet_id', $outlet->id)
            ->where('report_stock_id', $reportStock->id)
            ->with(['product', 'outlet'])
            ->get();

        if ($reportDetails->isEmpty()) {
            return $purchaseRequest;
        }

        // Create purchase request header if not exists
        if (!$purchaseRequest) {
            $purchaseRequest = PurchaseRequest::create([
                'purchase_request_date' => $reportStock->report_date,
            ]);
        }

        $itemsAdded = 0;

        // Process each product
        foreach ($reportDetails as $detail) {
            // Check if this product already exists in purchase request for this outlet
            $existingDetail = PurchaseRequestDetail::where('purchase_request_id', $purchaseRequest->id)
                ->where('outlet_id', $outlet->id)
                ->where('product_id', $detail->product_id)
                ->first();

            if ($existingDetail && !($options['force_regenerate'] ?? false)) {
                continue; // Skip if already exists and not forcing regeneration
            }

            $purchaseQuantity = $this->calculatePurchaseQuantity($detail);

            if ($purchaseQuantity > 0) {
                if ($existingDetail) {
                    // Update existing detail
                    $existingDetail->update([
                        'purchase_quantity' => $purchaseQuantity,
                    ]);
                } else {
                    // Create new detail
                    PurchaseRequestDetail::create([
                        'purchase_request_id' => $purchaseRequest->id,
                        'outlet_id' => $outlet->id,
                        'product_id' => $detail->product_id,
                        'purchase_quantity' => $purchaseQuantity,
                    ]);
                }

                $itemsAdded++;
            }
        }

        Log::info('Purchase request processed', [
            'purchase_request_id' => $purchaseRequest->id,
            'outlet_id' => $outlet->id,
            'items_added' => $itemsAdded,
            'report_date' => $reportStock->report_date
        ]);

        return $purchaseRequest;
    }

    /**
     * Calculate purchase quantity based on current stock and buffer settings
     * Only generate for products where current stock <= min_buffer
     */
    protected function calculatePurchaseQuantity(ReportStockDetail $detail): int
    {
        $currentStock = $detail->quantity;
        $product = $detail->product;
        $outletId = $detail->outlet_id;

        // Get outlet product settings
        $outletProduct = OutletProduct::where('outlet_id', $outletId)
            ->where('product_id', $product->id)
            ->first();

        if (!$outletProduct) {
            // No outlet product settings found, skip this product
            return 0;
        }

        $minBuffer = $outletProduct->min_buffer ?? 0;
        $maxBuffer = $outletProduct->max_buffer ?? 0;
        $pareto = $outletProduct->outlet_pareto;

        // Only generate purchase request if min_buffer and max_buffer are set and current stock <= min_buffer
        if ($minBuffer <= 0) {
            // No min_buffer set, skip this product
            return 0;
        }

        if ($currentStock > $minBuffer) {
            // Stock is above minimum buffer, no purchase needed
            return 0;
        }

        if ($maxBuffer <= 0) {
            // No max_buffer set, use default calculation
            $maxBuffer = $minBuffer * 2;
        }

        // Check pareto classification
        if (empty($pareto)) {
            // No pareto set, skip this product
            return 0;
        }

        // Stock is at or below minimum buffer, calculate purchase to reach max buffer
        $targetStock = $maxBuffer;
        $neededQuantity = $targetStock - $currentStock;

        if ($neededQuantity <= 0) {
            return 0;
        }

        // Calculate purchase quantity based on pack size and pareto classification
        $packQuantity = $product->pack_quantity ?? 1;

        if ($pareto == 'FMI' || $pareto == 'FM') {
            // Fast moving items: round up to ensure adequate stock
            $purchaseQuantity = ceil($neededQuantity / $packQuantity) * $packQuantity;
        } else {
            // Slow/Bad moving items: round down to minimize excess stock
            $purchaseQuantity = floor($neededQuantity / $packQuantity) * $packQuantity;
        }

        return max(0, $purchaseQuantity);
    }

    /**
     * Default purchase quantity calculation when no buffer settings
     */
    protected function getDefaultPurchaseQuantity(int $currentStock, array $options = []): int
    {
        $threshold = $options['low_stock_threshold'] ?? 10;
        $purchaseMultiplier = $options['purchase_multiplier'] ?? 2;

        if ($currentStock <= $threshold) {
            // Calculate purchase quantity to bring stock to reasonable level
            $targetStock = $threshold * $purchaseMultiplier;
            return max(0, $targetStock - $currentStock);
        }

        return 0;
    }

    /**
     * Get outlets that have report stock details for the given report stock
     */
    protected function getOutletsWithReportStock(ReportStock $reportStock)
    {
        return DB::table('outlets')
            ->join('report_stock_details', 'outlets.id', '=', 'report_stock_details.outlet_id')
            ->where('report_stock_details.report_stock_id', $reportStock->id)
            ->select('outlets.*')
            ->distinct()
            ->get();
    }

    /**
     * Get summary of low stock items for a report
     * Only show products that have min_buffer settings and current stock <= min_buffer
     */
    public function getLowStockSummary(ReportStock $reportStock): array
    {
        // Get report stock details with outlet product buffer settings using raw SQL for better performance
        $lowStockItems = DB::table('report_stock_details as rsd')
            ->join('outlet_products as op', function($join) {
                $join->on('rsd.outlet_id', '=', 'op.outlet_id')
                     ->on('rsd.product_id', '=', 'op.product_id');
            })
            ->join('products as p', 'rsd.product_id', '=', 'p.id')
            ->join('outlets as o', 'rsd.outlet_id', '=', 'o.id')
            ->where('rsd.report_stock_id', $reportStock->id)
            ->where('op.min_buffer', '>', 0)
            ->whereRaw('rsd.quantity <= op.min_buffer')
            ->select([
                'rsd.*',
                'p.name as product_name',
                'p.barcode as product_barcode',
                'o.name as outlet_name',
                'op.min_buffer',
                'op.max_buffer'
            ])
            ->get()
            ->map(function ($item) {
                // Convert to object with nested structure for compatibility
                return (object) [
                    'id' => $item->id,
                    'outlet_id' => $item->outlet_id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'product' => (object) [
                        'id' => $item->product_id,
                        'name' => $item->product_name,
                        'barcode' => $item->product_barcode,
                    ],
                    'outlet' => (object) [
                        'id' => $item->outlet_id,
                        'name' => $item->outlet_name,
                    ],
                    'min_buffer' => $item->min_buffer,
                    'max_buffer' => $item->max_buffer,
                ];
            });

        $summary = [
            'total_low_stock_items' => $lowStockItems->count(),
            'outlets_affected' => $lowStockItems->pluck('outlet_id')->unique()->count(),
            'products_affected' => $lowStockItems->pluck('product_id')->unique()->count(),
            'items_by_outlet' => []
        ];

        // Group by outlet
        $groupedByOutlet = $lowStockItems->groupBy('outlet_id');
        foreach ($groupedByOutlet as $items) {
            $outlet = $items->first()->outlet;
            $summary['items_by_outlet'][] = [
                'outlet' => $outlet,
                'low_stock_count' => $items->count(),
                'out_of_stock_count' => $items->where('quantity', 0)->count(),
                'items' => $items->map(function ($item) {
                    // Calculate suggested purchase based on buffer settings
                    $targetStock = $item->max_buffer > 0 ? $item->max_buffer : $item->min_buffer * 2;
                    $suggestedPurchase = max(0, $targetStock - $item->quantity);

                    return [
                        'product' => $item->product,
                        'current_quantity' => $item->quantity,
                        'min_buffer' => $item->min_buffer,
                        'max_buffer' => $item->max_buffer,
                        'suggested_purchase' => $suggestedPurchase
                    ];
                })
            ];
        }

        return $summary;
    }
}
