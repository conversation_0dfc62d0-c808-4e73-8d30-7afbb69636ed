<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

echo "=== Debug Product ID Issue ===\n\n";

// Create test outlet
$outlet = Outlet::firstOrCreate(['code' => 'DEBUG001'], ['name' => 'Debug Outlet']);
echo "Using outlet: {$outlet->code} (ID: {$outlet->id})\n\n";

// Create a simple import instance
$import = new ReportStockBulkImport('2025-09-12');

// Test the processProduct method directly
echo "Testing processProduct method...\n";

// Simulate processing a new product
$barcode = 'DEBUG_BARCODE_001';
$name = 'Debug Product';
$unit = 'PCS';
$packQuantity = 1;

// Use reflection to access protected method
$reflection = new ReflectionClass($import);
$processProductMethod = $reflection->getMethod('processProduct');
$processProductMethod->setAccessible(true);

$product = $processProductMethod->invoke($import, $barcode, $name, $unit, $packQuantity);

echo "Product created with temporary ID: {$product->id}\n";
echo "Product barcode: {$product->barcode}\n";
echo "Product name: {$product->name}\n\n";

// Check the productsToInsert array
$productsToInsertProperty = $reflection->getProperty('productsToInsert');
$productsToInsertProperty->setAccessible(true);
$productsToInsert = $productsToInsertProperty->getValue($import);

echo "Products to insert:\n";
print_r($productsToInsert);
echo "\n";

// Test processOutletProduct method
echo "Testing processOutletProduct method...\n";

$processOutletProductMethod = $reflection->getMethod('processOutletProduct');
$processOutletProductMethod->setAccessible(true);

$processOutletProductMethod->invoke($import, $outlet->id, $product->id, 'A');

// Check the outletProductsToInsert array
$outletProductsToInsertProperty = $reflection->getProperty('outletProductsToInsert');
$outletProductsToInsertProperty->setAccessible(true);
$outletProductsToInsert = $outletProductsToInsertProperty->getValue($import);

echo "Outlet products to insert:\n";
print_r($outletProductsToInsert);
echo "\n";

// Now test the batch insert process
echo "Testing batch insert process...\n";

try {
    // Insert the products first
    if (!empty($productsToInsert)) {
        Product::insert($productsToInsert);
        echo "✅ Products inserted successfully\n";
        
        // Check if products were actually inserted
        $insertedProduct = Product::where('barcode', $barcode)->first();
        if ($insertedProduct) {
            echo "✅ Product found in database with ID: {$insertedProduct->id}\n";
        } else {
            echo "❌ Product not found in database\n";
        }
    }
    
    // Test updateProductCacheWithRealIds
    $updateProductCacheMethod = $reflection->getMethod('updateProductCacheWithRealIds');
    $updateProductCacheMethod->setAccessible(true);
    $updateProductCacheMethod->invoke($import);
    
    echo "✅ Product cache updated\n";
    
    // Check the product cache
    $productCacheProperty = $reflection->getProperty('productCache');
    $productCacheProperty->setAccessible(true);
    $productCache = $productCacheProperty->getValue($import);
    
    if (isset($productCache[$barcode])) {
        echo "Product in cache - ID: {$productCache[$barcode]->id}\n";
    } else {
        echo "❌ Product not found in cache\n";
    }
    
    // Test updateOutletProductsWithRealProductIds
    $updateOutletProductsMethod = $reflection->getMethod('updateOutletProductsWithRealProductIds');
    $updateOutletProductsMethod->setAccessible(true);
    $updateOutletProductsMethod->invoke($import);
    
    echo "✅ Outlet products updated\n";
    
    // Check the updated outletProductsToInsert array
    $updatedOutletProductsToInsert = $outletProductsToInsertProperty->getValue($import);
    echo "Updated outlet products to insert:\n";
    print_r($updatedOutletProductsToInsert);
    echo "\n";
    
    // Try to insert outlet products
    if (!empty($updatedOutletProductsToInsert)) {
        // Validate product IDs first
        foreach ($updatedOutletProductsToInsert as $op) {
            if (!is_numeric($op['product_id']) || $op['product_id'] <= 0) {
                echo "❌ Invalid product_id: {$op['product_id']}\n";
            } else {
                echo "✅ Valid product_id: {$op['product_id']}\n";
            }
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
Product::where('barcode', $barcode)->delete();
if ($outlet->code === 'DEBUG001') {
    $outlet->delete();
}
echo "✅ Cleanup completed\n";

echo "\n=== Debug Completed ===\n";
