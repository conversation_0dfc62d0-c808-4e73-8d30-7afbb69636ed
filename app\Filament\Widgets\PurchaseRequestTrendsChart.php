<?php

namespace App\Filament\Widgets;

use App\Models\PurchaseRequest;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class PurchaseRequestTrendsChart extends ChartWidget
{
    protected static ?int $sort = 3;

    public function getHeading(): ?string
    {
        return 'Purchase Requests Trend';
    }

    protected function getData(): array
    {
        $user = Auth::user();
        
        // Get data for the last 12 months
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $months->push(now()->subMonths($i));
        }

        $data = $months->map(function ($month) use ($user) {
            $query = PurchaseRequest::whereYear('purchase_request_date', $month->year)
                                   ->whereMonth('purchase_request_date', $month->month);

            if ($user->hasRole('admin') && $user->outlet_id) {
                $query->whereHas('details', function ($detailQuery) use ($user) {
                    $detailQuery->where('outlet_id', $user->outlet_id);
                });
            }

            return $query->count();
        });

        return [
            'datasets' => [
                [
                    'label' => 'Purchase Requests',
                    'data' => $data->toArray(),
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months->map(fn ($month) => $month->format('M Y'))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
