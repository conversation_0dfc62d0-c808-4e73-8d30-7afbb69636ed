<?php

namespace Database\Factories;

use App\Models\Outlet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Outlet>
 */
class OutletFactory extends Factory
{
    protected $model = Outlet::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cities = [
            'Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang', 
            'Makassar', 'Palembang', 'Tangerang', 'Depok', 'Bekasi',
            'Bogor', 'Yogyakarta', 'Malang', 'Solo', 'Balikpapan'
        ];

        $areas = [
            'Pusat', 'Utara', 'Selatan', 'Timur', 'Barat',
            'Kota', 'Mall', 'Plaza', 'Square', 'Center'
        ];

        $city = $this->faker->randomElement($cities);
        $area = $this->faker->randomElement($areas);
        
        return [
            'name' => "Apotek Keluarga {$city} {$area}",
            'code' => strtoupper(substr($city, 0, 3) . substr($area, 0, 2) . $this->faker->unique()->numberBetween(100, 999)),
        ];
    }

    /**
     * Create an outlet with a specific city.
     */
    public function city(string $city): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => "Apotek Keluarga {$city} " . $this->faker->randomElement(['Pusat', 'Utara', 'Selatan']),
            'code' => strtoupper(substr($city, 0, 3) . $this->faker->randomElement(['PUS', 'UTR', 'SEL']) . $this->faker->unique()->numberBetween(100, 999)),
        ]);
    }

    /**
     * Create an outlet with a specific code pattern.
     */
    public function withCode(string $prefix): static
    {
        return $this->state(fn (array $attributes) => [
            'code' => strtoupper($prefix . $this->faker->unique()->numberBetween(100, 999)),
        ]);
    }

    /**
     * Create a main/central outlet.
     */
    public function main(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Apotek Keluarga Pusat',
            'code' => 'AKP001',
        ]);
    }
}
