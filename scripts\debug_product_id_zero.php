<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\OutletProduct;
use App\Models\ReportStockDetail;
use App\Models\Product;
use App\Models\Outlet;
use Illuminate\Support\Facades\DB;

echo "=== Debug Product ID = 0 Issue ===\n\n";

// Check for outlet products with product_id = 0
echo "1. Checking OutletProducts with product_id = 0:\n";
$outletProductsWithZero = OutletProduct::where('product_id', 0)->get();
echo "Found: " . $outletProductsWithZero->count() . " records\n";

if ($outletProductsWithZero->count() > 0) {
    echo "Sample records:\n";
    foreach ($outletProductsWithZero->take(5) as $op) {
        echo "  ID: {$op->id}, outlet_id: {$op->outlet_id}, product_id: {$op->product_id}, created_at: {$op->created_at}\n";
    }
}

// Check for report stock details with product_id = 0
echo "\n2. Checking ReportStockDetails with product_id = 0:\n";
$reportStockDetailsWithZero = ReportStockDetail::where('product_id', 0)->get();
echo "Found: " . $reportStockDetailsWithZero->count() . " records\n";

if ($reportStockDetailsWithZero->count() > 0) {
    echo "Sample records:\n";
    foreach ($reportStockDetailsWithZero->take(5) as $rsd) {
        echo "  ID: {$rsd->id}, outlet_id: {$rsd->outlet_id}, product_id: {$rsd->product_id}, quantity: {$rsd->quantity}, created_at: {$rsd->created_at}\n";
    }
}

// Check for products with empty or null barcodes
echo "\n3. Checking Products with empty/null barcodes:\n";
$productsWithEmptyBarcode = Product::whereNull('barcode')
    ->orWhere('barcode', '')
    ->orWhere('barcode', '0')
    ->get();
echo "Found: " . $productsWithEmptyBarcode->count() . " records\n";

if ($productsWithEmptyBarcode->count() > 0) {
    echo "Sample records:\n";
    foreach ($productsWithEmptyBarcode->take(5) as $product) {
        echo "  ID: {$product->id}, name: {$product->name}, barcode: '{$product->barcode}'\n";
    }
}

// Check for duplicate barcodes
echo "\n4. Checking for duplicate barcodes:\n";
$duplicateBarcodes = DB::table('products')
    ->select('barcode', DB::raw('COUNT(*) as count'))
    ->whereNotNull('barcode')
    ->where('barcode', '!=', '')
    ->groupBy('barcode')
    ->having('count', '>', 1)
    ->get();

echo "Found: " . $duplicateBarcodes->count() . " duplicate barcodes\n";

if ($duplicateBarcodes->count() > 0) {
    echo "Sample duplicates:\n";
    foreach ($duplicateBarcodes->take(5) as $dup) {
        echo "  Barcode: '{$dup->barcode}', Count: {$dup->count}\n";
        
        // Show the actual products with this barcode
        $products = Product::where('barcode', $dup->barcode)->get();
        foreach ($products as $product) {
            echo "    - ID: {$product->id}, Name: {$product->name}\n";
        }
    }
}

// Check recent imports
echo "\n5. Checking recent imports (last 24 hours):\n";
$recentOutletProducts = OutletProduct::where('created_at', '>=', now()->subDay())->count();
$recentReportStockDetails = ReportStockDetail::where('created_at', '>=', now()->subDay())->count();
$recentProducts = Product::where('created_at', '>=', now()->subDay())->count();

echo "Recent OutletProducts: {$recentOutletProducts}\n";
echo "Recent ReportStockDetails: {$recentReportStockDetails}\n";
echo "Recent Products: {$recentProducts}\n";

// Check for orphaned outlet products (product_id doesn't exist in products table)
echo "\n6. Checking for orphaned OutletProducts:\n";
$orphanedOutletProducts = DB::table('outlet_products as op')
    ->leftJoin('products as p', 'op.product_id', '=', 'p.id')
    ->whereNull('p.id')
    ->where('op.product_id', '!=', 0) // Exclude the known zeros
    ->count();

echo "Found: {$orphanedOutletProducts} orphaned outlet products\n";

// Check for orphaned report stock details
echo "\n7. Checking for orphaned ReportStockDetails:\n";
$orphanedReportStockDetails = DB::table('report_stock_details as rsd')
    ->leftJoin('products as p', 'rsd.product_id', '=', 'p.id')
    ->whereNull('p.id')
    ->where('rsd.product_id', '!=', 0) // Exclude the known zeros
    ->count();

echo "Found: {$orphanedReportStockDetails} orphaned report stock details\n";

echo "\n=== Debug Complete ===\n";

// Suggest cleanup if needed
if ($outletProductsWithZero->count() > 0 || $reportStockDetailsWithZero->count() > 0) {
    echo "\n=== CLEANUP SUGGESTIONS ===\n";
    echo "To clean up records with product_id = 0:\n";
    echo "1. Delete OutletProducts: DELETE FROM outlet_products WHERE product_id = 0;\n";
    echo "2. Delete ReportStockDetails: DELETE FROM report_stock_details WHERE product_id = 0;\n";
    echo "\nWARNING: Make sure to backup your database before running cleanup commands!\n";
}
