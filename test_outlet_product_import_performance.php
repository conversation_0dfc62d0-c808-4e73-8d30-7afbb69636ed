<?php

require_once 'vendor/autoload.php';

use App\Imports\OutletProductImport;
use App\Services\ImportOptimizationService;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

function generateTestData($filename, $rows = 10000)
{
    echo "Generating test data with {$rows} rows...\n";
    
    $outlets = ['AK01', 'AK02', 'AK03', 'AK04', 'AK05'];
    $categories = ['FM', 'SM', 'BM'];
    $units = ['tablet', 'capsule', 'bottle', 'box', 'strip'];
    
    $file = fopen($filename, 'w');
    
    // Header
    fputcsv($file, [
        'outlet_code',
        'barcode', 
        'product_name',
        'unit',
        'pack_quantity',
        'outlet_pareto',
        'rumus_pareto',
        'min_buffer',
        'max_buffer'
    ]);
    
    // Data rows
    for ($i = 1; $i <= $rows; $i++) {
        $barcode = 'TEST' . str_pad($i, 8, '0', STR_PAD_LEFT);
        $productName = "Test Product {$i}";
        $outlet = $outlets[array_rand($outlets)];
        $category = $categories[array_rand($categories)];
        $unit = $units[array_rand($units)];
        
        fputcsv($file, [
            $outlet,
            $barcode,
            $productName,
            $unit,
            rand(1, 20),
            $category,
            $category,
            rand(5, 50),
            rand(51, 200)
        ]);
    }
    
    fclose($file);
    echo "Test data generated: {$filename}\n";
}

function testImportPerformance($filename, $testName)
{
    echo "\n=== Testing {$testName} ===\n";
    
    $optimizationService = new ImportOptimizationService();
    
    try {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        echo "Starting import...\n";
        echo "Initial memory: " . formatBytes($startMemory) . "\n";
        
        $import = new OutletProductImport();
        
        // Use optimization service
        $optimizationService->optimizeForImport('outlet_products');
        
        Excel::import($import, $filename);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $duration = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;
        
        echo "\n=== Import Results ===\n";
        echo "Duration: " . number_format($duration, 2) . " seconds\n";
        echo "Memory used: " . formatBytes($memoryUsed) . "\n";
        echo "Peak memory: " . formatBytes($peakMemory) . "\n";
        echo "Processed rows: " . $import->getProcessedRows() . "\n";
        echo "Products created: " . $import->getProductsCreated() . "\n";
        echo "Outlet products created: " . $import->getOutletProductsCreated() . "\n";
        echo "Outlet products updated: " . $import->getOutletProductsUpdated() . "\n";
        echo "Errors: " . count($import->getErrors()) . "\n";
        
        if (!empty($import->getErrors())) {
            echo "\nFirst 5 errors:\n";
            foreach (array_slice($import->getErrors(), 0, 5) as $error) {
                echo "- {$error}\n";
            }
        }
        
        // Calculate performance metrics
        $rowsPerSecond = $import->getProcessedRows() / $duration;
        $memoryPerRow = $memoryUsed / $import->getProcessedRows();
        
        echo "\n=== Performance Metrics ===\n";
        echo "Rows per second: " . number_format($rowsPerSecond, 2) . "\n";
        echo "Memory per row: " . formatBytes($memoryPerRow) . "\n";
        
        $optimizationService->restoreSettings();
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
        $optimizationService->restoreSettings();
    }
}

function formatBytes($bytes, $precision = 2)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Test scenarios
$testScenarios = [
    ['rows' => 1000, 'name' => '1K rows'],
    ['rows' => 5000, 'name' => '5K rows'],
    ['rows' => 10000, 'name' => '10K rows'],
    ['rows' => 25000, 'name' => '25K rows'],
];

foreach ($testScenarios as $scenario) {
    $filename = "test_outlet_products_{$scenario['rows']}.csv";
    
    // Generate test data
    generateTestData($filename, $scenario['rows']);
    
    // Test import performance
    testImportPerformance($filename, $scenario['name']);
    
    // Clean up
    if (file_exists($filename)) {
        unlink($filename);
    }
    
    // Wait a bit between tests
    sleep(2);
}

echo "\n=== Performance Testing Completed ===\n";
