<?php

namespace App\Services;

use App\Models\DailyStock;
use App\Models\ProductOutletSetting;
use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestDetail;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class PurchaseCalculationService
{
    /**
     * Generate purchase request from daily stock data
     */
    public function generatePurchaseRequest(int $outletId, Carbon $stockDate, int $userId): PurchaseRequest
    {
        // Get all daily stocks for the outlet on the specified date
        $dailyStocks = DailyStock::where('outlet_id', $outletId)
            ->where('stock_date', $stockDate)
            ->with(['product', 'product.outletSettings' => function ($query) use ($outletId) {
                $query->where('outlet_id', $outletId);
            }])
            ->get();

        // Create purchase request
        $purchaseRequest = PurchaseRequest::create([
            'request_number' => PurchaseRequest::generateRequestNumber(),
            'outlet_id' => $outletId,
            'user_id' => $userId,
            'request_date' => now()->toDateString(),
            'status' => PurchaseRequest::STATUS_DRAFT,
        ]);

        $totalAmount = 0;

        foreach ($dailyStocks as $dailyStock) {
            $product = $dailyStock->product;
            $setting = $product->outletSettings->first();

            if (!$setting) {
                // Skip products without settings
                continue;
            }

            $purchaseQuantity = $this->calculatePurchaseQuantity(
                $dailyStock->quantity,
                $setting->rumus_pareto,
                $setting->min_buffer,
                $setting->max_buffer,
                $dailyStock->sales_quantity
            );

            if ($purchaseQuantity > 0) {
                $unitPrice = $product->price ?? 0;
                $totalPrice = $purchaseQuantity * $unitPrice;

                PurchaseRequestDetail::create([
                    'purchase_request_id' => $purchaseRequest->id,
                    'product_id' => $product->id,
                    'current_stock' => $dailyStock->quantity,
                    'purchase_quantity' => $purchaseQuantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                ]);

                $totalAmount += $totalPrice;
            }
        }

        // Update total amount
        $purchaseRequest->update(['total_amount' => $totalAmount]);

        return $purchaseRequest;
    }

    /**
     * Calculate purchase quantity based on Pareto formula
     */
    private function calculatePurchaseQuantity(
        float $currentStock,
        string $paretoFormula,
        float $minBuffer,
        float $maxBuffer,
        float $salesQuantity
    ): float {
        // Base calculation: if current stock is below minimum buffer, we need to purchase
        if ($currentStock >= $minBuffer) {
            return 0; // No need to purchase
        }

        // Calculate target stock based on Pareto formula
        $targetStock = $this->calculateTargetStock($paretoFormula, $minBuffer, $maxBuffer, $salesQuantity);
        
        // Purchase quantity is the difference between target and current stock
        $purchaseQuantity = $targetStock - $currentStock;

        // Apply rounding based on Pareto formula
        return $this->applyRounding($purchaseQuantity, $paretoFormula);
    }

    /**
     * Calculate target stock based on Pareto formula
     */
    private function calculateTargetStock(string $paretoFormula, float $minBuffer, float $maxBuffer, float $salesQuantity): float
    {
        switch ($paretoFormula) {
            case ProductOutletSetting::PARETO_FAST_MOVING:
                // Fast moving: target closer to max buffer, consider sales velocity
                return $maxBuffer + ($salesQuantity * 0.5); // Add 50% of daily sales as safety stock
                
            case ProductOutletSetting::PARETO_BIG_MOVING:
                // Big moving: target between min and max buffer
                return ($minBuffer + $maxBuffer) / 2 + ($salesQuantity * 0.3); // Add 30% of daily sales
                
            case ProductOutletSetting::PARETO_SLOW_MOVING:
            default:
                // Slow moving: target closer to min buffer
                return $minBuffer + ($salesQuantity * 0.1); // Add 10% of daily sales
        }
    }

    /**
     * Apply rounding based on Pareto formula
     */
    private function applyRounding(float $quantity, string $paretoFormula): float
    {
        switch ($paretoFormula) {
            case ProductOutletSetting::PARETO_FAST_MOVING:
                // Fast moving: round up to ensure availability
                return ceil($quantity);
                
            case ProductOutletSetting::PARETO_BIG_MOVING:
                // Big moving: round to nearest whole number
                return round($quantity);
                
            case ProductOutletSetting::PARETO_SLOW_MOVING:
            default:
                // Slow moving: round down to minimize inventory
                return floor($quantity);
        }
    }

    /**
     * Get purchase recommendations for an outlet
     */
    public function getPurchaseRecommendations(int $outletId, Carbon $stockDate): Collection
    {
        $dailyStocks = DailyStock::where('outlet_id', $outletId)
            ->where('stock_date', $stockDate)
            ->with(['product', 'product.outletSettings' => function ($query) use ($outletId) {
                $query->where('outlet_id', $outletId);
            }])
            ->get();

        return $dailyStocks->map(function ($dailyStock) {
            $product = $dailyStock->product;
            $setting = $product->outletSettings->first();

            if (!$setting) {
                return null;
            }

            $purchaseQuantity = $this->calculatePurchaseQuantity(
                $dailyStock->quantity,
                $setting->rumus_pareto,
                $setting->min_buffer,
                $setting->max_buffer,
                $dailyStock->sales_quantity
            );

            return [
                'product' => $product,
                'current_stock' => $dailyStock->quantity,
                'min_buffer' => $setting->min_buffer,
                'max_buffer' => $setting->max_buffer,
                'pareto_formula' => $setting->rumus_pareto,
                'recommended_quantity' => $purchaseQuantity,
                'estimated_cost' => $purchaseQuantity * ($product->price ?? 0),
            ];
        })->filter()->values();
    }
}
