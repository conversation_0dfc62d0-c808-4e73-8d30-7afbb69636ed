<?php

require __DIR__ . '/../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

$path = $argv[1] ?? 'D:\laragon\www\apotek-keluarga\REKAP SOH OUTLET 07092025.xlsx';

if (!file_exists($path)) {
    echo "File not found: {$path}\n";
    exit(1);
}

echo "=== Excel File Inspector (Direct) ===\n";
echo "File: {$path}\n";
echo "Size: " . round(filesize($path) / 1024 / 1024, 2) . " MB\n\n";

try {
    $reader = IOFactory::createReader('Xlsx');
    $reader->setReadDataOnly(true);
    $spreadsheet = $reader->load($path);
    $worksheet = $spreadsheet->getActiveSheet();
    
    $highestRow = $worksheet->getHighestRow();
    $highestColumn = $worksheet->getHighestColumn();
    
    echo "Worksheet info:\n";
    echo "  Highest row: {$highestRow}\n";
    echo "  Highest column: {$highestColumn}\n\n";
    
    // Get headers (first row)
    echo "Headers (Row 1):\n";
    $headers = [];
    for ($col = 'A'; $col <= $highestColumn; $col++) {
        $value = $worksheet->getCell($col . '1')->getCalculatedValue();
        $headers[$col] = $value;
        echo "  [{$col}] '{$value}'\n";
    }
    
    // Get sample data (rows 2-6)
    echo "\nSample data (rows 2-6):\n";
    for ($row = 2; $row <= min(6, $highestRow); $row++) {
        echo "\nRow {$row}:\n";
        for ($col = 'A'; $col <= $highestColumn; $col++) {
            $value = $worksheet->getCell($col . $row)->getCalculatedValue();
            $header = $headers[$col] ?? "Col{$col}";
            $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "'{$value}'" : $value);
            echo "  {$header} ({$col}): {$displayValue}\n";
        }
    }
    
    $spreadsheet->disconnectWorksheets();
    unset($spreadsheet);
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
