<?php

echo "🔍 Debugging Page Errors...\n";
echo "===========================\n\n";

$testUrl = "http://127.0.0.1:8000/admin";

echo "Testing URL: {$testUrl}\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_USERAGENT, 'Debug Tester');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "cURL Error: " . ($error ?: 'None') . "\n";
echo "Response Length: " . strlen($response) . " chars\n\n";

// Look for specific error patterns
$errorPatterns = [
    'Error' => '/Error/i',
    'Exception' => '/Exception/i',
    'Fatal error' => '/Fatal error/i',
    'Parse error' => '/Parse error/i',
    'Call to undefined' => '/Call to undefined/i',
    'Class not found' => '/Class.*not found/i',
    'Method not found' => '/Method.*not found/i',
    'Property not found' => '/Property.*not found/i'
];

echo "🔍 Checking for error patterns:\n";
foreach ($errorPatterns as $name => $pattern) {
    if (preg_match($pattern, $response)) {
        echo "   ❌ Found: {$name}\n";
        
        // Extract context around the error
        preg_match($pattern . '.*$/m', $response, $matches);
        if (!empty($matches[0])) {
            echo "      Context: " . substr($matches[0], 0, 200) . "...\n";
        }
    } else {
        echo "   ✅ Not found: {$name}\n";
    }
}

// Check for specific content
echo "\n🔍 Checking for specific content:\n";
$contentChecks = [
    'HTML' => '/<html/i',
    'DOCTYPE' => '/<!DOCTYPE/i',
    'Filament' => '/filament/i',
    'Dashboard' => '/dashboard/i',
    'Login' => '/login/i',
    'Admin' => '/admin/i'
];

foreach ($contentChecks as $name => $pattern) {
    if (preg_match($pattern, $response)) {
        echo "   ✅ Found: {$name}\n";
    } else {
        echo "   ❌ Not found: {$name}\n";
    }
}

// Show first 1000 characters of response
echo "\n📄 First 1000 characters of response:\n";
echo str_repeat("-", 50) . "\n";
echo substr($response, 0, 1000);
echo "\n" . str_repeat("-", 50) . "\n";

// Show last 500 characters of response
echo "\n📄 Last 500 characters of response:\n";
echo str_repeat("-", 50) . "\n";
echo substr($response, -500);
echo "\n" . str_repeat("-", 50) . "\n";
