<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            
            Actions\Action::make('reset_password')
                ->label('Reset Password')
                ->icon('heroicon-o-key')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Reset User Password')
                ->modalDescription('This will reset the user password to "password123". The user will need to change their password after login.')
                ->modalSubmitActionLabel('Reset Password')
                ->action(function () {
                    $this->record->update([
                        'password' => Hash::make('password123')
                    ]);
                    
                    Notification::make()
                        ->title('Password Reset')
                        ->body("Password has been reset to 'password123'")
                        ->warning()
                        ->send();
                }),
                
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading('Delete User')
                ->modalDescription('Are you sure you want to delete this user? This action cannot be undone.')
                ->modalSubmitActionLabel('Yes, delete user'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('User Updated')
            ->body('The user has been updated successfully.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // If manager role is selected, ensure outlet_id is null
        if (isset($data['roles'])) {
            $roleNames = \Spatie\Permission\Models\Role::whereIn('id', $data['roles'])->pluck('name')->toArray();
            if (in_array('manager', $roleNames)) {
                $data['outlet_id'] = null;
            }
        }

        return $data;
    }

    protected function afterSave(): void
    {
        $user = $this->record;
        
        // Log user update
        activity()
            ->performedOn($user)
            ->causedBy(auth()->user())
            ->log("User '{$user->name}' was updated");
    }
}
