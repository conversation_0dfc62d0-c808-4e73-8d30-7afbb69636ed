<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Facades\Auth;

class ProductsExport implements FromQuery, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithMapping
{
    protected $outletId;

    public function __construct($outletId = null)
    {
        $this->outletId = $outletId;
    }

    public function query()
    {
        $query = Product::query()->with('outlets');

        // Apply outlet filtering based on user role
        if ($this->outletId) {
            $query->whereHas('outlets', function ($q) {
                $q->where('outlet_id', $this->outletId);
            });
        } elseif (Auth::user()?->hasRole('admin')) {
            $query->whereHas('outlets', function ($q) {
                $q->where('outlet_id', Auth::user()->outlet_id);
            });
        }

        return $query->orderBy('name');
    }

    public function map($product): array
    {
        return [
            $product->outlet->name ?? 'Unknown',
            $product->name,
            $product->barcode,
            $product->unit,
            $product->packaging,
            $product->outlet_pareto ?? 'N/A',
            $product->rumus_pareto ?? 'N/A',
            $product->min_buffer,
            $product->max_buffer,
            $product->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function headings(): array
    {
        return [
            'Outlet',
            'Product Name',
            'Barcode',
            'Unit',
            'Packaging',
            'Outlet Pareto',
            'Rumus Pareto',
            'Min Buffer',
            'Max Buffer',
            'Created At',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '3B82F6'], // Blue color
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25, // outlet
            'B' => 35, // product name
            'C' => 20, // barcode
            'D' => 15, // unit
            'E' => 15, // packaging
            'F' => 15, // outlet_pareto
            'G' => 15, // rumus_pareto
            'H' => 12, // min_buffer
            'I' => 12, // max_buffer
            'J' => 20, // created_at
        ];
    }

    public function title(): string
    {
        if ($this->outletId) {
            $outlet = \App\Models\Outlet::find($this->outletId);
            return 'Products - ' . ($outlet->name ?? 'Unknown Outlet');
        }
        
        return 'Products - All Outlets';
    }
}
