# Report Stock Import Documentation

## Overview
Fitur import report stock memungkinkan Anda untuk mengimpor laporan stok dalam jumlah besar menggunakan file Excel. Fitur ini dapat membuat produk baru secara otomatis jika produk dengan barcode tertentu belum ada dalam sistem, dan memungkinkan update data stok untuk tanggal tertentu.

## Fitur Utama

### 1. Group by Outlet
- Tabel report stock sekarang menampilkan data yang dikelompokkan berdasarkan outlet
- Default sorting: Outlet → Report Date (terbaru) → Product
- Kolom tambahan menampilkan informasi pareto dan buffer dari konfigurasi outlet product

### 2. Import Bulk Data
- Import data stok untuk multiple outlet dan produk sekaligus
- Pilih tanggal laporan untuk semua data yang diimpor
- Otomatis membuat produk baru jika belum ada
- Update data existing jika kombinasi outlet + produk + tanggal sudah ada

## Format Excel

### Kolom yang <PERSON>

| Kolom | Nama Field | Tipe | Wajib | Deskripsi |
|-------|------------|------|-------|-----------|
| A | outlet_code | String | Ya | Kode outlet yang sudah ada di sistem |
| B | barcode | String | Ya | Barcode produk (akan dibuat jika belum ada) |
| C | product_name | String | Ya | Nama produk |
| D | unit | String | Tidak | Satuan produk (default: 'pcs') |
| E | pack_quantity | Integer | Tidak | Jumlah per kemasan (default: 1) |
| F | quantity | Integer | Ya | Jumlah stok (bisa 0 untuk out of stock) |

### Contoh Data

```
outlet_code | barcode     | product_name        | unit    | pack_quantity | quantity
AK01        | PROD001     | Paracetamol 500mg   | tablet  | 10           | 25
AK01        | PROD002     | Vitamin C           | bottle  | 1            | 15
AK02        | PROD003     | Antasida            | box     | 12           | 8
AK02        | PROD001     | Paracetamol 500mg   | tablet  | 10           | 30
AK03        | PROD004     | Antibiotik          | capsule | 20           | 0
```

## Cara Menggunakan

### 1. Download Template
1. Buka halaman **Report Stocks**
2. Klik tombol **"Download Template"**
3. File template Excel akan diunduh dengan format yang benar

### 2. Isi Data
1. Buka file template yang sudah diunduh
2. Hapus baris contoh (baris 2-6)
3. Isi data sesuai dengan format yang ditentukan
4. Pastikan:
   - `outlet_code` sesuai dengan kode outlet yang ada di sistem
   - `barcode` unik untuk setiap produk
   - `product_name` tidak kosong
   - `quantity` berupa angka (bisa 0)

### 3. Import Data
1. Klik tombol **"Import Report Stocks"**
2. Pilih **tanggal laporan** (semua data akan menggunakan tanggal ini)
3. Upload file Excel yang sudah diisi
4. Klik **"Import"**
5. Tunggu proses selesai dan lihat notifikasi hasil

## Fitur Tabel yang Ditingkatkan

### 1. Kolom Quantity dengan Color Coding
- **Gray**: Quantity = 0 (out of stock)
- **Red**: Quantity < min_buffer (low stock)
- **Yellow**: Quantity ≤ (min_buffer × 2) (warning)
- **Green**: Quantity > (min_buffer × 2) (good stock)

### 2. Kolom Pareto Information
- **FM (Fast Moving)**: Badge hijau
- **SM (Slow Moving)**: Badge kuning
- **BM (Bad Moving)**: Badge merah
- **N/A**: Badge abu-abu (belum dikonfigurasi)

### 3. Buffer Information
- Menampilkan range buffer minimum dan maksimum
- Diambil dari konfigurasi outlet product
- Format: "Buffer: min - max"

### 4. Enhanced Product Information
- Nama produk dengan barcode dan unit
- Informasi outlet dengan kode outlet
- Deskripsi waktu relatif (e.g., "2 hours ago")

## Fitur Otomatis

### 1. Pembuatan Produk Baru
- Jika produk dengan barcode tertentu belum ada, sistem akan membuat produk baru
- Data produk diambil dari kolom: `product_name`, `unit`, `pack_quantity`

### 2. Update Produk Existing
- Jika produk sudah ada, sistem akan mengupdate data jika ada perbedaan
- Field yang diupdate: `name`, `unit`, `pack_quantity`

### 3. Update/Create Report Stock
- Sistem akan membuat atau mengupdate laporan stok
- Jika kombinasi outlet + produk + tanggal sudah ada, quantity akan diupdate
- Jika belum ada, akan dibuat laporan stok baru

### 4. Constraint Validation
- Unique constraint: `outlet_id` + `product_id` + `report_date`
- Mencegah duplikasi data untuk kombinasi yang sama

## Validasi Data

### Validasi Wajib
- `outlet_code`: Harus ada dan outlet harus exist di sistem
- `barcode`: Harus ada dan tidak boleh kosong
- `product_name`: Harus ada dan tidak boleh kosong
- `quantity`: Harus berupa angka non-negatif

### Validasi Opsional
- `pack_quantity`: Harus berupa angka positif (min: 1)
- `unit`: String (default: 'pcs')

## Error Handling

### Jenis Error yang Mungkin Terjadi
1. **Outlet tidak ditemukan**: Outlet dengan kode tertentu tidak ada di sistem
2. **Data tidak valid**: Format data tidak sesuai dengan validasi
3. **File corrupt**: File Excel rusak atau tidak bisa dibaca
4. **Constraint violation**: Pelanggaran constraint database

### Cara Mengatasi Error
1. Periksa kembali format data sesuai template
2. Pastikan semua outlet code sudah ada di sistem
3. Pastikan file Excel tidak corrupt
4. Periksa log error untuk detail masalah

## Tips Penggunaan

1. **Gunakan Template**: Selalu download template terbaru untuk memastikan format yang benar
2. **Pilih Tanggal yang Tepat**: Semua data akan menggunakan tanggal yang dipilih saat import
3. **Backup Data**: Backup data sebelum melakukan import besar-besaran
4. **Test dengan Data Kecil**: Coba import dengan beberapa baris data dulu
5. **Periksa Hasil**: Verifikasi data setelah import untuk memastikan akurasi
6. **Update Berkala**: Gunakan fitur ini untuk update stok harian/berkala

## Batasan

- Maximum file size: 10MB
- Maximum rows: 1000 baris per import
- Supported formats: .xlsx, .xls
- Batch processing: 100 rows per batch untuk performa optimal
- Satu tanggal laporan per import (tidak bisa mixed date)

## Integrasi dengan Fitur Lain

### 1. Outlet Product Configuration
- Data pareto dan buffer diambil dari konfigurasi outlet product
- Jika belum ada konfigurasi, akan ditampilkan "N/A"

### 2. Dashboard Widgets
- Data import akan langsung terlihat di dashboard widgets
- Stock overview akan terupdate otomatis

### 3. Purchase Request
- Data stok yang diimpor dapat digunakan untuk generate purchase request
- Sistem akan menganalisis stok vs buffer untuk rekomendasi pembelian
