<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use App\Models\ReportStock;
use Illuminate\Validation\ValidationException;

class CreateReportStock extends CreateRecord
{
    protected static string $resource = ReportStockResource::class;

    protected function handleRecordCreation(array $data): Model
    {


        // Check for duplicate report (report_date only, since outlet_id is now in details)
        $existingReport = ReportStock::where('report_date', $data['report_date'])
            ->first();

        if ($existingReport) {
            // Show user-friendly error notification
            Notification::make()
                ->title('Duplicate Report')
                ->body("A report for this outlet and date already exists (Report ID: {$existingReport->id}). Please choose a different date or edit the existing report.")
                ->danger()
                ->persistent()
                ->send();

            // Throw validation exception to prevent form submission
            throw ValidationException::withMessages([
                'report_date' => 'A report for this outlet and date already exists. Please choose a different date.',
            ]);
        }

        // Create the ReportStock record with additional error handling
        try {
            $record = static::getModel()::create($data);
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle database constraint violations
            if ($e->getCode() === '23000') { // Integrity constraint violation
                Notification::make()
                    ->title('Duplicate Report Error')
                    ->body('A report for this outlet and date already exists. Please choose a different date or edit the existing report.')
                    ->danger()
                    ->persistent()
                    ->send();

                throw ValidationException::withMessages([
                    'report_date' => 'A report for this outlet and date already exists.',
                ]);
            }

            // Re-throw other database exceptions
            throw $e;
        }

        return $record;
    }


}
