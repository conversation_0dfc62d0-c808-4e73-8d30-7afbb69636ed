<?php

namespace App\Filament\Widgets;

use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestDetail;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class PurchaseRequestsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();

        // Get purchase requests query
        $purchaseRequests = PurchaseRequest::query();

        // Filter by outlet if user is admin with specific outlet
        if ($user->hasRole('admin') && $user->outlet_id) {
            $outletId = $user->outlet_id;
            $purchaseRequests = $purchaseRequests->whereHas('details', function ($query) use ($outletId) {
                $query->where('outlet_id', $outletId);
            });
        }

        // Calculate statistics
        $totalRequests = $purchaseRequests->count();
        $thisMonthRequests = (clone $purchaseRequests)->whereMonth('purchase_request_date', now()->month)
                                            ->whereYear('purchase_request_date', now()->year)
                                            ->count();

        $todayRequests = (clone $purchaseRequests)->whereDate('purchase_request_date', today())->count();

        // Calculate total items from purchase request details
        if ($user->hasRole('admin') && $user->outlet_id) {
            $totalItems = PurchaseRequestDetail::where('outlet_id', $user->outlet_id)->sum('purchase_quantity');
        } else {
            $totalItems = PurchaseRequestDetail::sum('purchase_quantity');
        }

        return [
            Stat::make('Total Requests', number_format($totalRequests))
                ->description('All purchase requests')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary'),

            Stat::make('This Month', number_format($thisMonthRequests))
                ->description('Requests this month')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('success'),

            Stat::make('Today', number_format($todayRequests))
                ->description('Requests created today')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),

            Stat::make('Total Items', number_format($totalItems))
                ->description('Items requested')
                ->descriptionIcon('heroicon-m-squares-plus')
                ->color('warning'),
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
