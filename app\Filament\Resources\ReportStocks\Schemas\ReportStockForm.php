<?php

namespace App\Filament\Resources\ReportStocks\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Schemas\Schema;

class ReportStockForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                DatePicker::make('report_date')
                    ->label('Report Date')
                    ->default(now())
                    ->required()
                    ->helperText('Date of the stock report'),
            ]);
    }
}
