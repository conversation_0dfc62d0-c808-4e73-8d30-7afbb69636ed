<?php

namespace App\Filament\Widgets;

use App\Models\ReportStock;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class StockTrendsChart extends ChartWidget
{
    protected static ?int $sort = 2;

    public function getHeading(): ?string
    {
        return 'Stock Reports Trend';
    }

    protected function getData(): array
    {
        // Get data for the last 12 months
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $months->push(now()->subMonths($i));
        }

        $data = $months->map(function ($month) {
            $query = ReportStock::whereYear('report_date', $month->year)
                               ->whereMonth('report_date', $month->month);

            return $query->count();
        });

        return [
            'datasets' => [
                [
                    'label' => 'Stock Reports',
                    'data' => $data->toArray(),
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months->map(fn ($month) => $month->format('M Y'))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
