<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use Maatwebsite\Excel\Facades\Excel;

echo "=== Debug Optimized Import ===\n\n";

// Create simple test data
$testFile = 'debug_optimized.xlsx';
$reportDate = '2025-09-12';

$testData = [
    ['OUTLET', 'BARCODE', 'NAMA_PRODUK', 'SAT', 'PACK', 'QTY', 'PRT'],
    ['OUT001', '1234567890001', 'Test Product 1', 'PCS', '1,00', 10, 'A'],
    ['OUT001', '1234567890002', 'Test Product 2', 'BOX', '1,00', 5, 'B'],
];

echo "Creating simple test Excel...\n";

// Create Excel file
$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

foreach ($testData as $rowIndex => $rowData) {
    foreach ($rowData as $colIndex => $cellData) {
        $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
    }
}

$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
$writer->save($testFile);

echo "Test file created: {$testFile}\n\n";

// Test import with error details
echo "Starting import...\n";

try {
    $import = new ReportStockBulkImport($reportDate);
    Excel::import($import, $testFile);
    
    echo "Import completed!\n\n";
    
    echo "=== Statistics ===\n";
    echo "Processed rows: " . $import->getProcessedRows() . "\n";
    echo "Products created: " . $import->getProductsCreated() . "\n";
    echo "Errors: " . count($import->getErrors()) . "\n\n";
    
    if (!empty($import->getErrors())) {
        echo "=== First 5 Errors ===\n";
        foreach (array_slice($import->getErrors(), 0, 5) as $error) {
            echo "Row {$error['row']}: {$error['error']}\n";
            echo "Data: " . json_encode($error['data']) . "\n\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Cleanup
if (file_exists($testFile)) {
    unlink($testFile);
    echo "Cleaned up test file: {$testFile}\n";
}

echo "\n=== Debug Complete ===\n";
