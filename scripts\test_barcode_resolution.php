<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStockDetail;

echo "=== Testing Barcode Resolution Fix ===\n\n";

// Clean up any existing test data
Product::whereIn('barcode', ['8992806158738', 'NEW_BARCODE_001'])->delete();
$outlet = Outlet::firstOrCreate(['code' => 'BARCODE001'], ['name' => 'Barcode Test Outlet']);

echo "Using outlet: {$outlet->code} (ID: {$outlet->id})\n";

// Create an existing product to test mixed scenarios
$existingProduct = Product::create([
    'barcode' => '8992806158738',
    'name' => 'Existing Test Product',
    'unit' => 'PCS',
    'pack_quantity' => 1,
]);

echo "Created existing product: ID={$existingProduct->id}, Barcode={$existingProduct->barcode}\n\n";

// Get initial counts
$initialCounts = [
    'products' => Product::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

echo "Initial counts: Products={$initialCounts['products']}, OutletProducts={$initialCounts['outlet_products']}, ReportDetails={$initialCounts['report_stock_details']}\n\n";

// Create test data with both existing and new products
$testData = collect([
    [
        'outlet' => 'BARCODE001',
        'nama_produk' => 'Existing Test Product Updated',
        'prt' => 'BM',
        'barcode' => '8992806158738', // Existing product
        'pack' => '1',
        'qty' => 50,
        'sat' => 'PCS',
    ],
    [
        'outlet' => 'BARCODE001',
        'nama_produk' => 'New Test Product',
        'prt' => 'SM',
        'barcode' => 'NEW_BARCODE_001', // New product
        'pack' => '2',
        'qty' => 100,
        'sat' => 'BOX',
    ],
]);

echo "Testing import with mixed existing/new products...\n";

try {
    $import = new ReportStockBulkImport('2025-09-12');
    $import->collection($testData);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Get final counts
    $finalCounts = [
        'products' => Product::count(),
        'outlet_products' => OutletProduct::count(),
        'report_stock_details' => ReportStockDetail::count(),
    ];
    
    echo "Final counts: Products={$finalCounts['products']}, OutletProducts={$finalCounts['outlet_products']}, ReportDetails={$finalCounts['report_stock_details']}\n";
    
    $changes = [
        'products' => $finalCounts['products'] - $initialCounts['products'],
        'outlet_products' => $finalCounts['outlet_products'] - $initialCounts['outlet_products'],
        'report_stock_details' => $finalCounts['report_stock_details'] - $initialCounts['report_stock_details'],
    ];
    
    echo "Changes: Products=+{$changes['products']}, OutletProducts=+{$changes['outlet_products']}, ReportDetails=+{$changes['report_stock_details']}\n\n";
    
    // Verify the data
    echo "Verification:\n";
    
    // Check existing product
    $existingProductUpdated = Product::where('barcode', '8992806158738')->first();
    if ($existingProductUpdated) {
        echo "✅ Existing product: ID={$existingProductUpdated->id}, Name={$existingProductUpdated->name}\n";
        
        $existingOutletProduct = OutletProduct::where('outlet_id', $outlet->id)
            ->where('product_id', $existingProductUpdated->id)
            ->first();
        if ($existingOutletProduct) {
            echo "  ✅ Outlet product: Pareto={$existingOutletProduct->outlet_pareto}\n";
        } else {
            echo "  ❌ Outlet product not found\n";
        }
    }
    
    // Check new product
    $newProduct = Product::where('barcode', 'NEW_BARCODE_001')->first();
    if ($newProduct) {
        echo "✅ New product: ID={$newProduct->id}, Name={$newProduct->name}\n";
        
        $newOutletProduct = OutletProduct::where('outlet_id', $outlet->id)
            ->where('product_id', $newProduct->id)
            ->first();
        if ($newOutletProduct) {
            echo "  ✅ Outlet product: Pareto={$newOutletProduct->outlet_pareto}\n";
        } else {
            echo "  ❌ Outlet product not found\n";
        }
    }
    
    // Check report stock details
    $reportDetails = ReportStockDetail::where('outlet_id', $outlet->id)->get();
    echo "✅ Report stock details created: {$reportDetails->count()}\n";
    foreach ($reportDetails as $detail) {
        $product = Product::find($detail->product_id);
        echo "  - Product {$product->barcode}: Quantity={$detail->quantity}\n";
    }
    
    // Show import statistics
    $summary = $import->getImportSummary();
    echo "\nImport Summary:\n";
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "  {$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
    // Show the problematic SQL if available
    if (strpos($e->getMessage(), 'SQLSTATE') !== false) {
        echo "\nSQL Error Details:\n";
        echo $e->getMessage() . "\n";
    }
}

// Clean up
echo "\nCleaning up...\n";
Product::whereIn('barcode', ['8992806158738', 'NEW_BARCODE_001'])->delete();
if ($outlet->code === 'BARCODE001' && $outlet->outletProducts()->count() === 0) {
    $outlet->delete();
}
echo "✅ Cleanup completed\n";

echo "\n=== Test Completed ===\n";
