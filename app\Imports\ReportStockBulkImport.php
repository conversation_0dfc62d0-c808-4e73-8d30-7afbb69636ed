<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithValidation;

class ReportStockBulkImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading, SkipsEmptyRows, WithValidation
{
    protected string $reportDate;
    protected int $processedRows = 0;
    protected int $productsCreated = 0;
    protected int $outletProductsCreated = 0;
    protected int $outletProductsUpdated = 0;
    protected int $reportStocksCreated = 0;
    protected int $reportStocksUpdated = 0;
    protected int $reportStockDetailsCreated = 0;
    protected int $reportStockDetailsUpdated = 0;
    protected array $errors = [];
    protected array $skippedOutlets = [];

    // Caches untuk optimasi
    protected array $outletCache = [];
    protected array $productCache = [];
    protected array $outletProductCache = [];
    protected array $reportStockDetailCache = [];
    protected ?int $reportStockId = null;

    // Batch data untuk bulk insert
    protected array $productsToInsert = [];
    protected array $outletProductsToInsert = [];
    protected array $outletProductsToUpdate = [];
    protected array $reportStockDetailsToInsert = [];
    protected array $reportStockDetailsToUpdate = [];

    // Optimization service
    // protected ImportOptimizationService $optimizationService;

    public function __construct(string $reportDate)
    {
        $this->reportDate = $reportDate;
        // $this->optimizationService = new ImportOptimizationService();
    }

    public function collection(Collection $rows): void
    {
        // Optimize environment for import
        // $this->optimizationService->optimizeForImport('report_stock_bulk');

        try {
            $this->initializeReportStock();
            $this->preloadCaches();
            $this->processRows($rows);
            $this->processBatchInserts();
        } finally {
            // $this->optimizationService->restoreSettings();
        }
    }

    protected function initializeReportStock(): void
    {
        // Cek atau buat ReportStock untuk tanggal yang dipilih
        $reportStock = ReportStock::firstOrCreate(
            ['report_date' => $this->reportDate],
            ['is_generated' => false]
        );

        $this->reportStockId = $reportStock->id;

        if ($reportStock->wasRecentlyCreated) {
            $this->reportStocksCreated++;
            Log::info("Created new ReportStock for date: {$this->reportDate}");
        } else {
            $this->reportStocksUpdated++;
            Log::info("Using existing ReportStock for date: {$this->reportDate}");
        }
    }

    protected function preloadCaches(): void
    {
        // Preload outlets
        $outlets = Outlet::all(['id', 'code', 'name']);
        foreach ($outlets as $outlet) {
            $this->outletCache[$outlet->code] = $outlet;
        }

        // Preload products by barcode
        $products = Product::all(['id', 'barcode', 'name', 'unit', 'pack_quantity']);
        foreach ($products as $product) {
            $this->productCache[$product->barcode] = $product;
        }

        // Preload outlet products
        $outletProducts = OutletProduct::with(['outlet', 'product'])
            ->get(['id', 'outlet_id', 'product_id', 'outlet_pareto']);
        foreach ($outletProducts as $outletProduct) {
            $key = $outletProduct->outlet_id . '_' . $outletProduct->product_id;
            $this->outletProductCache[$key] = $outletProduct;
        }

        // Preload existing report stock details for this date
        $existingDetails = ReportStockDetail::where('report_stock_id', $this->reportStockId)
            ->with(['outlet', 'product'])
            ->get(['id', 'outlet_id', 'product_id', 'quantity']);
        foreach ($existingDetails as $detail) {
            $key = $detail->outlet_id . '_' . $detail->product_id;
            $this->reportStockDetailCache[$key] = $detail;
        }

        Log::info('Caches preloaded', [
            'outlets' => count($this->outletCache),
            'products' => count($this->productCache),
            'outlet_products' => count($this->outletProductCache),
            'report_stock_details' => count($this->reportStockDetailCache),
        ]);
    }

    protected function processRows(Collection $rows): void
    {
        foreach ($rows as $index => $row) {
            $this->processedRows++;

            try {
                $this->processRow($row, $index + 2); // +2 karena header di baris 1, data mulai baris 2
            } catch (\Exception $e) {
                $this->errors[] = [
                    'row' => $index + 2,
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ];
                Log::error("Error processing row " . ($index + 2), [
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ]);
            }

            // Log progress setiap 1000 baris
            if ($this->processedRows % 1000 === 0) {
                // $this->optimizationService->logProgress("Processed {$this->processedRows} rows", [
                //     'processed_rows' => $this->processedRows,
                //     'errors' => count($this->errors),
                // ]);
            }
        }
    }

    protected function processRow($row, int $rowNumber): void
    {
        // Validasi dan normalisasi data
        $outletCode = trim(strtoupper($row['outlet'] ?? ''));
        $productName = trim($row['nama_produk'] ?? '');
        $barcode = trim($row['barcode'] ?? '');
        $unit = trim($row['sat'] ?? '');
        $packQuantity = $this->parseNumeric($row['pack'] ?? 0);
        $quantity = $this->parseNumeric($row['qty'] ?? 0);
        $pareto = trim($row['prt'] ?? '');

        // Validasi data wajib
        if (empty($outletCode) || empty($barcode)) {
            throw new \Exception("Missing required data: outlet code or barcode at row {$rowNumber}");
        }

        // Validasi format barcode
        if (strlen($barcode) < 3) {
            throw new \Exception("Invalid barcode format: {$barcode} at row {$rowNumber}");
        }

        // Cek outlet
        if (!isset($this->outletCache[$outletCode])) {
            $this->skippedOutlets[] = $outletCode;
            throw new \Exception("Outlet not found: {$outletCode} at row {$rowNumber}");
        }

        $outlet = $this->outletCache[$outletCode];

        // Proses product
        $product = $this->processProduct($barcode, $productName, $unit, $packQuantity);

        // Proses outlet product
        $this->processOutletProduct($outlet->id, $product, $pareto);

        // Proses report stock detail
        $this->processReportStockDetail($outlet->id, $product, $quantity);
    }

    protected function processProduct(string $barcode, string $name, string $unit, int $packQuantity): Product
    {
        if (isset($this->productCache[$barcode])) {
            $product = $this->productCache[$barcode];

            // Update product jika ada perubahan
            $needsUpdate = false;
            $updates = [];

            if (!empty($name) && $product->name !== $name) {
                $updates['name'] = $name;
                $needsUpdate = true;
            }

            if (!empty($unit) && $product->unit !== $unit) {
                $updates['unit'] = $unit;
                $needsUpdate = true;
            }

            if ($packQuantity > 0 && $product->pack_quantity !== $packQuantity) {
                $updates['pack_quantity'] = $packQuantity;
                $needsUpdate = true;
            }

            if ($needsUpdate) {
                $product->update($updates);
                Log::info("Updated product: {$barcode}", $updates);
            }

            return $product;
        }

        // Buat product baru
        $productData = [
            'barcode' => $barcode,
            'name' => $name ?: "Product {$barcode}",
            'unit' => $unit ?: 'PCS',
            'pack_quantity' => $packQuantity ?: 1,
        ];

        $this->productsToInsert[] = $productData;

        // Buat object sementara untuk cache
        $product = new Product($productData);
        $product->barcode = $barcode; // Ensure barcode is set
        $this->productCache[$barcode] = $product;

        return $product;
    }

    protected function processOutletProduct(int $outletId, $productId, string $pareto): void
    {
        // Handle both Product objects (new products) and numeric IDs (existing products)
        if (is_object($productId)) {
            // New product - use barcode for cache key and storage
            $cacheKey = $outletId . '_' . $productId->barcode;
            $productIdForStorage = $productId->barcode;
        } else {
            // Existing product - use numeric ID
            $cacheKey = $outletId . '_' . $productId;
            $productIdForStorage = $productId;
        }

        if (isset($this->outletProductCache[$cacheKey])) {
            $outletProduct = $this->outletProductCache[$cacheKey];

            // Update pareto jika berbeda
            if (!empty($pareto) && $outletProduct->outlet_pareto !== $pareto) {
                $this->outletProductsToUpdate[] = [
                    'id' => $outletProduct->id,
                    'outlet_pareto' => $pareto,
                ];
                $outletProduct->outlet_pareto = $pareto;
            }
        } else {
            // Buat outlet product baru
            $outletProductData = [
                'outlet_id' => $outletId,
                'product_id' => $productIdForStorage, // Store barcode for new products, ID for existing
                'outlet_pareto' => $pareto,
                'rumus_pareto' => null,
                'min_buffer' => 0,
                'max_buffer' => 0,
            ];

            $this->outletProductsToInsert[] = $outletProductData;

            // Buat object sementara untuk cache
            $outletProduct = new OutletProduct($outletProductData);
            $this->outletProductCache[$cacheKey] = $outletProduct;
            $this->outletProductsCreated++;
        }
    }

    protected function processReportStockDetail(int $outletId, $productId, int $quantity): void
    {
        // Handle both Product objects (new products) and numeric IDs (existing products)
        if (is_object($productId)) {
            // New product - use barcode for cache key and storage
            $cacheKey = $outletId . '_' . $productId->barcode;
            $productIdForStorage = $productId->barcode;
        } else {
            // Existing product - use numeric ID
            $cacheKey = $outletId . '_' . $productId;
            $productIdForStorage = $productId;
        }

        if (isset($this->reportStockDetailCache[$cacheKey])) {
            $detail = $this->reportStockDetailCache[$cacheKey];

            // Update quantity jika berbeda
            if ($detail->quantity !== $quantity) {
                $this->reportStockDetailsToUpdate[] = [
                    'id' => $detail->id,
                    'quantity' => $quantity,
                ];
                $detail->quantity = $quantity;
                $this->reportStockDetailsUpdated++;
            }
        } else {
            // Buat report stock detail baru
            $detailData = [
                'report_stock_id' => $this->reportStockId,
                'outlet_id' => $outletId,
                'product_id' => $productIdForStorage, // Store barcode for new products, ID for existing
                'quantity' => $quantity,
            ];

            $this->reportStockDetailsToInsert[] = $detailData;

            // Buat object sementara untuk cache
            $detail = new ReportStockDetail($detailData);
            $this->reportStockDetailCache[$cacheKey] = $detail;
            $this->reportStockDetailsCreated++;
        }
    }

    protected function parseNumeric($value): int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        // Handle comma as decimal separator
        $cleaned = str_replace(',', '.', (string) $value);
        $cleaned = preg_replace('/[^\d.]/', '', $cleaned);

        return (int) floatval($cleaned);
    }

    protected function processBatchInserts(): void
    {
        DB::transaction(function () {
            // Insert products baru
            if (!empty($this->productsToInsert)) {
                $chunks = array_chunk($this->productsToInsert, 500);
                foreach ($chunks as $chunk) {
                    Product::insert($chunk);
                }
                $this->productsCreated = count($this->productsToInsert);

                // Update product cache dengan ID yang sebenarnya
                $this->updateProductCacheWithRealIds();

                // Update outlet products dan report stock details dengan product ID yang benar
                $this->updateOutletProductsWithRealProductIds();
                $this->updateReportStockDetailsWithRealProductIds();
            }

            // Insert outlet products baru
            if (!empty($this->outletProductsToInsert)) {
                // Validate all product IDs before insert
                $this->validateProductIds($this->outletProductsToInsert, 'outlet_products');

                $chunks = array_chunk($this->outletProductsToInsert, 500);
                foreach ($chunks as $chunk) {
                    OutletProduct::insert($chunk);
                }

            }

            // Update outlet products
            if (!empty($this->outletProductsToUpdate)) {
                foreach ($this->outletProductsToUpdate as $update) {
                    OutletProduct::where('id', $update['id'])
                        ->update(['outlet_pareto' => $update['outlet_pareto']]);
                }
                $this->outletProductsUpdated = count($this->outletProductsToUpdate);
            }

            // Insert report stock details baru
            if (!empty($this->reportStockDetailsToInsert)) {
                // Validate all product IDs before insert
                $this->validateProductIds($this->reportStockDetailsToInsert, 'report_stock_details');

                $chunks = array_chunk($this->reportStockDetailsToInsert, 500);
                foreach ($chunks as $chunk) {
                    ReportStockDetail::insert($chunk);
                }

            }

            // Update report stock details
            if (!empty($this->reportStockDetailsToUpdate)) {
                foreach ($this->reportStockDetailsToUpdate as $update) {
                    ReportStockDetail::where('id', $update['id'])
                        ->update(['quantity' => $update['quantity']]);
                }

            }
        });

        // Log ringkasan hasil import
        Log::info("Report Stock Import Completed", [
            'report_date' => $this->reportDate,
            'processed_rows' => $this->processedRows,
            'products_created' => $this->productsCreated,
            'outlet_products_created' => count($this->outletProductsToInsert),
            'outlet_products_updated' => $this->outletProductsUpdated,
            'report_stock_details_created' => count($this->reportStockDetailsToInsert),
            'report_stock_details_updated' => count($this->reportStockDetailsToUpdate),
            'errors_count' => count($this->errors),
            'skipped_outlets_count' => count($this->skippedOutlets),
        ]);
    }

    protected function updateProductCacheWithRealIds(): void
    {
        $barcodes = array_column($this->productsToInsert, 'barcode');
        $products = Product::whereIn('barcode', $barcodes)->get(['id', 'barcode']);

        foreach ($products as $product) {
            if (isset($this->productCache[$product->barcode])) {
                $this->productCache[$product->barcode]->id = $product->id;
            }
        }
    }

    protected function updateOutletProductsWithRealProductIds(): void
    {
        foreach ($this->outletProductsToInsert as $index => &$outletProduct) {
            $productId = $outletProduct['product_id'];

            // Check if product_id needs to be resolved (could be barcode)
            if (is_string($productId) && !empty($productId)) {
                // Check if this is actually a valid product ID or a barcode
                $isValidProductId = is_numeric($productId) && Product::where('id', $productId)->exists();

                if (!$isValidProductId) {
                    // This is a barcode that needs to be resolved
                    $barcode = $productId;

                    // Get the real product ID from database
                    $product = Product::where('barcode', $barcode)->first();
                    if ($product) {
                        $outletProduct['product_id'] = $product->id;
                    } else {
                        unset($this->outletProductsToInsert[$index]);
                        continue;
                    }
                }
                // If it's a valid product ID, leave it as is
            } elseif (empty($productId) || !is_numeric($productId)) {
                unset($this->outletProductsToInsert[$index]);
                continue;
            }
        }

        // Remove any unset items and reindex
        $this->outletProductsToInsert = array_values(array_filter($this->outletProductsToInsert));
    }

    protected function updateReportStockDetailsWithRealProductIds(): void
    {
        foreach ($this->reportStockDetailsToInsert as $index => &$detail) {
            $productId = $detail['product_id'];

            // Check if product_id needs to be resolved (could be barcode)
            if (is_string($productId) && !empty($productId)) {
                // Check if this is actually a valid product ID or a barcode
                $isValidProductId = is_numeric($productId) && Product::where('id', $productId)->exists();

                if (!$isValidProductId) {
                    // This is a barcode that needs to be resolved
                    $barcode = $productId;

                    // Get the real product ID from database
                    $product = Product::where('barcode', $barcode)->first();
                    if ($product) {
                        $detail['product_id'] = $product->id;
                    } else {
                        unset($this->reportStockDetailsToInsert[$index]);
                        continue;
                    }
                }
                // If it's a valid product ID, leave it as is
            } elseif (empty($productId) || !is_numeric($productId)) {
                unset($this->reportStockDetailsToInsert[$index]);
                continue;
            }
        }

        // Remove any unset items and reindex
        $this->reportStockDetailsToInsert = array_values(array_filter($this->reportStockDetailsToInsert));
    }

    protected function validateProductIds(array $data, string $context): void
    {
        foreach ($data as $index => $item) {
            $productId = $item['product_id'] ?? null;

            if (!is_numeric($productId) || $productId <= 0) {
                $productIdDisplay = $productId === null ? 'null' :
                                  ($productId === '' ? 'empty string' :
                                  (is_string($productId) ? "'{$productId}'" : $productId));

                throw new \Exception("Invalid product_id ({$productIdDisplay}) found in {$context} at index {$index}. This indicates a data processing error.");
            }
        }
    }

    // Laravel Excel methods
    public function batchSize(): int
    {
        return config('import.report_stock_bulk.batch_size', 500);
    }

    public function chunkSize(): int
    {
        return config('import.report_stock_bulk.chunk_size', 500);
    }

    public function rules(): array
    {
        return [
            'outlet' => 'required|string',
            'barcode' => 'required|string',
            'qty' => 'nullable|numeric',
        ];
    }

    // Getter methods untuk statistik
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getProductsCreated(): int
    {
        return $this->productsCreated;
    }

    public function getOutletProductsCreated(): int
    {
        return $this->outletProductsCreated;
    }

    public function getOutletProductsUpdated(): int
    {
        return $this->outletProductsUpdated;
    }

    public function getReportStocksCreated(): int
    {
        return $this->reportStocksCreated;
    }

    public function getReportStocksUpdated(): int
    {
        return $this->reportStocksUpdated;
    }

    public function getReportStockDetailsCreated(): int
    {
        return $this->reportStockDetailsCreated;
    }

    public function getReportStockDetailsUpdated(): int
    {
        return $this->reportStockDetailsUpdated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getSkippedOutlets(): array
    {
        return array_unique($this->skippedOutlets);
    }

    public function getImportSummary(): array
    {
        return [
            'processed_rows' => $this->processedRows,
            'products_created' => $this->productsCreated,
            'outlet_products_created' => $this->outletProductsCreated,
            'outlet_products_updated' => $this->outletProductsUpdated,
            'report_stocks_created' => $this->reportStocksCreated,
            'report_stocks_updated' => $this->reportStocksUpdated,
            'report_stock_details_created' => $this->reportStockDetailsCreated,
            'report_stock_details_updated' => $this->reportStockDetailsUpdated,
            'errors_count' => count($this->errors),
            'skipped_outlets' => $this->getSkippedOutlets(),
        ];
    }

    /**
     * Get template headers for Excel file
     */
    public static function getTemplateHeaders(): array
    {
        return [
            'OUTLET' => 'Kode outlet (wajib)',
            'NAMA PRODUK' => 'Nama produk',
            'PRT' => 'Outlet pareto (A/B/C)',
            'BARCODE' => 'Barcode produk (wajib)',
            'PACK' => 'Pack quantity',
            'QTY' => 'Quantity stock',
            'SAT' => 'Satuan unit',
        ];
    }

    /**
     * Validate import data before processing
     */
    public function validateImportData(Collection $rows): array
    {
        $errors = [];
        $requiredColumns = ['outlet', 'barcode'];

        if ($rows->isEmpty()) {
            $errors[] = 'File is empty or has no data rows';
            return $errors;
        }

        // Check if required columns exist
        $firstRow = $rows->first();
        foreach ($requiredColumns as $column) {
            if (!isset($firstRow[$column])) {
                $errors[] = "Missing required column: {$column}";
            }
        }

        return $errors;
    }
}