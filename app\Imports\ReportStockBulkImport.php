<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Services\ImportOptimizationService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithValidation;

class ReportStockBulkImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading, SkipsEmptyRows, WithValidation
{
    protected string $reportDate;
    protected int $processedRows = 0;
    protected int $productsCreated = 0;
    protected int $outletProductsCreated = 0;
    protected int $outletProductsUpdated = 0;
    protected int $reportStocksCreated = 0;
    protected int $reportStocksUpdated = 0;
    protected int $reportStockDetailsCreated = 0;
    protected int $reportStockDetailsUpdated = 0;
    protected array $errors = [];
    protected array $skippedOutlets = [];

    // Caches untuk optimasi
    protected array $outletCache = [];
    protected array $productCache = [];
    protected array $outletProductCache = [];
    protected array $reportStockDetailCache = [];
    protected ?int $reportStockId = null;

    // Batch data untuk bulk insert
    protected array $productsToInsert = [];
    protected array $outletProductsToInsert = [];
    protected array $outletProductsToUpdate = [];
    protected array $reportStockDetailsToInsert = [];
    protected array $reportStockDetailsToUpdate = [];

    // Optimization service
    // protected ImportOptimizationService $optimizationService;

    public function __construct(string $reportDate)
    {
        $this->reportDate = $reportDate;
        // $this->optimizationService = new ImportOptimizationService();
    }

    public function collection(Collection $rows): void
    {
        // Optimize environment for import
        // $this->optimizationService->optimizeForImport('report_stock_bulk');

        try {
            $this->initializeReportStock();
            $this->preloadCaches();
            $this->processRows($rows);
            $this->processBatchInserts();
        } finally {
            // $this->optimizationService->restoreSettings();
        }
    }

    protected function initializeReportStock(): void
    {
        // Cek atau buat ReportStock untuk tanggal yang dipilih
        $reportStock = ReportStock::firstOrCreate(
            ['report_date' => $this->reportDate],
            ['is_generated' => false]
        );

        $this->reportStockId = $reportStock->id;

        if ($reportStock->wasRecentlyCreated) {
            $this->reportStocksCreated++;
            Log::info("Created new ReportStock for date: {$this->reportDate}");
        } else {
            $this->reportStocksUpdated++;
            Log::info("Using existing ReportStock for date: {$this->reportDate}");
        }
    }

    protected function preloadCaches(): void
    {
        // Preload outlets
        $outlets = Outlet::all(['id', 'code', 'name']);
        foreach ($outlets as $outlet) {
            $this->outletCache[$outlet->code] = $outlet;
        }

        // Preload products by barcode
        $products = Product::all(['id', 'barcode', 'name', 'unit', 'pack_quantity']);
        foreach ($products as $product) {
            $this->productCache[$product->barcode] = $product;
        }

        // Preload outlet products
        $outletProducts = OutletProduct::with(['outlet', 'product'])
            ->get(['id', 'outlet_id', 'product_id', 'outlet_pareto']);
        foreach ($outletProducts as $outletProduct) {
            $key = $outletProduct->outlet_id . '_' . $outletProduct->product_id;
            $this->outletProductCache[$key] = $outletProduct;
        }

        // Preload existing report stock details for this date
        $existingDetails = ReportStockDetail::where('report_stock_id', $this->reportStockId)
            ->with(['outlet', 'product'])
            ->get(['id', 'outlet_id', 'product_id', 'quantity']);
        foreach ($existingDetails as $detail) {
            $key = $detail->outlet_id . '_' . $detail->product_id;
            $this->reportStockDetailCache[$key] = $detail;
        }

        Log::info('Caches preloaded', [
            'outlets' => count($this->outletCache),
            'products' => count($this->productCache),
            'outlet_products' => count($this->outletProductCache),
            'report_stock_details' => count($this->reportStockDetailCache),
        ]);
    }

    protected function processRows(Collection $rows): void
    {
        foreach ($rows as $index => $row) {
            $this->processedRows++;

            try {
                $this->processRow($row, $index + 2); // +2 karena header di baris 1, data mulai baris 2
            } catch (\Exception $e) {
                $this->errors[] = [
                    'row' => $index + 2,
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ];
                Log::error("Error processing row " . ($index + 2), [
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ]);
            }

            // Log progress setiap 1000 baris
            if ($this->processedRows % 1000 === 0) {
                // $this->optimizationService->logProgress("Processed {$this->processedRows} rows", [
                //     'processed_rows' => $this->processedRows,
                //     'errors' => count($this->errors),
                // ]);
            }
        }
    }

    protected function processRow(Collection $row, int $rowNumber): void
    {
        // Validasi dan normalisasi data
        $outletCode = trim(strtoupper($row['outlet'] ?? ''));
        $productName = trim($row['nama_produk'] ?? '');
        $barcode = trim($row['barcode'] ?? '');
        $unit = trim($row['sat'] ?? '');
        $packQuantity = $this->parseNumeric($row['pack'] ?? 0);
        $quantity = $this->parseNumeric($row['qty'] ?? 0);
        $pareto = trim($row['prt'] ?? '');

        // Validasi data wajib
        if (empty($outletCode) || empty($barcode)) {
            throw new \Exception("Missing required data: outlet code or barcode at row {$rowNumber}");
        }

        // Validasi format barcode
        if (strlen($barcode) < 3) {
            throw new \Exception("Invalid barcode format: {$barcode} at row {$rowNumber}");
        }

        // Cek outlet
        if (!isset($this->outletCache[$outletCode])) {
            $this->skippedOutlets[] = $outletCode;
            throw new \Exception("Outlet not found: {$outletCode} at row {$rowNumber}");
        }

        $outlet = $this->outletCache[$outletCode];

        // Proses product
        $product = $this->processProduct($barcode, $productName, $unit, $packQuantity);

        // Proses outlet product
        $this->processOutletProduct($outlet->id, $product->id, $pareto);

        // Proses report stock detail
        $this->processReportStockDetail($outlet->id, $product->id, $quantity);
    }

    protected function processProduct(string $barcode, string $name, string $unit, int $packQuantity): Product
    {
        if (isset($this->productCache[$barcode])) {
            $product = $this->productCache[$barcode];

            // Update product jika ada perubahan
            $needsUpdate = false;
            $updates = [];

            if (!empty($name) && $product->name !== $name) {
                $updates['name'] = $name;
                $needsUpdate = true;
            }

            if (!empty($unit) && $product->unit !== $unit) {
                $updates['unit'] = $unit;
                $needsUpdate = true;
            }

            if ($packQuantity > 0 && $product->pack_quantity !== $packQuantity) {
                $updates['pack_quantity'] = $packQuantity;
                $needsUpdate = true;
            }

            if ($needsUpdate) {
                $product->update($updates);
                Log::info("Updated product: {$barcode}", $updates);
            }

            return $product;
        }

        // Buat product baru
        $productData = [
            'barcode' => $barcode,
            'name' => $name ?: "Product {$barcode}",
            'unit' => $unit ?: 'PCS',
            'pack_quantity' => $packQuantity ?: 1,
        ];

        $this->productsToInsert[] = $productData;

        // Buat object sementara untuk cache
        $product = new Product($productData);
        $product->id = 'temp_' . $barcode; // Temporary ID
        $this->productCache[$barcode] = $product;

        return $product;
    }

    protected function processOutletProduct(int $outletId, $productId, string $pareto): void
    {
        $key = $outletId . '_' . $productId;

        if (isset($this->outletProductCache[$key])) {
            $outletProduct = $this->outletProductCache[$key];

            // Update pareto jika berbeda
            if (!empty($pareto) && $outletProduct->outlet_pareto !== $pareto) {
                $this->outletProductsToUpdate[] = [
                    'id' => $outletProduct->id,
                    'outlet_pareto' => $pareto,
                ];
                $outletProduct->outlet_pareto = $pareto;
            }
        } else {
            // Buat outlet product baru
            $outletProductData = [
                'outlet_id' => $outletId,
                'product_id' => $productId,
                'outlet_pareto' => $pareto,
                'rumus_pareto' => null,
                'min_buffer' => 0,
                'max_buffer' => 0,
            ];

            $this->outletProductsToInsert[] = $outletProductData;

            // Buat object sementara untuk cache
            $outletProduct = new OutletProduct($outletProductData);
            $this->outletProductCache[$key] = $outletProduct;
            $this->outletProductsCreated++;
        }
    }

    protected function processReportStockDetail(int $outletId, $productId, int $quantity): void
    {
        $key = $outletId . '_' . $productId;

        if (isset($this->reportStockDetailCache[$key])) {
            $detail = $this->reportStockDetailCache[$key];

            // Update quantity jika berbeda
            if ($detail->quantity !== $quantity) {
                $this->reportStockDetailsToUpdate[] = [
                    'id' => $detail->id,
                    'quantity' => $quantity,
                ];
                $detail->quantity = $quantity;
                $this->reportStockDetailsUpdated++;
            }
        } else {
            // Buat report stock detail baru
            $detailData = [
                'report_stock_id' => $this->reportStockId,
                'outlet_id' => $outletId,
                'product_id' => $productId,
                'quantity' => $quantity,
            ];

            $this->reportStockDetailsToInsert[] = $detailData;

            // Buat object sementara untuk cache
            $detail = new ReportStockDetail($detailData);
            $this->reportStockDetailCache[$key] = $detail;
            $this->reportStockDetailsCreated++;
        }
    }

    protected function parseNumeric($value): int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        // Handle comma as decimal separator
        $cleaned = str_replace(',', '.', (string) $value);
        $cleaned = preg_replace('/[^\d.]/', '', $cleaned);

        return (int) floatval($cleaned);
    }

    protected function processBatchInserts(): void
    {
        DB::transaction(function () {
            // Insert products baru
            if (!empty($this->productsToInsert)) {
                $chunks = array_chunk($this->productsToInsert, 500);
                foreach ($chunks as $chunk) {
                    Product::insert($chunk);
                }
                $this->productsCreated = count($this->productsToInsert);
                Log::info("Inserted {$this->productsCreated} new products");

                // Update product cache dengan ID yang sebenarnya
                $this->updateProductCacheWithRealIds();
            }

            // Insert outlet products baru
            if (!empty($this->outletProductsToInsert)) {
                // Update product_id dengan ID yang sebenarnya
                $this->updateOutletProductsWithRealProductIds();

                $chunks = array_chunk($this->outletProductsToInsert, 500);
                foreach ($chunks as $chunk) {
                    OutletProduct::insert($chunk);
                }
                Log::info("Inserted " . count($this->outletProductsToInsert) . " new outlet products");
            }

            // Update outlet products
            if (!empty($this->outletProductsToUpdate)) {
                foreach ($this->outletProductsToUpdate as $update) {
                    OutletProduct::where('id', $update['id'])
                        ->update(['outlet_pareto' => $update['outlet_pareto']]);
                }
                $this->outletProductsUpdated = count($this->outletProductsToUpdate);
                Log::info("Updated {$this->outletProductsUpdated} outlet products");
            }

            // Insert report stock details baru
            if (!empty($this->reportStockDetailsToInsert)) {
                // Update product_id dengan ID yang sebenarnya
                $this->updateReportStockDetailsWithRealProductIds();

                $chunks = array_chunk($this->reportStockDetailsToInsert, 500);
                foreach ($chunks as $chunk) {
                    ReportStockDetail::insert($chunk);
                }
                Log::info("Inserted " . count($this->reportStockDetailsToInsert) . " new report stock details");
            }

            // Update report stock details
            if (!empty($this->reportStockDetailsToUpdate)) {
                foreach ($this->reportStockDetailsToUpdate as $update) {
                    ReportStockDetail::where('id', $update['id'])
                        ->update(['quantity' => $update['quantity']]);
                }
                Log::info("Updated " . count($this->reportStockDetailsToUpdate) . " report stock details");
            }
        });
    }

    protected function updateProductCacheWithRealIds(): void
    {
        $barcodes = array_column($this->productsToInsert, 'barcode');
        $products = Product::whereIn('barcode', $barcodes)->get(['id', 'barcode']);

        foreach ($products as $product) {
            if (isset($this->productCache[$product->barcode])) {
                $this->productCache[$product->barcode]->id = $product->id;
            }
        }
    }

    protected function updateOutletProductsWithRealProductIds(): void
    {
        foreach ($this->outletProductsToInsert as &$outletProduct) {
            $productId = $outletProduct['product_id'];
            if (is_string($productId) && str_starts_with($productId, 'temp_')) {
                $barcode = substr($productId, 5); // Remove 'temp_' prefix
                if (isset($this->productCache[$barcode])) {
                    $outletProduct['product_id'] = $this->productCache[$barcode]->id;
                }
            }
        }
    }

    protected function updateReportStockDetailsWithRealProductIds(): void
    {
        foreach ($this->reportStockDetailsToInsert as &$detail) {
            $productId = $detail['product_id'];
            if (is_string($productId) && str_starts_with($productId, 'temp_')) {
                $barcode = substr($productId, 5); // Remove 'temp_' prefix
                if (isset($this->productCache[$barcode])) {
                    $detail['product_id'] = $this->productCache[$barcode]->id;
                }
            }
        }
    }

    // Laravel Excel methods
    public function batchSize(): int
    {
        return config('import.report_stock_bulk.batch_size', 500);
    }

    public function chunkSize(): int
    {
        return config('import.report_stock_bulk.chunk_size', 500);
    }

    public function rules(): array
    {
        return [
            'outlet' => 'required|string',
            'barcode' => 'required|string',
            'qty' => 'nullable|numeric',
        ];
    }

    // Getter methods untuk statistik
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getProductsCreated(): int
    {
        return $this->productsCreated;
    }

    public function getOutletProductsCreated(): int
    {
        return $this->outletProductsCreated;
    }

    public function getOutletProductsUpdated(): int
    {
        return $this->outletProductsUpdated;
    }

    public function getReportStocksCreated(): int
    {
        return $this->reportStocksCreated;
    }

    public function getReportStocksUpdated(): int
    {
        return $this->reportStocksUpdated;
    }

    public function getReportStockDetailsCreated(): int
    {
        return $this->reportStockDetailsCreated;
    }

    public function getReportStockDetailsUpdated(): int
    {
        return $this->reportStockDetailsUpdated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getSkippedOutlets(): array
    {
        return array_unique($this->skippedOutlets);
    }

    public function getImportSummary(): array
    {
        return [
            'processed_rows' => $this->processedRows,
            'products_created' => $this->productsCreated,
            'outlet_products_created' => $this->outletProductsCreated,
            'outlet_products_updated' => $this->outletProductsUpdated,
            'report_stocks_created' => $this->reportStocksCreated,
            'report_stocks_updated' => $this->reportStocksUpdated,
            'report_stock_details_created' => $this->reportStockDetailsCreated,
            'report_stock_details_updated' => $this->reportStockDetailsUpdated,
            'errors_count' => count($this->errors),
            'skipped_outlets' => $this->getSkippedOutlets(),
        ];
    }

    /**
     * Get template headers for Excel file
     */
    public static function getTemplateHeaders(): array
    {
        return [
            'OUTLET' => 'Kode outlet (wajib)',
            'NAMA PRODUK' => 'Nama produk',
            'PRT' => 'Outlet pareto (A/B/C)',
            'BARCODE' => 'Barcode produk (wajib)',
            'PACK' => 'Pack quantity',
            'QTY' => 'Quantity stock',
            'SAT' => 'Satuan unit',
        ];
    }

    /**
     * Validate import data before processing
     */
    public function validateImportData(Collection $rows): array
    {
        $errors = [];
        $requiredColumns = ['outlet', 'barcode'];

        if ($rows->isEmpty()) {
            $errors[] = 'File is empty or has no data rows';
            return $errors;
        }

        // Check if required columns exist
        $firstRow = $rows->first();
        foreach ($requiredColumns as $column) {
            if (!isset($firstRow[$column])) {
                $errors[] = "Missing required column: {$column}";
            }
        }

        return $errors;
    }
}