<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Imports\ReportStockBulkImport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReportStockBulkImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test outlets
        Outlet::create(['code' => 'OUT001', 'name' => 'Outlet 1']);
        Outlet::create(['code' => 'OUT002', 'name' => 'Outlet 2']);
        
        // Create test products
        Product::create([
            'barcode' => '1234567890001',
            'name' => 'Product 1',
            'unit' => 'PCS',
            'pack_quantity' => 1,
        ]);
    }

    public function test_can_import_new_products_and_report_stock_details()
    {
        $reportDate = '2025-09-12';
        
        // Create test data
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'New Product',
                'prt' => 'A',
                'barcode' => '1234567890002',
                'pack' => '1,00',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        // Assert report stock was created
        $this->assertDatabaseHas('report_stocks', [
            'report_date' => $reportDate,
        ]);

        // Assert product was created
        $this->assertDatabaseHas('products', [
            'barcode' => '1234567890002',
            'name' => 'New Product',
            'unit' => 'PCS',
            'pack_quantity' => 1,
        ]);

        // Assert outlet product was created
        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::where('barcode', '1234567890002')->first();
        
        $this->assertDatabaseHas('outlet_products', [
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'outlet_pareto' => 'A',
        ]);

        // Assert report stock detail was created
        $reportStock = ReportStock::where('report_date', $reportDate)->first();
        
        $this->assertDatabaseHas('report_stock_details', [
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 100,
        ]);

        // Check statistics
        $this->assertEquals(1, $import->getProcessedRows());
        $this->assertEquals(1, $import->getProductsCreated());
        $this->assertEquals(1, $import->getOutletProductsCreated());
        $this->assertEquals(1, $import->getReportStockDetailsCreated());
        $this->assertEmpty($import->getErrors());
    }

    public function test_can_update_existing_products_and_report_stock_details()
    {
        $reportDate = '2025-09-12';
        
        // Create existing report stock
        $reportStock = ReportStock::create([
            'report_date' => $reportDate,
            'is_generated' => false,
        ]);

        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::where('barcode', '1234567890001')->first();

        // Create existing outlet product
        OutletProduct::create([
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'outlet_pareto' => 'B',
        ]);

        // Create existing report stock detail
        ReportStockDetail::create([
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 50,
        ]);

        // Create test data with updates
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Updated Product Name',
                'prt' => 'A', // Update pareto
                'barcode' => '1234567890001',
                'pack' => '2,00', // Update pack quantity
                'qty' => 150, // Update quantity
                'sat' => 'BOX', // Update unit
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        // Assert product was updated
        $product->refresh();
        $this->assertEquals('Updated Product Name', $product->name);
        $this->assertEquals('BOX', $product->unit);
        $this->assertEquals(2, $product->pack_quantity);

        // Assert outlet product pareto was updated
        $outletProduct = OutletProduct::where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();
        $this->assertEquals('A', $outletProduct->outlet_pareto);

        // Assert report stock detail quantity was updated
        $reportStockDetail = ReportStockDetail::where('report_stock_id', $reportStock->id)
            ->where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();
        $this->assertEquals(150, $reportStockDetail->quantity);

        // Check statistics
        $this->assertEquals(1, $import->getProcessedRows());
        $this->assertEquals(0, $import->getProductsCreated());
        $this->assertEquals(0, $import->getOutletProductsCreated());
        $this->assertEquals(1, $import->getOutletProductsUpdated());
        $this->assertEquals(0, $import->getReportStockDetailsCreated());
        $this->assertEquals(1, $import->getReportStockDetailsUpdated());
        $this->assertEmpty($import->getErrors());
    }

    public function test_skips_rows_with_invalid_outlet()
    {
        $reportDate = '2025-09-12';
        
        $rows = collect([
            [
                'outlet' => 'INVALID_OUTLET',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890003',
                'pack' => '1,00',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        // Assert no product was created
        $this->assertDatabaseMissing('products', [
            'barcode' => '1234567890003',
        ]);

        // Check statistics
        $this->assertEquals(1, $import->getProcessedRows());
        $this->assertEquals(0, $import->getProductsCreated());
        $this->assertEquals(1, count($import->getErrors()));
        $this->assertContains('INVALID_OUTLET', $import->getSkippedOutlets());
    }

    public function test_handles_missing_required_data()
    {
        $reportDate = '2025-09-12';
        
        $rows = collect([
            [
                'outlet' => '', // Missing outlet
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890004',
                'pack' => '1,00',
                'qty' => 100,
                'sat' => 'PCS',
            ],
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '', // Missing barcode
                'pack' => '1,00',
                'qty' => 100,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        // Check statistics
        $this->assertEquals(2, $import->getProcessedRows());
        $this->assertEquals(0, $import->getProductsCreated());
        $this->assertEquals(2, count($import->getErrors()));
    }

    public function test_parses_numeric_values_correctly()
    {
        $reportDate = '2025-09-12';
        
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product',
                'prt' => 'A',
                'barcode' => '1234567890005',
                'pack' => '2,50', // Comma as decimal separator
                'qty' => '100,75', // Comma as decimal separator
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        // Assert product was created with correct pack quantity
        $this->assertDatabaseHas('products', [
            'barcode' => '1234567890005',
            'pack_quantity' => 2, // Should be converted to integer
        ]);

        // Assert report stock detail was created with correct quantity
        $outlet = Outlet::where('code', 'OUT001')->first();
        $product = Product::where('barcode', '1234567890005')->first();
        $reportStock = ReportStock::where('report_date', $reportDate)->first();
        
        $this->assertDatabaseHas('report_stock_details', [
            'report_stock_id' => $reportStock->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => 100, // Should be converted to integer
        ]);
    }

    public function test_import_summary_returns_correct_statistics()
    {
        $reportDate = '2025-09-12';
        
        $rows = collect([
            [
                'outlet' => 'OUT001',
                'nama_produk' => 'Product 1',
                'prt' => 'A',
                'barcode' => '1234567890006',
                'pack' => '1',
                'qty' => 100,
                'sat' => 'PCS',
            ],
            [
                'outlet' => 'OUT002',
                'nama_produk' => 'Product 2',
                'prt' => 'B',
                'barcode' => '1234567890007',
                'pack' => '1',
                'qty' => 200,
                'sat' => 'PCS',
            ],
        ]);

        $import = new ReportStockBulkImport($reportDate);
        $import->collection($rows);

        $summary = $import->getImportSummary();

        $this->assertEquals(2, $summary['processed_rows']);
        $this->assertEquals(2, $summary['products_created']);
        $this->assertEquals(2, $summary['outlet_products_created']);
        $this->assertEquals(2, $summary['report_stock_details_created']);
        $this->assertEquals(0, $summary['errors_count']);
        $this->assertEmpty($summary['skipped_outlets']);
    }
}
