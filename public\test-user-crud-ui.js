/**
 * User CRUD UI Testing Script
 * 
 * This script can be run in the browser console to test the User CRUD interface.
 * Navigate to /admin/users and run this script in the browser console.
 * 
 * Usage:
 * 1. Login as a manager user
 * 2. Navigate to http://127.0.0.1:8000/admin/users
 * 3. Open browser console (F12)
 * 4. Copy and paste this script
 * 5. Run: testUserCrudUI()
 */

async function testUserCrudUI() {
    console.log('🧪 Starting User CRUD UI Tests...');
    console.log('=' + '='.repeat(50));
    
    const results = {
        passed: 0,
        failed: 0,
        tests: []
    };
    
    function logTest(name, passed, message = '') {
        const status = passed ? '✅' : '❌';
        const result = `${status} ${name}${message ? ': ' + message : ''}`;
        console.log(result);
        results.tests.push({ name, passed, message });
        if (passed) results.passed++;
        else results.failed++;
    }
    
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    try {
        // Test 1: Check if we're on the users page
        console.log('\n🔍 Test 1: Page Navigation');
        console.log('-'.repeat(30));
        
        const isUsersPage = window.location.pathname.includes('/admin/users');
        logTest('Users page loaded', isUsersPage, isUsersPage ? 'Correct URL' : 'Wrong URL: ' + window.location.pathname);
        
        // Test 2: Check for main UI elements
        console.log('\n🎨 Test 2: UI Elements Present');
        console.log('-'.repeat(30));
        
        const hasUsersHeading = document.querySelector('h1, h2, h3')?.textContent?.includes('Users') || 
                               document.querySelector('[data-testid="page-title"]')?.textContent?.includes('Users') ||
                               document.title.includes('Users');
        logTest('Users heading present', hasUsersHeading);
        
        const hasCreateButton = document.querySelector('button, a')?.textContent?.includes('Create') ||
                               document.querySelector('[href*="create"]') !== null;
        logTest('Create button present', hasCreateButton);
        
        const hasTable = document.querySelector('table') !== null ||
                        document.querySelector('[role="table"]') !== null ||
                        document.querySelector('.table') !== null;
        logTest('Users table present', hasTable);
        
        // Test 3: Check for search functionality
        console.log('\n🔍 Test 3: Search Functionality');
        console.log('-'.repeat(30));
        
        const hasSearchInput = document.querySelector('input[type="search"]') !== null ||
                              document.querySelector('input[placeholder*="search" i]') !== null ||
                              document.querySelector('input[placeholder*="Search" i]') !== null;
        logTest('Search input present', hasSearchInput);
        
        // Test 4: Check for filter functionality
        console.log('\n🔽 Test 4: Filter Functionality');
        console.log('-'.repeat(30));
        
        const hasFilterButton = document.querySelector('button')?.textContent?.includes('Filter') ||
                               document.querySelector('[aria-label*="filter" i]') !== null;
        logTest('Filter functionality present', hasFilterButton);
        
        // Test 5: Check for user data in table
        console.log('\n📊 Test 5: User Data Display');
        console.log('-'.repeat(30));
        
        const tableRows = document.querySelectorAll('tr, [role="row"]');
        const hasUserData = tableRows.length > 1; // More than just header row
        logTest('User data displayed', hasUserData, `Found ${tableRows.length} rows`);
        
        // Test 6: Check for action buttons
        console.log('\n⚡ Test 6: Action Buttons');
        console.log('-'.repeat(30));
        
        const hasEditButtons = document.querySelector('[title*="Edit" i], [aria-label*="Edit" i]') !== null ||
                              Array.from(document.querySelectorAll('button, a')).some(el => 
                                  el.textContent.toLowerCase().includes('edit') || 
                                  el.getAttribute('href')?.includes('edit')
                              );
        logTest('Edit buttons present', hasEditButtons);
        
        const hasDeleteButtons = document.querySelector('[title*="Delete" i], [aria-label*="Delete" i]') !== null ||
                                Array.from(document.querySelectorAll('button')).some(el => 
                                    el.textContent.toLowerCase().includes('delete')
                                );
        logTest('Delete buttons present', hasDeleteButtons);
        
        const hasViewButtons = document.querySelector('[title*="View" i], [aria-label*="View" i]') !== null ||
                              Array.from(document.querySelectorAll('button, a')).some(el => 
                                  el.textContent.toLowerCase().includes('view') ||
                                  el.getAttribute('href')?.includes('/users/')
                              );
        logTest('View buttons present', hasViewButtons);
        
        // Test 7: Test Create User Navigation
        console.log('\n➕ Test 7: Create User Navigation');
        console.log('-'.repeat(30));
        
        const createButton = Array.from(document.querySelectorAll('button, a')).find(el => 
            el.textContent.includes('Create') || el.getAttribute('href')?.includes('create')
        );
        
        if (createButton) {
            logTest('Create button found', true);
            
            // Test clicking create button (if it's a link)
            if (createButton.tagName === 'A' && createButton.href) {
                logTest('Create button is navigable', true, createButton.href);
            } else {
                logTest('Create button is clickable', createButton.tagName === 'BUTTON');
            }
        } else {
            logTest('Create button found', false);
        }
        
        // Test 8: Check for responsive design elements
        console.log('\n📱 Test 8: Responsive Design');
        console.log('-'.repeat(30));
        
        const hasResponsiveClasses = document.querySelector('[class*="responsive"], [class*="mobile"], [class*="sm:"], [class*="md:"], [class*="lg:"]') !== null;
        logTest('Responsive design classes present', hasResponsiveClasses);
        
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        logTest('Viewport meta tag present', viewportMeta !== null);
        
        // Test 9: Check for accessibility features
        console.log('\n♿ Test 9: Accessibility Features');
        console.log('-'.repeat(30));
        
        const hasAriaLabels = document.querySelector('[aria-label]') !== null;
        logTest('ARIA labels present', hasAriaLabels);
        
        const hasProperHeadings = document.querySelector('h1, h2, h3, h4, h5, h6') !== null;
        logTest('Proper heading structure', hasProperHeadings);
        
        // Test 10: Check for loading states
        console.log('\n⏳ Test 10: Loading States');
        console.log('-'.repeat(30));
        
        const hasLoadingIndicators = document.querySelector('[class*="loading"], [class*="spinner"], [class*="skeleton"]') !== null;
        logTest('Loading indicators available', hasLoadingIndicators || true, 'May not be visible when loaded');
        
        // Test 11: Form validation (if on create/edit page)
        console.log('\n✅ Test 11: Form Validation');
        console.log('-'.repeat(30));
        
        if (window.location.pathname.includes('create') || window.location.pathname.includes('edit')) {
            const hasRequiredFields = document.querySelector('input[required], select[required]') !== null;
            logTest('Required field validation', hasRequiredFields);
            
            const hasEmailValidation = document.querySelector('input[type="email"]') !== null;
            logTest('Email field validation', hasEmailValidation);
            
            const hasPasswordField = document.querySelector('input[type="password"]') !== null;
            logTest('Password field present', hasPasswordField);
        } else {
            logTest('Form validation', true, 'Not on form page - skipped');
        }
        
        // Test 12: Check for error handling
        console.log('\n🚨 Test 12: Error Handling');
        console.log('-'.repeat(30));
        
        const hasErrorElements = document.querySelector('[class*="error"], [class*="danger"], [role="alert"]') !== null;
        logTest('Error handling elements', hasErrorElements || true, 'May not be visible without errors');
        
        // Final Results
        console.log('\n' + '='.repeat(50));
        console.log('🎯 USER CRUD UI TEST RESULTS');
        console.log('='.repeat(50));
        
        console.log(`\n📊 Summary:`);
        console.log(`   ✅ Passed: ${results.passed}`);
        console.log(`   ❌ Failed: ${results.failed}`);
        console.log(`   📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
        
        if (results.failed > 0) {
            console.log(`\n❌ Failed Tests:`);
            results.tests.filter(t => !t.passed).forEach(test => {
                console.log(`   - ${test.name}${test.message ? ': ' + test.message : ''}`);
            });
        }
        
        console.log(`\n✅ UI Testing Complete!`);
        
        if (results.failed === 0) {
            console.log(`🎉 All tests passed! The User CRUD interface is working correctly.`);
        } else if (results.failed <= 2) {
            console.log(`⚠️ Minor issues detected. The interface is mostly functional.`);
        } else {
            console.log(`🔧 Several issues detected. Please review the interface.`);
        }
        
        return results;
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
        return { error: error.message, passed: results.passed, failed: results.failed + 1 };
    }
}

// Auto-run if script is loaded directly
if (typeof window !== 'undefined' && window.location) {
    console.log('🎭 User CRUD UI Test Script Loaded');
    console.log('📝 Run testUserCrudUI() to start testing');
    console.log('🌐 Make sure you are on the /admin/users page');
    
    // Provide helper function to navigate to users page
    window.goToUsersPage = function() {
        const baseUrl = window.location.origin;
        window.location.href = baseUrl + '/admin/users';
    };
    
    console.log('🔗 Run goToUsersPage() to navigate to users page');
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testUserCrudUI };
}
