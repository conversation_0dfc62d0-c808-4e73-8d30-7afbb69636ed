<?php

namespace App\Exports;

use App\Models\Outlet;
use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;

class ReportStockTemplateExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function collection()
    {
        // Get sample data for template
        $outlets = Outlet::take(3)->get();
        $products = Product::take(3)->get();
        
        $sampleData = collect();
        
        // Add sample rows with different outlets and products
        $sampleData->push([
            'AK01', // outlet_code
            'Sample Product 1', // product_name
            'FM', // pareto
            'SAMPLE001', // barcode
            10, // pack_quantity
            25, // quantity
            'tablet', // unit
        ]);

        $sampleData->push([
            'AK01', // outlet_code
            'Sample Product 2', // product_name
            'SM', // pareto
            'SAMPLE002', // barcode
            1, // pack_quantity
            15, // quantity
            'bottle', // unit
        ]);
        $sampleData->push([
            'AK02', // outlet_code
            'Sample Product 3', // product_name
            'BM', // pareto
            'SAMPLE003', // barcode
            12, // pack_quantity
            8, // quantity
            'box', // unit
        ]);
        $sampleData->push([
            'AK02', // outlet_code
            'Sample Product 1', // product_name
            'FM', // pareto
            'SAMPLE001', // barcode
            10, // pack_quantity
            30, // quantity
            'tablet', // unit
        ]);
        
        return $sampleData;
    }

    public function headings(): array
    {
        return [
            'OUTLET',
            'NAMA PRODUK',
            'PRT',
            'BARCODE',
            'PACK',
            'QTY',
            'SAT',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '3b82f6'], // Blue color
                ],
            ],
            // Style sample data rows
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            5 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            6 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // OUTLET
            'B' => 30, // NAMA PRODUK
            'C' => 12, // PRT
            'D' => 15, // BARCODE
            'E' => 12, // PACK
            'F' => 12, // QTY
            'G' => 12, // SAT
        ];
    }

    public function title(): string
    {
        return 'Report Stock Template';
    }
}
