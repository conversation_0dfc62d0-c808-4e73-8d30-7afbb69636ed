<?php

namespace App\Filament\Resources\OutletProductResource\Pages;

use App\Filament\Resources\OutletProductResource\OutletProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Notifications\Notification;

class EditOutletProduct extends EditRecord
{
    protected static string $resource = OutletProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make()
                ->label('View')
                ->icon('heroicon-m-eye')
                ->color('info'),

            DeleteAction::make()
                ->label('Delete')
                ->icon('heroicon-m-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Delete Configuration')
                ->modalDescription(fn () => "Are you sure you want to delete this outlet product configuration?")
                ->modalSubmitActionLabel('Yes, delete it'),
        ];
    }

    public function getTitle(): string
    {
        return "Edit Configuration: {$this->record->product->name} @ {$this->record->outlet->name}";
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Configuration updated successfully')
            ->body('The outlet product configuration has been updated.')
            ->duration(5000);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure numeric fields are properly cast
        $data['min_buffer'] = (int) ($data['min_buffer'] ?? 0);
        $data['max_buffer'] = (int) ($data['max_buffer'] ?? 0);
        
        return $data;
    }
}
