<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\OutletProduct;
use App\Models\ReportStockDetail;
use Illuminate\Support\Facades\DB;

echo "=== Cleanup Product ID = 0 Records ===\n\n";

// Confirm before cleanup
echo "This will delete all records with product_id = 0\n";
echo "Are you sure you want to continue? (y/N): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'y') {
    echo "Cleanup cancelled.\n";
    exit(0);
}

echo "\nStarting cleanup...\n";

// Count before cleanup
$outletProductsBefore = OutletProduct::where('product_id', 0)->count();
$reportStockDetailsBefore = ReportStockDetail::where('product_id', 0)->count();

echo "Before cleanup:\n";
echo "  OutletProducts with product_id = 0: {$outletProductsBefore}\n";
echo "  ReportStockDetails with product_id = 0: {$reportStockDetailsBefore}\n\n";

// Start transaction
DB::beginTransaction();

try {
    // Delete outlet products with product_id = 0
    echo "Deleting OutletProducts with product_id = 0...\n";
    $deletedOutletProducts = OutletProduct::where('product_id', 0)->delete();
    echo "Deleted: {$deletedOutletProducts} OutletProducts\n";

    // Delete report stock details with product_id = 0
    echo "Deleting ReportStockDetails with product_id = 0...\n";
    $deletedReportStockDetails = ReportStockDetail::where('product_id', 0)->delete();
    echo "Deleted: {$deletedReportStockDetails} ReportStockDetails\n";

    // Commit transaction
    DB::commit();
    
    echo "\nCleanup completed successfully!\n";
    
    // Verify cleanup
    $outletProductsAfter = OutletProduct::where('product_id', 0)->count();
    $reportStockDetailsAfter = ReportStockDetail::where('product_id', 0)->count();
    
    echo "\nAfter cleanup:\n";
    echo "  OutletProducts with product_id = 0: {$outletProductsAfter}\n";
    echo "  ReportStockDetails with product_id = 0: {$reportStockDetailsAfter}\n";
    
    if ($outletProductsAfter === 0 && $reportStockDetailsAfter === 0) {
        echo "\n✅ Cleanup successful! All records with product_id = 0 have been removed.\n";
    } else {
        echo "\n⚠️  Warning: Some records with product_id = 0 still exist.\n";
    }
    
} catch (\Exception $e) {
    // Rollback on error
    DB::rollback();
    echo "\nError during cleanup: " . $e->getMessage() . "\n";
    echo "All changes have been rolled back.\n";
    exit(1);
}
