<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class UserStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalUsers = User::count();
        $managersCount = User::role('manager')->count();
        $adminsCount = User::role('admin')->count();
        $usersWithoutOutlet = User::whereNull('outlet_id')->count();
        $recentUsers = User::where('created_at', '>=', now()->subDays(7))->count();

        return [
            Stat::make('Total Users', $totalUsers)
                ->description('All registered users')
                ->descriptionIcon('heroicon-o-users')
                ->color('primary'),

            Stat::make('Managers', $managersCount)
                ->description('Users with manager role')
                ->descriptionIcon('heroicon-o-shield-check')
                ->color('success'),

            Stat::make('Admins', $adminsCount)
                ->description('Users with admin role')
                ->descriptionIcon('heroicon-o-user-group')
                ->color('warning'),

            Stat::make('Without Outlet', $usersWithoutOutlet)
                ->description('Users not assigned to outlet')
                ->descriptionIcon('heroicon-o-building-storefront')
                ->color('gray'),

            Stat::make('New This Week', $recentUsers)
                ->description('Users created in last 7 days')
                ->descriptionIcon('heroicon-o-calendar')
                ->color('info'),
        ];
    }
}
