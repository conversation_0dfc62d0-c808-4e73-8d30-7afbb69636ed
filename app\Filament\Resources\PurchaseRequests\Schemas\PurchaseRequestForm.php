<?php

namespace App\Filament\Resources\PurchaseRequests\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Get;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Auth;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;

class PurchaseRequestForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Step 1: Date Selection
                Section::make('Step 1: Purchase Date')
                    ->description('Choose the purchase request date')
                    ->icon('heroicon-o-calendar-days')
                    ->schema([
                        DatePicker::make('purchase_request_date')
                            ->label('Purchase Request Date')
                            ->required()
                            ->default(now())
                            ->maxDate(now()->addDays(30))
                            ->helperText('Select the date for this purchase request')
                            ->live()
                            ->afterStateUpdated(function (callable $set) {
                                // Reset products when date changes
                                $set('purchase_details', []);
                            }),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // Step 2: Outlet Selection
                Section::make('Step 2: Outlet Selection')
                    ->description('Choose the outlet for this request')
                    ->icon('heroicon-o-building-storefront')
                    ->schema([
                        // Show selected date as read-only info
                        Placeholder::make('selected_date_info')
                            ->label('Selected Date')
                            ->content(fn (Get $get): string =>
                                $get('purchase_request_date') ?
                                \Carbon\Carbon::parse($get('purchase_request_date'))->format('l, F j, Y') :
                                'No date selected'
                            )
                            ->visible(fn (Get $get): bool => filled($get('purchase_request_date'))),

                        // For admin users, use hidden field with their outlet_id
                        // For managers, show the select dropdown
                        ...Auth::user()?->hasRole('admin') ? [
                            Hidden::make('outlet_id')
                                ->default(Auth::user()->outlet_id),
                            Placeholder::make('outlet_info')
                                ->label('Outlet')
                                ->content(fn (): string =>
                                    Auth::user()->outlet?->name ?? 'No outlet assigned'
                                ),
                        ] : [
                            Select::make('outlet_id')
                                ->label('Outlet')
                                ->options(Outlet::pluck('name', 'id'))
                                ->required()
                                ->searchable()
                                ->preload()
                                ->placeholder('Select an outlet')
                                ->live()
                                ->afterStateUpdated(function (callable $set) {
                                    // Reset products when outlet changes
                                    $set('purchase_details', []);
                                }),
                        ],
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->visible(fn (Get $get): bool => filled($get('purchase_request_date'))),

                // Step 3: Product Selection
                Section::make('Step 3: Product Selection')
                    ->description('Add products to your purchase request')
                    ->icon('heroicon-o-shopping-cart')
                    ->schema([
                        // Show selected date and outlet as read-only info
                        Section::make('Request Information')
                            ->schema([
                                Placeholder::make('request_summary')
                                    ->label('Purchase Request Summary')
                                    ->content(function (Get $get): string {
                                        $date = $get('purchase_request_date');
                                        $outletId = $get('outlet_id');

                                        $dateStr = $date ? \Carbon\Carbon::parse($date)->format('l, F j, Y') : 'No date selected';
                                        $outletStr = 'No outlet selected';

                                        if ($outletId) {
                                            $outlet = Outlet::find($outletId);
                                            $outletStr = $outlet ? $outlet->name : 'Unknown outlet';
                                        } elseif (Auth::user()?->hasRole('admin') && Auth::user()->outlet) {
                                            $outletStr = Auth::user()->outlet->name;
                                        }

                                        return "Date: {$dateStr}<br>Outlet: {$outletStr}";
                                    })
                                    ->columnSpanFull(),
                            ])
                            ->collapsible()
                            ->collapsed(),

                        Repeater::make('purchase_details')
                            ->label('Products to Purchase')
                            ->schema([
                                Select::make('product_id')
                                    ->label('Product')
                                    ->options(function (Get $get) {
                                        $outletId = $get('../../outlet_id') ?? Auth::user()?->outlet_id;
                                        if (!$outletId) {
                                            return [];
                                        }

                                        // Get products available for this outlet
                                        return Product::whereHas('outlets', function ($query) use ($outletId) {
                                            $query->where('outlet_id', $outletId);
                                        })
                                        ->get()
                                        ->mapWithKeys(function ($product) {
                                            return [$product->id => "{$product->name} ({$product->barcode})"];
                                        })
                                        ->toArray();
                                    })
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->placeholder('Select a product')
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $set, Get $get) {
                                        if ($state) {
                                            $outletId = $get('../../outlet_id') ?? Auth::user()?->outlet_id;
                                            $requestDate = $get('../../purchase_request_date');

                                            if ($outletId) {
                                                // Get outlet product configuration
                                                $outletProduct = OutletProduct::where('outlet_id', $outletId)
                                                    ->where('product_id', $state)
                                                    ->first();

                                                if ($outletProduct && $requestDate) {
                                                    // Get current stock for the selected date
                                                    $reportStock = ReportStock::where('report_date', $requestDate)->first();
                                                    $currentStock = 0;

                                                    if ($reportStock) {
                                                        $stockDetail = ReportStockDetail::where('report_stock_id', $reportStock->id)
                                                            ->where('outlet_id', $outletId)
                                                            ->where('product_id', $state)
                                                            ->first();

                                                        if ($stockDetail) {
                                                            $currentStock = $stockDetail->quantity;
                                                        }
                                                    }

                                                    // Calculate suggested purchase quantity
                                                    $suggestedQuantity = 0;
                                                    if ($currentStock < $outletProduct->min_buffer) {
                                                        $suggestedQuantity = $outletProduct->max_buffer - $currentStock;
                                                    }

                                                    if ($suggestedQuantity > 0) {
                                                        $set('purchase_quantity', $suggestedQuantity);
                                                    }
                                                }
                                            }
                                        }
                                    }),

                                TextInput::make('purchase_quantity')
                                    ->label('Purchase Quantity')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1)
                                    ->step(1)
                                    ->helperText('Enter the quantity to purchase'),

                                Placeholder::make('product_info')
                                    ->label('Product Information')
                                    ->content(function (Get $get): string {
                                        $productId = $get('product_id');
                                        $outletId = $get('../../outlet_id') ?? Auth::user()?->outlet_id;
                                        $requestDate = $get('../../purchase_request_date');

                                        if (!$productId || !$outletId) {
                                            return 'Select a product to see details';
                                        }

                                        $product = Product::find($productId);
                                        $outletProduct = OutletProduct::where('outlet_id', $outletId)
                                            ->where('product_id', $productId)
                                            ->first();

                                        if (!$product) {
                                            return 'Product not found';
                                        }

                                        $info = "<strong>Product Details:</strong><br>";
                                        $info .= "Barcode: {$product->barcode}<br>";
                                        $info .= "Unit: {$product->unit}<br>";
                                        $info .= "Pack Quantity: {$product->pack_quantity}<br><br>";

                                        if ($outletProduct) {
                                            $info .= "<strong>Buffer Configuration:</strong><br>";
                                            $info .= "Min Buffer: {$outletProduct->min_buffer}<br>";
                                            $info .= "Max Buffer: {$outletProduct->max_buffer}<br>";
                                            if ($outletProduct->outlet_pareto) {
                                                $info .= "Pareto: {$outletProduct->outlet_pareto}<br>";
                                            }
                                            if ($outletProduct->rumus_pareto) {
                                                $info .= "Formula: {$outletProduct->rumus_pareto}<br>";
                                            }
                                        }

                                        // Get current stock information if date is provided
                                        if ($requestDate) {
                                            $reportStock = ReportStock::where('report_date', $requestDate)->first();
                                            if ($reportStock) {
                                                $stockDetail = ReportStockDetail::where('report_stock_id', $reportStock->id)
                                                    ->where('outlet_id', $outletId)
                                                    ->where('product_id', $productId)
                                                    ->first();

                                                if ($stockDetail) {
                                                    $info .= "<br><strong>Current Stock ({$requestDate}):</strong><br>";
                                                    $info .= "Quantity: {$stockDetail->quantity}<br>";

                                                    if ($outletProduct) {
                                                        $status = 'Normal';
                                                        $statusColor = 'green';

                                                        if ($stockDetail->quantity <= 0) {
                                                            $status = 'Out of Stock';
                                                            $statusColor = 'red';
                                                        } elseif ($stockDetail->quantity < $outletProduct->min_buffer) {
                                                            $status = 'Below Minimum';
                                                            $statusColor = 'orange';
                                                        } elseif ($stockDetail->quantity > $outletProduct->max_buffer) {
                                                            $status = 'Overstock';
                                                            $statusColor = 'blue';
                                                        }

                                                        $info .= "Status: <span style='color: {$statusColor}; font-weight: bold;'>{$status}</span>";
                                                    }
                                                } else {
                                                    $info .= "<br><strong>Stock Status:</strong><br>";
                                                    $info .= "<span style='color: gray;'>No stock data available for this date</span>";
                                                }
                                            } else {
                                                $info .= "<br><strong>Stock Status:</strong><br>";
                                                $info .= "<span style='color: gray;'>No stock report available for this date</span>";
                                            }
                                        }

                                        return $info;
                                    })
                                    ->columnSpan(2),
                            ])
                            ->columns(3)
                            ->addActionLabel('Add Product')
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->itemLabel(function (array $state): ?string {
                                if (!isset($state['product_id'])) {
                                    return 'New Product';
                                }

                                $product = Product::find($state['product_id']);
                                $quantity = $state['purchase_quantity'] ?? 0;

                                return $product ? "{$product->name} (Qty: {$quantity})" : 'Unknown Product';
                            })
                            ->minItems(1)
                            ->helperText('Add at least one product to your purchase request'),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->visible(function (Get $get): bool {
                        $hasDate = filled($get('purchase_request_date'));
                        $hasOutlet = filled($get('outlet_id')) || Auth::user()?->hasRole('admin');
                        return $hasDate && $hasOutlet;
                    }),
            ]);
    }
}
