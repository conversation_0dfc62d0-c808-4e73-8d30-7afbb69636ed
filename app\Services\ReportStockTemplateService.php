<?php

namespace App\Services;

use App\Models\Outlet;
use App\Models\Product;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class ReportStockTemplateService
{
    /**
     * Generate Excel template for report stock import
     */
    public function generateTemplate(): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Report Stock Template');

        // Set headers
        $headers = [
            'A1' => 'OUTLET',
            'B1' => 'NAMA PRODUK', 
            'C1' => 'PRT',
            'D1' => 'BARCODE',
            'E1' => 'PACK',
            'F1' => 'QTY',
            'G1' => 'SAT',
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // Style headers
        $headerRange = 'A1:G1';
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        // Add sample data
        $sampleData = [
            ['OUT001', 'Paracetamol 500mg', 'A', '1234567890001', '1,00', 100, 'PCS'],
            ['OUT001', 'Amoxicillin 500mg', 'B', '1234567890002', '1,00', 50, 'PCS'],
            ['OUT002', 'Vitamin C 1000mg', 'A', '1234567890003', '1,00', 75, 'PCS'],
        ];

        $row = 2;
        foreach ($sampleData as $data) {
            $col = 'A';
            foreach ($data as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }

        // Style sample data
        $dataRange = 'A2:G4';
        $sheet->getStyle($dataRange)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Add instructions sheet
        $instructionSheet = $spreadsheet->createSheet();
        $instructionSheet->setTitle('Instructions');
        
        $instructions = [
            ['INSTRUKSI PENGGUNAAN TEMPLATE IMPORT REPORT STOCK'],
            [''],
            ['1. KOLOM WAJIB:'],
            ['   - OUTLET: Kode outlet yang sudah terdaftar di sistem'],
            ['   - BARCODE: Barcode produk (minimal 3 karakter)'],
            [''],
            ['2. KOLOM OPSIONAL:'],
            ['   - NAMA PRODUK: Nama produk (akan diupdate jika sudah ada)'],
            ['   - PRT: Outlet pareto (A/B/C)'],
            ['   - PACK: Pack quantity (default: 1)'],
            ['   - QTY: Quantity stock (default: 0)'],
            ['   - SAT: Satuan unit (default: PCS)'],
            [''],
            ['3. CATATAN PENTING:'],
            ['   - Jika outlet tidak ditemukan, baris akan dilewati'],
            ['   - Jika produk belum ada, akan dibuat otomatis'],
            ['   - Data yang sudah ada akan diupdate sesuai file Excel'],
            ['   - Format angka bisa menggunakan koma (,) sebagai pemisah desimal'],
            [''],
            ['4. CONTOH DATA:'],
            ['   Lihat sheet "Report Stock Template" untuk contoh format data'],
        ];

        $row = 1;
        foreach ($instructions as $instruction) {
            $instructionSheet->setCellValue('A' . $row, $instruction[0]);
            if ($row === 1) {
                $instructionSheet->getStyle('A' . $row)->applyFromArray([
                    'font' => ['bold' => true, 'size' => 14],
                ]);
            }
            $row++;
        }

        $instructionSheet->getColumnDimension('A')->setWidth(80);

        // Set active sheet back to template
        $spreadsheet->setActiveSheetIndex(0);

        // Save to temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'report_stock_template_') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);

        return $tempFile;
    }

    /**
     * Generate template with existing outlet and product data
     */
    public function generateTemplateWithData(): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Report Stock Template');

        // Set headers
        $headers = [
            'A1' => 'OUTLET',
            'B1' => 'NAMA PRODUK', 
            'C1' => 'PRT',
            'D1' => 'BARCODE',
            'E1' => 'PACK',
            'F1' => 'QTY',
            'G1' => 'SAT',
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // Style headers
        $this->styleHeaders($sheet, 'A1:G1');

        // Get sample data from database
        $outlets = Outlet::take(3)->get();
        $products = Product::take(10)->get();

        $row = 2;
        foreach ($outlets as $outlet) {
            foreach ($products->take(3) as $product) {
                $sheet->setCellValue('A' . $row, $outlet->code);
                $sheet->setCellValue('B' . $row, $product->name);
                $sheet->setCellValue('C' . $row, 'A'); // Default pareto
                $sheet->setCellValue('D' . $row, $product->barcode);
                $sheet->setCellValue('E' . $row, $product->pack_quantity);
                $sheet->setCellValue('F' . $row, 0); // Default quantity
                $sheet->setCellValue('G' . $row, $product->unit);
                $row++;
            }
        }

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Add outlet list sheet
        $this->addOutletListSheet($spreadsheet);

        // Add instructions sheet
        $this->addInstructionsSheet($spreadsheet);

        // Set active sheet back to template
        $spreadsheet->setActiveSheetIndex(0);

        // Save to temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'report_stock_template_with_data_') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);

        return $tempFile;
    }

    private function styleHeaders($sheet, $range): void
    {
        $sheet->getStyle($range)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);
    }

    private function addOutletListSheet($spreadsheet): void
    {
        $outletSheet = $spreadsheet->createSheet();
        $outletSheet->setTitle('Outlet List');
        
        $outletSheet->setCellValue('A1', 'KODE OUTLET');
        $outletSheet->setCellValue('B1', 'NAMA OUTLET');
        
        $this->styleHeaders($outletSheet, 'A1:B1');

        $outlets = Outlet::orderBy('code')->get();
        $row = 2;
        foreach ($outlets as $outlet) {
            $outletSheet->setCellValue('A' . $row, $outlet->code);
            $outletSheet->setCellValue('B' . $row, $outlet->name);
            $row++;
        }

        $outletSheet->getColumnDimension('A')->setAutoSize(true);
        $outletSheet->getColumnDimension('B')->setAutoSize(true);
    }

    private function addInstructionsSheet($spreadsheet): void
    {
        $instructionSheet = $spreadsheet->createSheet();
        $instructionSheet->setTitle('Instructions');
        
        $instructions = [
            ['INSTRUKSI PENGGUNAAN TEMPLATE IMPORT REPORT STOCK'],
            [''],
            ['1. KOLOM WAJIB:'],
            ['   - OUTLET: Kode outlet (lihat sheet "Outlet List")'],
            ['   - BARCODE: Barcode produk (minimal 3 karakter)'],
            [''],
            ['2. KOLOM OPSIONAL:'],
            ['   - NAMA PRODUK: Nama produk (akan diupdate jika sudah ada)'],
            ['   - PRT: Outlet pareto (A/B/C)'],
            ['   - PACK: Pack quantity (default: 1)'],
            ['   - QTY: Quantity stock (default: 0)'],
            ['   - SAT: Satuan unit (default: PCS)'],
            [''],
            ['3. CATATAN PENTING:'],
            ['   - Jika outlet tidak ditemukan, baris akan dilewati'],
            ['   - Jika produk belum ada, akan dibuat otomatis'],
            ['   - Data yang sudah ada akan diupdate sesuai file Excel'],
            ['   - Format angka bisa menggunakan koma (,) sebagai pemisah desimal'],
            [''],
            ['4. DAFTAR OUTLET:'],
            ['   Lihat sheet "Outlet List" untuk daftar kode outlet yang valid'],
        ];

        $row = 1;
        foreach ($instructions as $instruction) {
            $instructionSheet->setCellValue('A' . $row, $instruction[0]);
            if ($row === 1) {
                $instructionSheet->getStyle('A' . $row)->applyFromArray([
                    'font' => ['bold' => true, 'size' => 14],
                ]);
            }
            $row++;
        }

        $instructionSheet->getColumnDimension('A')->setWidth(80);
    }
}
