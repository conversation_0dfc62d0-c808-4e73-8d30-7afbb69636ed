<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ReportStockBulkImport;

function formatBytes($bytes, $precision = 2)
{
    $units = ['B','KB','MB','GB','TB'];
    for ($i=0; $bytes>1024 && $i<count($units)-1; $i++) { $bytes/=1024; }
    return round($bytes, $precision).' '.$units[$i];
}

$path = $argv[1] ?? null;
$date = $argv[2] ?? '2025-09-07';

if (!$path || !file_exists($path)) {
    fwrite(STDERR, "File not found or not provided.\nUsage: php scripts/import_excel.php <path> [YYYY-MM-DD]\nSupported: .xlsx, .xls, .csv\n");
    exit(1);
}

echo "\n=== Import Excel ===\n";
echo "File: {$path}\n";
echo "Date: {$date}\n";

DB::disableQueryLog();
@set_time_limit(0);

$import = new ReportStockBulkImport($date);
$start = microtime(true);
$memStart = memory_get_usage();

try {
    // Use queued import for better memory profile on big files
    Excel::queueImport($import, $path)->allOnQueue('imports')->allOnConnection(null);
    $ok = true;
    echo "\nQueued import dispatched to 'imports' queue. Monitor your queue worker logs.\n";
} catch (\Throwable $e) {
    $ok = false;
    echo "\nImport FAILED: ".$e->getMessage()."\n";
    echo $e->getFile().':'.$e->getLine()."\n";
}

$memEnd = memory_get_usage();
$end = microtime(true);

echo "\nProcessed rows: ".$import->getProcessedRows()."\n";
echo "Products created: ".$import->getProductsCreated()."\n";
echo "Report stocks created: ".$import->getReportStocksCreated()."\n";
echo "Report stocks updated: ".$import->getReportStocksUpdated()."\n";
echo "Duration: ".round(($end-$start)*1000,2)." ms\n";
echo "Mem used: ".formatBytes($memEnd - $memStart)." (peak ".formatBytes(memory_get_peak_usage()).")\n";

// Tail last lines of laravel.log for quick diagnostics
$logPath = __DIR__.'/../storage/logs/laravel.log';
echo "\nLast 50 log lines:\n";
if (file_exists($logPath)) {
    $lines = @file($logPath);
    if ($lines !== false) foreach (array_slice($lines, -50) as $line) echo $line;
}

echo "\n=== Done ===\n";
exit($ok ? 0 : 2);

