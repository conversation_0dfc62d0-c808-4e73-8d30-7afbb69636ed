<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Models\Outlet;
use App\Models\ReportStock;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Filament\Actions\Action;

class DateSelection extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ReportStockResource::class;
    protected static ?string $title = 'Select Report Date';

    public $outlet;
    public $outletRecord;

    public function mount($outlet): void
    {
        $this->outlet = $outlet;
        $this->outletRecord = Outlet::findOrFail($outlet);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('report_date')
                    ->label('Report Date')
                    ->date('l, M j, Y')
                    ->sortable()
                    ->weight('bold')
                    ->description(fn ($state) => $state->diffForHumans()),

                TextColumn::make('total_products')
                    ->label('Products Reported')
                    ->badge()
                    ->color('primary')
                    ->description('Number of products')
                    ->getStateUsing(function ($record) {
                        return ReportStock::where('outlet_id', $this->outlet)
                            ->where('report_date', $record->report_date)
                            ->count();
                    }),

                TextColumn::make('total_quantity')
                    ->label('Total Stock')
                    ->badge()
                    ->color('success')
                    ->numeric()
                    ->description('Sum of all quantities')
                    ->getStateUsing(function ($record) {
                        return ReportStock::where('outlet_id', $this->outlet)
                            ->where('report_date', $record->report_date)
                            ->sum('quantity');
                    }),

                TextColumn::make('out_of_stock')
                    ->label('Out of Stock')
                    ->badge()
                    ->color('danger')
                    ->description('Products with zero quantity')
                    ->getStateUsing(function ($record) {
                        return ReportStock::where('outlet_id', $this->outlet)
                            ->where('report_date', $record->report_date)
                            ->where('quantity', 0)
                            ->count();
                    }),

                TextColumn::make('low_stock')
                    ->label('Low Stock')
                    ->badge()
                    ->color('warning')
                    ->description('Products with low quantity (< 10)')
                    ->getStateUsing(function ($record) {
                        return ReportStock::where('outlet_id', $this->outlet)
                            ->where('report_date', $record->report_date)
                            ->where('quantity', '<', 10)
                            ->where('quantity', '>', 0)
                            ->count();
                    }),

                TextColumn::make('created_at')
                    ->label('Reported At')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->description(fn ($state) => $state->diffForHumans()),
            ])
            ->recordAction('viewDetails')
            ->recordUrl(fn ($record) => static::$resource::getUrl('details', [
                'outlet' => $this->outlet,
                'date' => $record->report_date->format('Y-m-d')
            ]))
            ->defaultSort('report_date', 'desc')
            ->searchPlaceholder('Search by date...')
            ->emptyStateHeading('No Reports Found')
            ->emptyStateDescription("No stock reports found for {$this->outletRecord->name}.")
            ->emptyStateIcon('heroicon-o-calendar-days')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    protected function getTableQuery(): Builder
    {
        // Disable strict mode temporarily to allow GROUP BY without all selected columns
        DB::statement("SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");

        return ReportStock::query()
            ->where('outlet_id', $this->outlet)
            ->selectRaw('report_date, MIN(id) as id, MIN(created_at) as created_at')
            ->groupBy('report_date')
            ->orderByRaw('report_date DESC');
    }



    public function getTitle(): string
    {
        return "Report Dates - {$this->outletRecord->name}";
    }

    public function getHeading(): string
    {
        return "Stock Report Dates";
    }

    public function getSubheading(): ?string
    {
        return "Select a date to view detailed stock report for {$this->outletRecord->name} ({$this->outletRecord->code}).";
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back_to_outlets')
                ->label('Back to Outlets')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(static::$resource::getUrl('index')),
        ];
    }

    public function getView(): string
    {
        return 'filament.resources.report-stocks.pages.date-selection';
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return null; // Disable default sorting to avoid GROUP BY conflicts
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return null;
    }


}
