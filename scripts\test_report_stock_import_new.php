<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Services\ReportStockTemplateService;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

echo "=== Report Stock Import Test ===\n\n";

// Create test data if not exists
echo "Setting up test data...\n";

// Create outlets if not exist
$outlets = [
    ['code' => 'OUT001', 'name' => 'Outlet Jakarta Pusat'],
    ['code' => 'OUT002', 'name' => 'Outlet Jakarta Selatan'],
    ['code' => 'OUT003', 'name' => 'Outlet Bandung'],
];

foreach ($outlets as $outletData) {
    Outlet::firstOrCreate(['code' => $outletData['code']], $outletData);
}

// Create some existing products
$products = [
    ['barcode' => '1111111111111', 'name' => 'Paracetamol 500mg', 'unit' => 'PCS', 'pack_quantity' => 1],
    ['barcode' => '2222222222222', 'name' => 'Amoxicillin 500mg', 'unit' => 'PCS', 'pack_quantity' => 1],
];

foreach ($products as $productData) {
    Product::firstOrCreate(['barcode' => $productData['barcode']], $productData);
}

echo "Test data setup completed.\n\n";

// Create test Excel file
echo "Creating test Excel file...\n";

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$headers = ['OUTLET', 'NAMA PRODUK', 'PRT', 'BARCODE', 'PACK', 'QTY', 'SAT'];
$col = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($col . '1', $header);
    $col++;
}

// Add test data
$testData = [
    // Existing product update
    ['OUT001', 'Paracetamol 500mg Updated', 'A', '1111111111111', '2,00', 150, 'BOX'],
    
    // New products
    ['OUT001', 'Vitamin C 1000mg', 'B', '3333333333333', '1,00', 100, 'PCS'],
    ['OUT002', 'Ibuprofen 400mg', 'A', '4444444444444', '1,00', 75, 'PCS'],
    ['OUT003', 'Aspirin 100mg', 'C', '5555555555555', '1,00', 50, 'PCS'],
    
    // Update existing product with different outlet
    ['OUT002', 'Amoxicillin 500mg', 'B', '2222222222222', '1,00', 200, 'PCS'],
    
    // Invalid outlet (should be skipped)
    ['INVALID', 'Test Product', 'A', '6666666666666', '1,00', 25, 'PCS'],
    
    // Missing barcode (should error)
    ['OUT001', 'No Barcode Product', 'A', '', '1,00', 10, 'PCS'],
    
    // Valid data with comma in numbers
    ['OUT003', 'Multivitamin', 'A', '7777777777777', '3,50', '125,75', 'PCS'],
];

$row = 2;
foreach ($testData as $data) {
    $col = 'A';
    foreach ($data as $value) {
        $sheet->setCellValue($col . $row, $value);
        $col++;
    }
    $row++;
}

$testFile = 'test_report_stock_import.xlsx';
$writer = new Xlsx($spreadsheet);
$writer->save($testFile);

echo "Test Excel file created: {$testFile}\n\n";

// Get initial counts
echo "=== Initial Database State ===\n";
$initialCounts = [
    'outlets' => Outlet::count(),
    'products' => Product::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stocks' => ReportStock::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

foreach ($initialCounts as $table => $count) {
    echo "{$table}: {$count}\n";
}
echo "\n";

// Test import
$reportDate = '2025-09-12';
echo "=== Testing Import ===\n";
echo "Report Date: {$reportDate}\n";
echo "File: {$testFile}\n\n";

$startTime = microtime(true);
$startMemory = memory_get_usage(true);

try {
    $import = new ReportStockBulkImport($reportDate);
    Excel::import($import, $testFile);
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage(true);
    $peakMemory = memory_get_peak_usage(true);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Show statistics
    echo "=== Import Statistics ===\n";
    $summary = $import->getImportSummary();
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "{$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "{$key}: {$value}\n";
        }
    }
    echo "\n";
    
    // Show errors if any
    $errors = $import->getErrors();
    if (!empty($errors)) {
        echo "=== Errors ===\n";
        foreach ($errors as $error) {
            echo "Row {$error['row']}: {$error['error']}\n";
            echo "Data: " . json_encode($error['data']) . "\n\n";
        }
    }
    
    // Show skipped outlets
    $skippedOutlets = $import->getSkippedOutlets();
    if (!empty($skippedOutlets)) {
        echo "=== Skipped Outlets ===\n";
        foreach ($skippedOutlets as $outlet) {
            echo "- {$outlet}\n";
        }
        echo "\n";
    }
    
    // Performance metrics
    echo "=== Performance Metrics ===\n";
    echo "Execution time: " . round($endTime - $startTime, 2) . " seconds\n";
    echo "Memory used: " . formatBytes($endMemory - $startMemory) . "\n";
    echo "Peak memory: " . formatBytes($peakMemory) . "\n\n";
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Get final counts
echo "=== Final Database State ===\n";
$finalCounts = [
    'outlets' => Outlet::count(),
    'products' => Product::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stocks' => ReportStock::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

foreach ($finalCounts as $table => $count) {
    $change = $count - $initialCounts[$table];
    $changeStr = $change > 0 ? " (+{$change})" : ($change < 0 ? " ({$change})" : "");
    echo "{$table}: {$count}{$changeStr}\n";
}
echo "\n";

// Test template generation
echo "=== Testing Template Generation ===\n";
try {
    $templateService = new ReportStockTemplateService();
    
    // Generate basic template
    $basicTemplate = $templateService->generateTemplate();
    echo "✅ Basic template generated: " . basename($basicTemplate) . "\n";
    
    // Generate template with data
    $dataTemplate = $templateService->generateTemplateWithData();
    echo "✅ Template with data generated: " . basename($dataTemplate) . "\n";
    
    // Clean up template files
    unlink($basicTemplate);
    unlink($dataTemplate);
    
} catch (\Exception $e) {
    echo "❌ Template generation failed: " . $e->getMessage() . "\n";
}

// Clean up test file
if (file_exists($testFile)) {
    unlink($testFile);
    echo "\nTest file cleaned up.\n";
}

echo "\n=== Test Completed ===\n";

function formatBytes($bytes, $precision = 2)
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, $precision) . ' ' . $units[$i];
}
