<x-filament-panels::page>
    <div class="space-y-6">
        {{-- <!-- Header Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-calendar-days class="w-6 h-6 text-green-600 dark:text-green-400" />
                        </div>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ $outletRecord->name }}
                        </h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Code: {{ $outletRecord->code }} • Select a report date to view stock details
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500 dark:text-gray-400">Outlet ID</div>
                    <div class="text-lg font-semibold text-gray-900 dark:text-white">#{{ $outletRecord->id }}</div>
                </div>
            </div>
        </div>

        <!-- Navigation Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ static::$resource::getUrl('outlet-selection') }}" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                        <x-heroicon-o-building-storefront class="w-4 h-4 mr-2" />
                        Select Outlet
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-400" />
                        <span class="ml-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                            <x-heroicon-o-calendar-days class="w-4 h-4 mr-1 inline" />
                            Select Date
                        </span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-400" />
                        <span class="ml-1 text-sm font-medium text-gray-500 dark:text-gray-400">View Details</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Instructions -->
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <x-heroicon-o-information-circle class="h-5 w-5 text-green-400" />
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                        Available Report Dates for {{ $outletRecord->name }}
                    </h3>
                    <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                        <p>Click on any date below to view detailed stock information for that specific day. The table shows summary statistics for each reporting date.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
                $totalReports = \App\Models\ReportStock::where('outlet_id', $outlet)->distinct('report_date')->count('report_date');
                $totalProducts = \App\Models\ReportStock::where('outlet_id', $outlet)->distinct('product_id')->count('product_id');
                $latestReport = \App\Models\ReportStock::where('outlet_id', $outlet)->latest('report_date')->first();
                $totalStock = \App\Models\ReportStock::where('outlet_id', $outlet)->sum('quantity');
            @endphp
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-calendar class="h-8 w-8 text-blue-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Report Dates</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalReports) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-cube class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unique Products</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalProducts) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-chart-bar class="h-8 w-8 text-yellow-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Stock</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalStock) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-clock class="h-8 w-8 text-purple-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Latest Report</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ $latestReport ? $latestReport->report_date->format('M j') : 'No reports' }}
                        </p>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Table Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
