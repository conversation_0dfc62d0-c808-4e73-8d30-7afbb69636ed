<?php

namespace App\Filament\Resources\OutletProductResource\Pages;

use App\Filament\Resources\OutletProductResource\OutletProductResource;
use App\Imports\OutletProductImport;
use App\Exports\OutletProductTemplateExport;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class ListOutletProducts extends ListRecords
{
    protected static string $resource = OutletProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('download_template')
                ->label('Download Template')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action(function () {
                    return Excel::download(new OutletProductTemplateExport(), 'outlet_products_template.xlsx');
                }),

            Action::make('import_outlet_products')
                ->label('Import Outlet Products')
                ->icon('heroicon-o-document-arrow-up')
                ->color('success')
                ->modalHeading('Import Outlet Products')
                ->modalDescription('Upload an Excel file to import outlet product configurations. Products will be created if they don\'t exist.')
                ->modalSubmitActionLabel('Import')
                ->modalWidth('lg')
                ->fillForm(fn (): array => [])
                ->schema([
                    FileUpload::make('excel_file')
                        ->label('Excel File')
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'])
                        ->required()
                        ->disk('public')
                        ->directory('imports')
                        ->helperText('Upload Excel file with outlet product data. Download template first for correct format.'),
                ])
                ->action(function (array $data) {
                    $this->processImport($data['excel_file']);
                }),

            CreateAction::make()
                ->label('Create Configuration')
                ->icon('heroicon-m-plus')
                ->color('primary'),
        ];
    }

    public function getTitle(): string
    {
        return 'Outlet Product Configurations';
    }

    protected function processImport(string $filePath): void
    {
        try {
            // Get the full file path
            $fullPath = Storage::disk('public')->path($filePath);

            if (!file_exists($fullPath)) {
                Notification::make()
                    ->title('File Not Found')
                    ->body('The uploaded file could not be found.')
                    ->danger()
                    ->send();
                return;
            }

            // Process the import
            $import = new OutletProductImport();
            Excel::import($import, $fullPath);

            // Get statistics
            $processedRows = $import->getProcessedRows();
            $productsCreated = $import->getProductsCreated();
            $outletProductsCreated = $import->getOutletProductsCreated();
            $outletProductsUpdated = $import->getOutletProductsUpdated();
            $errors = $import->getErrors();

            // Show success notification
            $message = "Import completed successfully!\n";
            $message .= "• Processed rows: {$processedRows}\n";
            $message .= "• Products created: {$productsCreated}\n";
            $message .= "• Outlet products created: {$outletProductsCreated}\n";
            $message .= "• Outlet products updated: {$outletProductsUpdated}";

            if (!empty($errors)) {
                $message .= "\n\nErrors encountered:\n" . implode("\n", array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $message .= "\n... and " . (count($errors) - 5) . " more errors.";
                }
            }

            Notification::make()
                ->title('Import Completed')
                ->body($message)
                ->success()
                ->persistent()
                ->send();

            // Clean up the uploaded file
            Storage::disk('public')->delete($filePath);

            // Refresh the page to show new data
            $this->redirect(static::getUrl());

        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body('An error occurred during import: ' . $e->getMessage())
                ->danger()
                ->persistent()
                ->send();
        }
    }
}
