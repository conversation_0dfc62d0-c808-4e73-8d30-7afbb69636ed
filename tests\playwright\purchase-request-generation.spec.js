import { test, expect } from '@playwright/test';

test.describe('Purchase Request Generation Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Login as manager
    await page.goto('http://127.0.0.1:8000/admin/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/admin');
  });

  test('should display generate purchase request action for non-generated reports', async ({ page }) => {
    console.log('🧪 Testing Generate Purchase Request Action Visibility...');
    
    // Navigate to Report Stocks
    await page.goto('http://127.0.0.1:8000/admin/report-stocks');
    await page.waitForLoadState('networkidle');
    
    // Check if we have report dates
    const reportRows = page.locator('table tbody tr');
    const rowCount = await reportRows.count();
    console.log(`📊 Found ${rowCount} report date rows`);
    
    if (rowCount === 0) {
      console.log('❌ No report dates found - skipping test');
      return;
    }
    
    // Look for the first row with actions
    const firstRow = reportRows.first();
    
    // Check if Generate Purchase Request action is visible
    const generateAction = firstRow.locator('button:has-text("Generate Purchase Request"), [data-testid*="generate"], .fi-ac-btn:has-text("Generate")');
    
    // Wait a bit for actions to load
    await page.waitForTimeout(2000);
    
    // Check if action exists (might be in dropdown)
    const actionsButton = firstRow.locator('button[aria-label="Open actions menu"], .fi-dropdown-trigger');
    if (await actionsButton.isVisible()) {
      console.log('🔽 Opening actions dropdown...');
      await actionsButton.click();
      await page.waitForTimeout(1000);
    }
    
    // Look for generate action in various possible locations
    const possibleSelectors = [
      'button:has-text("Generate Purchase Request")',
      '[data-testid*="generate"]',
      '.fi-ac-btn:has-text("Generate")',
      'button:has-text("Generate")',
      '[title*="Generate"]'
    ];
    
    let actionFound = false;
    for (const selector of possibleSelectors) {
      const element = page.locator(selector);
      if (await element.isVisible()) {
        console.log(`✅ Found generate action with selector: ${selector}`);
        actionFound = true;
        break;
      }
    }
    
    if (!actionFound) {
      console.log('⚠️ Generate action not immediately visible, checking page content...');
      const pageContent = await page.content();
      const hasGenerateText = pageContent.includes('Generate') || pageContent.includes('generate');
      console.log(`📄 Page contains 'Generate' text: ${hasGenerateText}`);
      
      // Take screenshot for debugging
      await page.screenshot({ path: 'debug-generate-action.png' });
    }
    
    expect(actionFound || await page.locator('text=Generate').isVisible()).toBeTruthy();
  });

  test('should open generate purchase request modal and submit form', async ({ page }) => {
    console.log('🧪 Testing Generate Purchase Request Modal...');
    
    await page.goto('http://127.0.0.1:8000/admin/report-stocks');
    await page.waitForLoadState('networkidle');
    
    const reportRows = page.locator('table tbody tr');
    const rowCount = await reportRows.count();
    
    if (rowCount === 0) {
      console.log('❌ No report dates found - skipping test');
      return;
    }
    
    // Try to find and click generate action
    const firstRow = reportRows.first();
    
    // First try direct button
    let generateButton = firstRow.locator('button:has-text("Generate Purchase Request")');
    
    if (!(await generateButton.isVisible())) {
      // Try actions dropdown
      const actionsButton = firstRow.locator('button[aria-label="Open actions menu"], .fi-dropdown-trigger');
      if (await actionsButton.isVisible()) {
        await actionsButton.click();
        await page.waitForTimeout(1000);
        generateButton = page.locator('button:has-text("Generate Purchase Request")');
      }
    }
    
    if (!(await generateButton.isVisible())) {
      // Try alternative selectors
      generateButton = page.locator('button:has-text("Generate"), [data-testid*="generate"]').first();
    }
    
    if (await generateButton.isVisible()) {
      console.log('🖱️ Clicking Generate Purchase Request button...');
      await generateButton.click();
      
      // Wait for modal to open
      await page.waitForSelector('.fi-modal, [role="dialog"]', { timeout: 10000 });
      console.log('✅ Modal opened successfully');
      
      // Check modal content
      const modal = page.locator('.fi-modal, [role="dialog"]');
      await expect(modal).toBeVisible();
      
      // Look for form fields
      const thresholdField = modal.locator('input[name="low_stock_threshold"], input[id*="threshold"]');
      const multiplierField = modal.locator('input[name="purchase_multiplier"], input[id*="multiplier"]');
      const forceToggle = modal.locator('input[name="force_regenerate"], input[type="checkbox"]');
      
      if (await thresholdField.isVisible()) {
        console.log('📝 Filling threshold field...');
        await thresholdField.fill('15');
      }
      
      if (await multiplierField.isVisible()) {
        console.log('📝 Filling multiplier field...');
        await multiplierField.fill('3');
      }
      
      if (await forceToggle.isVisible()) {
        console.log('☑️ Checking force regenerate...');
        await forceToggle.check();
      }
      
      // Submit form
      const submitButton = modal.locator('button[type="submit"], button:has-text("Submit"), button:has-text("Generate")');
      if (await submitButton.isVisible()) {
        console.log('🚀 Submitting form...');
        await submitButton.click();
        
        // Wait for response
        await page.waitForTimeout(5000);
        
        // Check for success notification
        const notification = page.locator('.fi-no, .notification, [role="alert"]');
        if (await notification.isVisible()) {
          const notificationText = await notification.textContent();
          console.log('📢 Notification:', notificationText);
          
          const isSuccess = notificationText.includes('Success') || 
                           notificationText.includes('Generated') || 
                           notificationText.includes('successfully');
          
          if (isSuccess) {
            console.log('✅ Purchase request generated successfully');
          } else {
            console.log('⚠️ Generation may have failed:', notificationText);
          }
        }
      }
    } else {
      console.log('❌ Generate button not found');
      await page.screenshot({ path: 'debug-no-generate-button.png' });
    }
  });

  test('should display low stock summary modal', async ({ page }) => {
    console.log('🧪 Testing Low Stock Summary Modal...');
    
    await page.goto('http://127.0.0.1:8000/admin/report-stocks');
    await page.waitForLoadState('networkidle');
    
    const reportRows = page.locator('table tbody tr');
    const rowCount = await reportRows.count();
    
    if (rowCount === 0) {
      console.log('❌ No report dates found - skipping test');
      return;
    }
    
    // Look for low stock summary action
    const firstRow = reportRows.first();
    
    let summaryButton = firstRow.locator('button:has-text("View Low Stock Summary")');
    
    if (!(await summaryButton.isVisible())) {
      // Try actions dropdown
      const actionsButton = firstRow.locator('button[aria-label="Open actions menu"], .fi-dropdown-trigger');
      if (await actionsButton.isVisible()) {
        await actionsButton.click();
        await page.waitForTimeout(1000);
        summaryButton = page.locator('button:has-text("View Low Stock Summary"), button:has-text("Low Stock")');
      }
    }
    
    if (await summaryButton.isVisible()) {
      console.log('🖱️ Clicking Low Stock Summary button...');
      await summaryButton.click();
      
      // Wait for modal to open
      await page.waitForSelector('.fi-modal, [role="dialog"]', { timeout: 10000 });
      console.log('✅ Low Stock Summary modal opened');
      
      const modal = page.locator('.fi-modal, [role="dialog"]');
      await expect(modal).toBeVisible();
      
      // Check for summary content
      const summaryContent = modal.locator('text=Low Stock, text=Out of Stock, text=Critical');
      if (await summaryContent.first().isVisible()) {
        console.log('✅ Summary content is visible');
        
        // Check for statistics
        const stats = await modal.locator('.grid, .bg-red-50, .bg-yellow-50, .bg-blue-50').count();
        console.log(`📊 Found ${stats} statistic cards`);
        
        // Check for outlet breakdown
        const outlets = await modal.locator('h4, .font-medium').count();
        console.log(`🏪 Found ${outlets} outlet sections`);
      }
      
      // Close modal
      const closeButton = modal.locator('button:has-text("Close"), button[aria-label="Close"]');
      if (await closeButton.isVisible()) {
        await closeButton.click();
        console.log('✅ Modal closed successfully');
      }
    } else {
      console.log('❌ Low Stock Summary button not found');
      await page.screenshot({ path: 'debug-no-summary-button.png' });
    }
  });

  test('should verify purchase requests are created in database', async ({ page }) => {
    console.log('🧪 Testing Database Integration...');
    
    // First generate a purchase request via UI
    await page.goto('http://127.0.0.1:8000/admin/report-stocks');
    await page.waitForLoadState('networkidle');
    
    // Navigate to Purchase Requests to verify
    await page.goto('http://127.0.0.1:8000/admin/purchase-requests');
    await page.waitForLoadState('networkidle');
    
    // Check if purchase requests exist
    const purchaseRows = page.locator('table tbody tr');
    const purchaseCount = await purchaseRows.count();
    console.log(`📋 Found ${purchaseCount} purchase request rows`);
    
    if (purchaseCount > 0) {
      console.log('✅ Purchase requests found in database');
      
      // Check first purchase request details
      const firstPurchase = purchaseRows.first();
      const purchaseText = await firstPurchase.textContent();
      console.log('📄 First purchase request:', purchaseText);
      
      // Look for today's date in the purchase requests
      const today = new Date().toISOString().split('T')[0];
      const todayRequests = page.locator(`table tbody tr:has-text("${today}")`);
      const todayCount = await todayRequests.count();
      console.log(`📅 Purchase requests for today (${today}): ${todayCount}`);
      
      if (todayCount > 0) {
        console.log('✅ Found purchase requests generated today');
      }
    } else {
      console.log('⚠️ No purchase requests found - may need to generate first');
    }
    
    // Take screenshot of purchase requests page
    await page.screenshot({ path: 'purchase-requests-page.png' });
  });

  test('should handle error scenarios gracefully', async ({ page }) => {
    console.log('🧪 Testing Error Handling...');
    
    await page.goto('http://127.0.0.1:8000/admin/report-stocks');
    await page.waitForLoadState('networkidle');
    
    // Test with invalid parameters (if form allows)
    const reportRows = page.locator('table tbody tr');
    const rowCount = await reportRows.count();
    
    if (rowCount > 0) {
      const firstRow = reportRows.first();
      
      // Try to open generate modal
      let generateButton = firstRow.locator('button:has-text("Generate Purchase Request")');
      
      if (!(await generateButton.isVisible())) {
        const actionsButton = firstRow.locator('button[aria-label="Open actions menu"], .fi-dropdown-trigger');
        if (await actionsButton.isVisible()) {
          await actionsButton.click();
          await page.waitForTimeout(1000);
          generateButton = page.locator('button:has-text("Generate Purchase Request")');
        }
      }
      
      if (await generateButton.isVisible()) {
        await generateButton.click();
        await page.waitForSelector('.fi-modal, [role="dialog"]', { timeout: 10000 });
        
        const modal = page.locator('.fi-modal, [role="dialog"]');
        
        // Try invalid threshold (negative number)
        const thresholdField = modal.locator('input[name="low_stock_threshold"], input[id*="threshold"]');
        if (await thresholdField.isVisible()) {
          await thresholdField.fill('-5');
          console.log('📝 Entered invalid threshold: -5');
        }
        
        // Submit and check for validation errors
        const submitButton = modal.locator('button[type="submit"], button:has-text("Submit")');
        if (await submitButton.isVisible()) {
          await submitButton.click();
          await page.waitForTimeout(2000);
          
          // Check for validation errors
          const errorMessage = page.locator('.fi-fo-field-wrp-error-message, .error, [role="alert"]');
          if (await errorMessage.isVisible()) {
            const errorText = await errorMessage.textContent();
            console.log('✅ Validation error displayed:', errorText);
          }
        }
      }
    }
    
    console.log('✅ Error handling test completed');
  });
});
