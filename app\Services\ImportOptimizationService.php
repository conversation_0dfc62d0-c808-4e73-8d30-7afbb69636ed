<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportOptimizationService
{
    protected $originalSettings = [];

    /**
     * Optimize environment for large imports
     */
    public function optimizeForImport(string $importType = 'outlet_products'): void
    {
        $config = config("import.{$importType}", config('import.outlet_products'));
        
        // Store original settings
        $this->originalSettings = [
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'foreign_key_checks' => DB::select('SELECT @@foreign_key_checks as fk')[0]->fk ?? 1,
            'query_log' => DB::logging(),
        ];

        // Set optimized settings
        ini_set('memory_limit', $config['memory_limit']);
        ini_set('max_execution_time', $config['max_execution_time']);
        
        if ($config['database']['disable_foreign_key_checks'] ?? true) {
            DB::statement('SET foreign_key_checks=0');
        }
        
        if ($config['database']['disable_query_log'] ?? true) {
            DB::disableQueryLog();
        }

        Log::info('Import optimization enabled', [
            'import_type' => $importType,
            'memory_limit' => $config['memory_limit'],
            'max_execution_time' => $config['max_execution_time'],
        ]);
    }

    /**
     * Restore original environment settings
     */
    public function restoreSettings(): void
    {
        if (empty($this->originalSettings)) {
            return;
        }

        // Restore original settings
        ini_set('memory_limit', $this->originalSettings['memory_limit']);
        ini_set('max_execution_time', $this->originalSettings['max_execution_time']);
        
        if ($this->originalSettings['foreign_key_checks']) {
            DB::statement('SET foreign_key_checks=1');
        }
        
        if ($this->originalSettings['query_log']) {
            DB::enableQueryLog();
        }

        Log::info('Import optimization restored', [
            'original_settings' => $this->originalSettings,
        ]);

        $this->originalSettings = [];
    }

    /**
     * Execute import with optimization
     */
    public function executeOptimizedImport(callable $importFunction, string $importType = 'outlet_products')
    {
        $this->optimizeForImport($importType);
        
        try {
            $result = $importFunction();
            return $result;
        } finally {
            $this->restoreSettings();
        }
    }

    /**
     * Get memory usage information
     */
    public function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'current_formatted' => $this->formatBytes(memory_get_usage(true)),
            'peak' => memory_get_peak_usage(true),
            'peak_formatted' => $this->formatBytes(memory_get_peak_usage(true)),
            'limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Monitor import progress
     */
    public function logProgress(string $stage, array $stats = []): void
    {
        $memoryUsage = $this->getMemoryUsage();
        
        Log::info("Import progress: {$stage}", array_merge($stats, [
            'memory_usage' => $memoryUsage['current_formatted'],
            'memory_peak' => $memoryUsage['peak_formatted'],
            'timestamp' => now()->toISOString(),
        ]));
    }
}
