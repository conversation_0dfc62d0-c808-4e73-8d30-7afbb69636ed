<x-filament-panels::page>
    <div class="space-y-6">
        {{-- <!-- Header Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-clipboard-document-list class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                        </div>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ $outletRecord->name }}
                        </h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $reportDate->format('l, F j, Y') }} • Code: {{ $outletRecord->code }}
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500 dark:text-gray-400">Report Date</div>
                    <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $reportDate->format('M j, Y') }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $reportDate->diffForHumans() }}</div>
                </div>
            </div>
        </div>

        <!-- Navigation Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ static::$resource::getUrl('outlet-selection') }}" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                        <x-heroicon-o-building-storefront class="w-4 h-4 mr-2" />
                        Select Outlet
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-400" />
                        <a href="{{ \App\Filament\Resources\ReportStocks\Pages\DateSelection::getUrl(['outlet' => $outlet]) }}" class="ml-1 text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                            <x-heroicon-o-calendar-days class="w-4 h-4 mr-1 inline" />
                            Select Date
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-400" />
                        <span class="ml-1 text-sm font-medium text-purple-600 dark:text-purple-400">
                            <x-heroicon-o-clipboard-document-list class="w-4 h-4 mr-1 inline" />
                            View Details
                        </span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            @php
                $stockData = \App\Models\ReportStock::where('outlet_id', $outlet)
                    ->whereDate('report_date', $date)
                    ->get();
                
                $totalProducts = $stockData->count();
                $totalStock = $stockData->sum('quantity');
                $outOfStock = $stockData->where('quantity', 0)->count();
                $lowStock = 0;
                $goodStock = 0;
                
                foreach ($stockData as $stock) {
                    $outletProduct = \App\Models\OutletProduct::where('outlet_id', $stock->outlet_id)
                        ->where('product_id', $stock->product_id)
                        ->first();
                    
                    if ($outletProduct && $stock->quantity > 0) {
                        $minBuffer = $outletProduct->min_buffer ?? 10;
                        if ($stock->quantity < $minBuffer) {
                            $lowStock++;
                        } elseif ($stock->quantity > ($minBuffer * 2)) {
                            $goodStock++;
                        }
                    }
                }
            @endphp
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-cube class="h-8 w-8 text-blue-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Products</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalProducts) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-chart-bar class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Stock</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalStock) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-x-circle class="h-8 w-8 text-gray-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Out of Stock</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($outOfStock) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-exclamation-triangle class="h-8 w-8 text-red-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Low Stock</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($lowStock) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-check-circle class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Good Stock</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($goodStock) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <x-heroicon-o-information-circle class="h-5 w-5 text-purple-400" />
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-purple-800 dark:text-purple-200">
                        Stock Report Details
                    </h3>
                    <div class="mt-2 text-sm text-purple-700 dark:text-purple-300">
                        <p>This table shows detailed stock information for each product on the selected date. Use filters to find specific products or stock status categories.</p>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Table Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
