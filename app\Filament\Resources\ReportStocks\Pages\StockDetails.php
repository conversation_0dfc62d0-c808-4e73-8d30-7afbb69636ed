<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Models\Outlet;
use App\Models\ReportStockDetail;

use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions\Action;
use Carbon\Carbon;

class StockDetails extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ReportStockResource::class;
    protected static ?string $title = 'Stock Report Details';

    public $outlet;
    public $date;
    public $outletRecord;
    public $reportDate;

    public function mount($outlet, $date): void
    {
        $this->outlet = $outlet;
        $this->date = $date;
        $this->outletRecord = Outlet::findOrFail($outlet);
        $this->reportDate = Carbon::parse($date);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->description(fn ($record) => "Barcode: {$record->product->barcode} | Unit: {$record->product->unit}")
                    ->copyable(),

                TextColumn::make('quantity')
                    ->label('Stock Quantity')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(function ($record) {
                        $minBuffer = $record->min_buffer ?? 10;

                        if ($record->quantity === 0) return 'gray';
                        if ($record->quantity < $minBuffer) return 'danger';
                        if ($record->quantity <= ($minBuffer * 2)) return 'warning';
                        return 'success';
                    })
                    ->description(function ($record) {
                        $min = $record->min_buffer ?? 0;
                        $max = $record->max_buffer ?? 0;

                        return "Buffer: {$min} - {$max}";
                    }),

                TextColumn::make('outlet_pareto')
                    ->label('Outlet Pareto')
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->outlet_pareto) return 'gray';

                        return match ($record->outlet_pareto) {
                            'FM' => 'success',
                            'SM' => 'warning',
                            'BM' => 'danger',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function ($record) {
                        return $record->outlet_pareto ?? 'N/A';
                    })
                    ->description(function ($record) {
                        $pareto = $record->outlet_pareto;

                        return match ($pareto) {
                            'FM' => 'Fast Moving',
                            'SM' => 'Slow Moving',
                            'BM' => 'Bad Moving',
                            default => 'Not classified',
                        };
                    }),

                TextColumn::make('rumus_pareto')
                    ->label('Rumus Pareto')
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->rumus_pareto) return 'gray';

                        return match ($record->rumus_pareto) {
                            'FM' => 'success',
                            'SM' => 'warning',
                            'BM' => 'danger',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function ($record) {
                        return $record->rumus_pareto ?? 'N/A';
                    })
                    ->description(function ($record) {
                        $rumus = $record->rumus_pareto;

                        return match ($rumus) {
                            'FM' => 'Fast Moving',
                            'SM' => 'Slow Moving',
                            'BM' => 'Bad Moving',
                            default => 'Not classified',
                        };
                    }),

                

                TextColumn::make('product.pack_quantity')
                    ->label('Pack Size')
                    ->numeric()
                    ->badge()
                    ->color('info')
                    ->description('Items per pack'),

                TextColumn::make('created_at')
                    ->label('Reported At')
                    ->dateTime('g:i A')
                    ->sortable()
                    ->description(fn ($record) => $record->created_at->diffForHumans())
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('stock_status')
                    ->label('Stock Status')
                    ->options([
                        'out_of_stock' => 'Out of Stock',
                        'low_stock' => 'Low Stock',
                        'warning' => 'Warning',
                        'good_stock' => 'Good Stock',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (!$data['value']) return $query;

                        return match ($data['value']) {
                            'out_of_stock' => $query->where('quantity', 0),
                            'low_stock' => $query->where('quantity', '>', 0)
                                ->whereHas('product.outlets', function ($q) {
                                    $q->where('outlet_id', $this->outlet)
                                      ->whereRaw('report_stock_details.quantity < outlet_products.min_buffer');
                                }),
                            'warning' => $query->where('quantity', '>', 0)
                                ->whereHas('product.outlets', function ($q) {
                                    $q->where('outlet_id', $this->outlet)
                                      ->whereRaw('report_stock_details.quantity >= outlet_products.min_buffer')
                                      ->whereRaw('report_stock_details.quantity <= (outlet_products.min_buffer * 2)');
                                }),
                            'good_stock' => $query->where('quantity', '>', 0)
                                ->whereHas('product.outlets', function ($q) {
                                    $q->where('outlet_id', $this->outlet)
                                      ->whereRaw('report_stock_details.quantity > (outlet_products.min_buffer * 2)');
                                }),
                            default => $query,
                        };
                    }),

                SelectFilter::make('pareto')
                    ->label('Pareto Category')
                    ->options([
                        'FM' => 'Fast Moving',
                        'SM' => 'Slow Moving',
                        'BM' => 'Bad Moving',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (!$data['value']) return $query;
                        
                        return $query->whereHas('product.outlets', function ($q) use ($data) {
                            $q->where('outlet_id', $this->outlet)
                              ->where('outlet_pareto', $data['value']);
                        });
                    }),
            ])
            ->defaultSort('quantity', 'asc')
            ->searchPlaceholder('Search products...')
            ->emptyStateHeading('No Stock Data')
            ->emptyStateDescription("No stock data found for {$this->outletRecord->name} on {$this->reportDate->format('M j, Y')}.")
            ->emptyStateIcon('heroicon-o-clipboard-document-list')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): Builder
    {
        return ReportStockDetail::query()
            ->select([
                'report_stock_details.*',
                'outlet_products.outlet_pareto',
                'outlet_products.rumus_pareto',
                'outlet_products.min_buffer',
                'outlet_products.max_buffer'
            ])
            ->leftJoin('outlet_products', function ($join) {
                $join->on('outlet_products.outlet_id', '=', 'report_stock_details.outlet_id')
                     ->on('outlet_products.product_id', '=', 'report_stock_details.product_id');
            })
            ->where('report_stock_details.outlet_id', $this->outlet)
            ->whereHas('reportStock', function ($query) {
                $query->whereDate('report_date', $this->date);
            })
            ->with(['product', 'outlet', 'reportStock']);
    }





    public function getTitle(): string
    {
        return "Stock Details - {$this->outletRecord->name}";
    }

    public function getHeading(): string
    {
        return "Stock Report Details";
    }

    public function getSubheading(): ?string
    {
        return "{$this->outletRecord->name} ({$this->outletRecord->code}) - {$this->reportDate->format('l, F j, Y')}";
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back_to_outlets')
                ->label('Back to Outlets')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(static::$resource::getUrl('outlets', ['date' => $this->date])),

            Action::make('back_to_dates')
                ->label('Back to Dates')
                ->icon('heroicon-o-calendar-days')
                ->color('gray')
                ->url(static::$resource::getUrl('index')),
        ];
    }

    public function getView(): string
    {
        return 'filament.resources.report-stocks.pages.stock-details';
    }
}
