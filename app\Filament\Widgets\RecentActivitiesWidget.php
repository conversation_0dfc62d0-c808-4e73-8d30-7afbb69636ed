<?php

namespace App\Filament\Widgets;


use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class RecentActivitiesWidget extends BaseWidget
{
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public function getHeading(): ?string
    {
        return 'Recent Stock Reports';
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('outlet.name')
                    ->label('Outlet')
                    ->visible(Auth::user()?->hasRole('manager') ?? false)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reportStock.report_date')
                    ->label('Report Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantity')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since(),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([5, 10]);
    }

    protected function getTableQuery(): Builder
    {
        $user = Auth::user();

        return \App\Models\ReportStockDetail::query()
            ->with(['outlet', 'product', 'reportStock'])
            ->when($user->hasRole('admin') && $user->outlet_id, fn ($query) => $query->where('outlet_id', $user->outlet_id))
            ->latest()
            ->limit(50); // Limit to recent 50 records for performance
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'manager']) ?? false;
    }
}
