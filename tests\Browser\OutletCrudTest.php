<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use App\Models\User;
use App\Models\Outlet;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class OutletCrudTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected $manager;
    protected $admin;
    protected $outlet;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        $permissions = [
            'view_outlets', 'create_outlets', 'edit_outlets', 'delete_outlets',
            'view_products', 'create_products', 'edit_products', 'delete_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks', 'delete_report_stocks',
            'import_report_stocks', 'view_purchase_requests', 'create_purchase_requests',
            'edit_purchase_requests', 'delete_purchase_requests', 'generate_purchase_requests',
            'export_reports', 'view_all_outlets', 'manage_users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);

        // Assign permissions to roles
        $adminPermissions = [
            'view_products', 'create_products', 'edit_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks',
            'import_report_stocks', 'export_reports',
        ];
        $adminRole->syncPermissions($adminPermissions);

        $managerPermissions = [
            'view_outlets', 'create_outlets', 'edit_outlets', 'delete_outlets',
            'view_products', 'create_products', 'edit_products', 'delete_products',
            'view_report_stocks', 'create_report_stocks', 'edit_report_stocks', 'delete_report_stocks',
            'import_report_stocks', 'view_purchase_requests', 'create_purchase_requests',
            'edit_purchase_requests', 'delete_purchase_requests', 'generate_purchase_requests',
            'export_reports', 'view_all_outlets', 'manage_users',
        ];
        $managerRole->syncPermissions($managerPermissions);

        // Create outlet
        $this->outlet = Outlet::create(['name' => 'Test Outlet', 'code' => 'TEST001']);

        // Create manager user
        $this->manager = User::factory()->create([
            'username' => 'test_manager',
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        $this->manager->assignRole('manager');

        // Create admin user
        $this->admin = User::factory()->create([
            'username' => 'test_admin',
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'outlet_id' => $this->outlet->id,
        ]);
        $this->admin->assignRole('admin');
    }

    /**
     * Test manager can access outlets index page
     */
    public function test_manager_can_access_outlets_index()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->assertSee('Outlets')
                    ->assertSee('Create Outlet')
                    ->assertPresent('table')
                    ->screenshot('outlets-index-manager');
        });
    }

    /**
     * Test admin cannot access outlets index page
     */
    public function test_admin_cannot_access_outlets_index()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/outlets')
                    ->assertSee('403')
                    ->screenshot('outlets-index-admin-forbidden');
        });
    }

    /**
     * Test manager can create a new outlet
     */
    public function test_manager_can_create_outlet()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->clickLink('Create Outlet')
                    ->assertPathIs('/admin/outlets/create')
                    ->assertSee('Create Outlet')
                    ->type('name', 'Apotek Keluarga Bandung')
                    ->type('code', 'akb001')
                    ->press('Create')
                    ->waitForText('Outlet created successfully')
                    ->assertPathIs('/admin/outlets')
                    ->assertSee('Apotek Keluarga Bandung')
                    ->assertSee('AKB001')
                    ->screenshot('outlet-created');
        });

        // Verify in database
        $this->assertDatabaseHas('outlets', [
            'name' => 'Apotek Keluarga Bandung',
            'code' => 'AKB001',
        ]);
    }

    /**
     * Test outlet code is automatically uppercased
     */
    public function test_outlet_code_auto_uppercase()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets/create')
                    ->type('name', 'Test Uppercase Outlet')
                    ->type('code', 'test123')
                    ->press('Create')
                    ->waitForText('Outlet created successfully')
                    ->assertSee('TEST123')
                    ->screenshot('outlet-code-uppercase');
        });

        $this->assertDatabaseHas('outlets', [
            'name' => 'Test Uppercase Outlet',
            'code' => 'TEST123',
        ]);
    }

    /**
     * Test outlet form validation
     */
    public function test_outlet_form_validation()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets/create')
                    ->press('Create')
                    ->waitForText('The name field is required')
                    ->assertSee('The name field is required')
                    ->assertSee('The code field is required')
                    ->screenshot('outlet-validation-errors');
        });
    }

    /**
     * Test unique code validation
     */
    public function test_outlet_unique_code_validation()
    {
        // Create an outlet first
        Outlet::create(['name' => 'Existing Outlet', 'code' => 'EXIST001']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets/create')
                    ->type('name', 'Another Outlet')
                    ->type('code', 'EXIST001')
                    ->press('Create')
                    ->waitForText('The code has already been taken')
                    ->assertSee('The code has already been taken')
                    ->screenshot('outlet-unique-code-validation');
        });
    }

    /**
     * Test manager can view outlet details
     */
    public function test_manager_can_view_outlet_details()
    {
        $outlet = Outlet::factory()->create([
            'name' => 'View Test Outlet',
            'code' => 'VIEW001'
        ]);

        $this->browse(function (Browser $browser) use ($outlet) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->clickLink('View')
                    ->assertPathIs("/admin/outlets/{$outlet->id}")
                    ->assertSee('View Test Outlet')
                    ->assertSee('VIEW001')
                    ->assertSee('Outlet Information')
                    ->assertSee('Statistics')
                    ->screenshot('outlet-view-details');
        });
    }

    /**
     * Test manager can edit outlet
     */
    public function test_manager_can_edit_outlet()
    {
        $outlet = Outlet::factory()->create([
            'name' => 'Edit Test Outlet',
            'code' => 'EDIT001'
        ]);

        $this->browse(function (Browser $browser) use ($outlet) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->clickLink('Edit')
                    ->assertPathIs("/admin/outlets/{$outlet->id}/edit")
                    ->assertSee('Edit Edit Test Outlet')
                    ->clear('name')
                    ->type('name', 'Updated Outlet Name')
                    ->clear('code')
                    ->type('code', 'UPDATED001')
                    ->press('Save changes')
                    ->waitForText('Outlet updated successfully')
                    ->assertPathIs('/admin/outlets')
                    ->assertSee('Updated Outlet Name')
                    ->assertSee('UPDATED001')
                    ->screenshot('outlet-edited');
        });

        // Verify in database
        $this->assertDatabaseHas('outlets', [
            'id' => $outlet->id,
            'name' => 'Updated Outlet Name',
            'code' => 'UPDATED001',
        ]);
    }

    /**
     * Test manager can delete outlet (when no dependencies)
     */
    public function test_manager_can_delete_outlet_without_dependencies()
    {
        $outlet = Outlet::factory()->create([
            'name' => 'Delete Test Outlet',
            'code' => 'DELETE001'
        ]);

        $this->browse(function (Browser $browser) use ($outlet) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->clickLink('Delete')
                    ->waitForText('Delete Outlet')
                    ->assertSee("Are you sure you want to delete the outlet 'Delete Test Outlet'?")
                    ->press('Yes, delete it')
                    ->waitForText('Outlet deleted successfully', 10)
                    ->assertDontSee('Delete Test Outlet')
                    ->screenshot('outlet-deleted');
        });

        // Verify in database
        $this->assertDatabaseMissing('outlets', [
            'id' => $outlet->id,
        ]);
    }

    /**
     * Test manager cannot delete outlet with users
     */
    public function test_manager_cannot_delete_outlet_with_users()
    {
        $outlet = Outlet::factory()->create([
            'name' => 'Outlet With Users',
            'code' => 'USERS001'
        ]);

        // Assign a user to this outlet
        $this->admin->update(['outlet_id' => $outlet->id]);

        $this->browse(function (Browser $browser) use ($outlet) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->clickLink('Delete')
                    ->waitForText('Delete Outlet')
                    ->press('Yes, delete it')
                    ->waitForText('Cannot delete outlet', 10)
                    ->assertSee('because it has')
                    ->assertSee('user(s) assigned')
                    ->screenshot('outlet-delete-blocked-users');
        });

        // Verify outlet still exists
        $this->assertDatabaseHas('outlets', [
            'id' => $outlet->id,
        ]);
    }

    /**
     * Test outlet search functionality
     */
    public function test_outlet_search_functionality()
    {
        Outlet::factory()->create(['name' => 'Searchable Outlet Jakarta', 'code' => 'SEARCH001']);
        Outlet::factory()->create(['name' => 'Another Outlet Bandung', 'code' => 'SEARCH002']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->type('input[placeholder*="Search"]', 'Jakarta')
                    ->pause(1000) // Wait for search to process
                    ->assertSee('Searchable Outlet Jakarta')
                    ->assertDontSee('Another Outlet Bandung')
                    ->clear('input[placeholder*="Search"]')
                    ->type('input[placeholder*="Search"]', 'SEARCH002')
                    ->pause(1000)
                    ->assertSee('Another Outlet Bandung')
                    ->assertDontSee('Searchable Outlet Jakarta')
                    ->screenshot('outlet-search');
        });
    }

    /**
     * Test outlet filters
     */
    public function test_outlet_filters()
    {
        // Create outlets with and without users
        $outletWithUsers = Outlet::factory()->create(['name' => 'Outlet With Users', 'code' => 'FILTER001']);
        $outletWithoutUsers = Outlet::factory()->create(['name' => 'Outlet Without Users', 'code' => 'FILTER002']);

        // Assign user to first outlet
        $this->admin->update(['outlet_id' => $outletWithUsers->id]);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->assertSee('Outlet With Users')
                    ->assertSee('Outlet Without Users')
                    ->screenshot('outlet-filter-before');

            // Try to find and click filter elements (may vary based on Filament version)
            try {
                $browser->click('button[aria-label="Filter"]')
                        ->pause(1000)
                        ->screenshot('outlet-filter-opened');
            } catch (\Exception $exception) {
                // Alternative filter approach
                $browser->screenshot('outlet-filter-alternative');
            }
        });
    }

    /**
     * Test outlet table sorting
     */
    public function test_outlet_table_sorting()
    {
        Outlet::factory()->create(['name' => 'Alpha Outlet', 'code' => 'ALPHA001']);
        Outlet::factory()->create(['name' => 'Beta Outlet', 'code' => 'BETA001']);
        Outlet::factory()->create(['name' => 'Gamma Outlet', 'code' => 'GAMMA001']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->visit('/admin/outlets')
                    ->pause(2000) // Wait for page to load
                    ->screenshot('outlet-table-before-sort')
                    ->assertSee('Alpha Outlet')
                    ->assertSee('Beta Outlet')
                    ->assertSee('Gamma Outlet');
        });
    }

    /**
     * Test outlet statistics display
     */
    public function test_outlet_statistics_display()
    {
        $outlet = Outlet::factory()->create(['name' => 'Stats Test Outlet', 'code' => 'STATS001']);

        // Assign user to outlet to show user count
        $this->admin->update(['outlet_id' => $outlet->id]);

        $this->browse(function (Browser $browser) use ($outlet) {
            $browser->loginAs($this->manager)
                    ->visit("/admin/outlets/{$outlet->id}")
                    ->assertSee('Stats Test Outlet')
                    ->assertSee('STATS001')
                    ->screenshot('outlet-statistics');
        });
    }

    /**
     * Test responsive design on mobile
     */
    public function test_outlet_responsive_design()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->manager)
                    ->resize(375, 667) // iPhone size
                    ->visit('/admin/outlets')
                    ->assertSee('Outlets')
                    ->screenshot('outlet-mobile-view');
        });
    }
}
