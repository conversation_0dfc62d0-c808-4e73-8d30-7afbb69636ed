<?php

namespace App\Filament\Resources\OutletProductResource\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use App\Models\Outlet;
use App\Models\Product;

class OutletProductsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('outlet.name')
                    ->label('Outlet')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->copyable()
                    ->copyMessage('Outlet name copied!')
                    ->copyMessageDuration(1500),

                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->description(fn ($record) => "Barcode: {$record->product->barcode}")
                    ->copyable()
                    ->copyMessage('Product name copied!')
                    ->copyMessageDuration(1500),

                TextColumn::make('outlet_pareto')
                    ->label('Outlet Pareto')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'FM' => 'success',
                        'SM' => 'warning',
                        'BM' => 'danger',
                        default => 'gray',
                    })
                    ->description(fn ($state) => match ($state) {
                        'FM' => 'Fast Moving',
                        'SM' => 'Slow Moving',
                        'BM' => 'Bad Moving',
                        default => 'Not Set',
                    }),

                TextColumn::make('rumus_pareto')
                    ->label('Rumus Pareto')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'FM' => 'success',
                        'SM' => 'warning',
                        'BM' => 'danger',
                        default => 'gray',
                    })
                    ->description(fn ($state) => match ($state) {
                        'FM' => 'Fast Moving',
                        'SM' => 'Slow Moving',
                        'BM' => 'Bad Moving',
                        default => 'Not Set',
                    }),

                TextColumn::make('min_buffer')
                    ->label('Min Buffer')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray')
                    ->description('Minimum stock level'),

                TextColumn::make('max_buffer')
                    ->label('Max Buffer')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray')
                    ->description('Maximum stock level'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->created_at->diffForHumans()),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->description(fn ($record) => $record->updated_at->diffForHumans()),
            ])
            ->filters([
                SelectFilter::make('outlet_id')
                    ->label('Filter by Outlet')
                    ->options(Outlet::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),

                SelectFilter::make('product_id')
                    ->label('Filter by Product')
                    ->options(Product::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),

                SelectFilter::make('outlet_pareto')
                    ->label('Filter by Pareto')
                    ->options([
                        'FM' => 'FM (Fast Moving)',
                        'SM' => 'SM (Slow Moving)',
                        'BM' => 'BM (Bad Moving)',
                    ]),

                SelectFilter::make('rumus_pareto')
                    ->label('Filter by Movement')
                    ->options([
                        'FM' => 'FM (Fast Moving)',
                        'SM' => 'SM (Slow Moving)',
                        'BM' => 'BM (Bad Moving)',
                    ]),
            ])
            ->recordActions([
                ViewAction::make()
                    ->label('View')
                    ->icon('heroicon-m-eye')
                    ->color('info'),

                EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-m-pencil-square')
                    ->color('warning'),

                DeleteAction::make()
                    ->label('Delete')
                    ->icon('heroicon-m-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Outlet Product Configuration')
                    ->modalDescription(fn ($record) => "Are you sure you want to delete the configuration for '{$record->product->name}' at '{$record->outlet->name}'?")
                    ->modalSubmitActionLabel('Yes, delete it'),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Configurations')
                        ->modalDescription('Are you sure you want to delete the selected outlet product configurations? This action cannot be undone.')
                        ->modalSubmitActionLabel('Yes, delete them'),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->searchPlaceholder('Search by outlet or product name...')
            ->emptyStateHeading('No outlet product configurations found')
            ->emptyStateDescription('Create your first outlet product configuration to get started.')
            ->emptyStateIcon('heroicon-o-building-office-2')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
