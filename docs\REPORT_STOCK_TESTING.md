# Report Stock Testing Documentation

## Overview
Comprehensive testing suite untuk fitur Report Stock menggunakan Laravel Dusk (Playwright alternative untuk Laravel). Testing mencakup navigation flow, import functionality, responsive design, dan accessibility.

## 🧪 Test Categories

### **1. Navigation Flow Tests**
**File**: `tests/Browser/ReportStockNavigationTest.php`

#### **Test Cases:**
- ✅ `user_can_access_report_stock_main_page`
- ✅ `user_can_navigate_to_outlet_selection`
- ✅ `outlet_selection_displays_correct_statistics`
- ✅ `user_can_navigate_from_outlet_to_date_selection`
- ✅ `date_selection_displays_correct_breadcrumb`
- ✅ `date_selection_shows_report_dates_with_statistics`
- ✅ `user_can_navigate_from_date_to_stock_details`
- ✅ `stock_details_displays_product_information`
- ✅ `stock_details_shows_correct_breadcrumb_and_navigation`

#### **Back Navigation Tests:**
- ✅ `user_can_navigate_back_from_stock_details_to_dates`
- ✅ `user_can_navigate_back_from_stock_details_to_outlets`
- ✅ `user_can_navigate_back_from_dates_to_outlets`

#### **Filtering and Search Tests:**
- ✅ `stock_details_filters_work_correctly`
- ✅ `search_functionality_works_in_stock_details`

### **2. Import Functionality Tests**
**File**: `tests/Browser/ReportStockImportTest.php`

#### **Basic Import Tests:**
- ✅ `user_can_access_import_functionality`
- ✅ `user_can_download_template`
- ✅ `user_can_open_import_modal`
- ✅ `import_modal_has_correct_form_fields`
- ✅ `user_can_select_report_date`
- ✅ `import_form_validation_works`

#### **Data Processing Tests:**
- ✅ `user_can_upload_file_and_submit_import`
- ✅ `import_creates_products_and_stock_reports`
- ✅ `import_handles_invalid_outlet_code`
- ✅ `imported_data_appears_in_navigation_flow`

#### **UI/UX Tests:**
- ✅ `user_can_cancel_import_modal`
- ✅ `import_modal_closes_after_successful_import`

### **3. Responsive Design Tests**
**File**: `tests/Browser/ReportStockResponsiveTest.php`

#### **Mobile Responsiveness:**
- ✅ `outlet_selection_is_responsive_on_mobile`
- ✅ `date_selection_statistics_are_responsive`
- ✅ `stock_details_table_is_horizontally_scrollable_on_mobile`
- ✅ `navigation_buttons_are_touch_friendly`
- ✅ `breadcrumb_navigation_works_on_mobile`
- ✅ `statistics_cards_stack_properly_on_mobile`
- ✅ `import_modal_is_responsive`
- ✅ `filters_are_accessible_on_mobile`

#### **Multi-Device Support:**
- ✅ `outlet_selection_is_responsive_on_tablet`
- ✅ `desktop_layout_shows_all_columns`
- ✅ `tablet_layout_maintains_readability`

#### **Performance & Accessibility:**
- ✅ `page_loads_quickly_on_slow_connection`
- ✅ `keyboard_navigation_works`
- ✅ `high_contrast_mode_is_readable`
- ✅ `zoom_level_150_percent_maintains_usability`
- ✅ `print_styles_work_correctly`

## 🛠️ Setup Instructions

### **1. Install Dependencies**
```bash
# Install Laravel Dusk
composer require --dev laravel/dusk

# Install Dusk
php artisan dusk:install

# Install ChromeDriver
php artisan dusk:chrome-driver
```

### **2. Environment Configuration**
Copy `.env.dusk.local` untuk testing environment:
```bash
cp .env.dusk.local .env.dusk.local
```

### **3. Database Setup**
```bash
# Create test database
touch database/testing.sqlite

# Run migrations for testing
php artisan migrate --env=testing
```

## 🚀 Running Tests

### **Run All Tests**
```bash
php run_report_stock_tests.php
```

### **Run Specific Test Category**
```bash
# Navigation tests only
php artisan dusk tests/Browser/ReportStockNavigationTest.php

# Import tests only
php artisan dusk tests/Browser/ReportStockImportTest.php

# Responsive tests only
php artisan dusk tests/Browser/ReportStockResponsiveTest.php
```

### **Run Individual Test**
```bash
php artisan dusk --filter=user_can_navigate_to_outlet_selection
```

### **Run with Browser Visible (Debug Mode)**
```bash
DUSK_HEADLESS_DISABLED=true php artisan dusk
```

## 📊 Test Data Setup

### **Automatic Test Data Creation**
Setiap test class memiliki `setupTestData()` method yang membuat:

#### **User Data:**
- Test Manager dengan role 'manager'
- Test Admin dengan role 'admin' (untuk role-specific tests)

#### **Outlet Data:**
- Test Outlet 1 (TEST01)
- Test Outlet 2 (TEST02)

#### **Product Data:**
- Test Product 1 (barcode: TEST001, unit: tablet)
- Test Product 2 (barcode: TEST002, unit: bottle)
- Test Product 3 (barcode: TEST003, unit: box)

#### **Outlet Product Configurations:**
- Pareto categories (FM, SM, BM)
- Buffer settings (min: 10, max: 50)

#### **Report Stock Data:**
- Multiple dates (today, yesterday, 2 days ago)
- Various quantities for testing color coding
- Different outlets for multi-outlet testing

## 🔧 Test Utilities

### **Custom Helper Methods**

#### **File Creation for Import Tests:**
```php
protected function createTestExcelFile()
{
    // Creates CSV file with test data
    // Returns file path for upload testing
}
```

#### **Responsive Testing:**
```php
$browser->resize(375, 667) // iPhone SE
$browser->resize(768, 1024) // iPad
$browser->resize(1920, 1080) // Desktop
```

#### **Network Simulation:**
```php
// Simulate slow 3G connection
$browser->driver->getCommandExecutor()->execute([
    'cmd' => 'Network.emulateNetworkConditions',
    'params' => [
        'offline' => false,
        'latency' => 100,
        'downloadThroughput' => 750 * 1024 / 8,
        'uploadThroughput' => 250 * 1024 / 8,
    ]
]);
```

## 🎯 Test Assertions

### **Navigation Assertions:**
- URL validation: `assertUrlIs()`, `assertPathIs()`
- Content presence: `assertSee()`, `assertDontSee()`
- Element presence: `assertPresent()`, `assertMissing()`
- Element visibility: `assertVisible()`, `assertNotVisible()`

### **Form Assertions:**
- Input values: `assertInputValue()`
- Form submission: `press()`, `clickLink()`
- File uploads: `attach()`
- Modal interactions: `waitForText()`, `waitUntilMissing()`

### **Database Assertions:**
- Data creation: `assertDatabaseHas()`
- Count validation: `assertGreaterThan()`
- Model relationships: Custom assertions

### **Responsive Assertions:**
- Viewport testing: `resize()`
- Element layout: `assertPresent('.grid-cols-1')`
- Touch targets: Click and interaction tests

## 🐛 Debugging Tests

### **Screenshot on Failure**
```php
$browser->screenshot('test-failure-' . time());
```

### **Console Logs**
```php
$logs = $browser->driver->manage()->getLog('browser');
foreach ($logs as $log) {
    echo $log['message'] . "\n";
}
```

### **Page Source**
```php
echo $browser->driver->getPageSource();
```

### **Wait Strategies**
```php
$browser->waitFor('.element-selector')
        ->waitForText('Expected Text')
        ->waitForLocation('/expected/url')
        ->waitUntilMissing('.loading-spinner');
```

## 📈 Performance Benchmarks

### **Page Load Times**
- Outlet Selection: < 2 seconds
- Date Selection: < 1.5 seconds  
- Stock Details: < 3 seconds
- Import Modal: < 1 second

### **Network Conditions**
- Fast 3G: All pages load within 3 seconds
- Slow 3G: All pages load within 5 seconds
- Offline: Graceful degradation with error messages

### **Memory Usage**
- Maximum heap size: < 100MB per page
- No memory leaks during navigation
- Efficient garbage collection

## 🔒 Security Testing

### **Authentication Tests**
- Unauthorized access prevention
- Role-based access control
- Session management

### **Input Validation**
- File upload restrictions
- SQL injection prevention
- XSS protection

### **CSRF Protection**
- Form token validation
- AJAX request protection

## 📱 Accessibility Testing

### **Keyboard Navigation**
- Tab order validation
- Enter key functionality
- Escape key handling

### **Screen Reader Support**
- ARIA labels
- Semantic HTML structure
- Alt text for images

### **Color Contrast**
- WCAG AA compliance
- High contrast mode support
- Color-blind friendly design

## 🚀 CI/CD Integration

### **GitHub Actions Example**
```yaml
name: Dusk Tests
on: [push, pull_request]
jobs:
  dusk-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install Dependencies
        run: composer install
      - name: Run Dusk Tests
        run: php run_report_stock_tests.php
```

### **Test Reports**
- JUnit XML format for CI integration
- HTML reports with screenshots
- Coverage reports for JavaScript interactions

Testing suite ini memastikan bahwa fitur Report Stock berfungsi dengan baik di berbagai kondisi dan device, memberikan confidence untuk deployment ke production.
