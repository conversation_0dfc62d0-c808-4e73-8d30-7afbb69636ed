<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Outlet;

class OutletPolicy
{
    /**
     * Determine whether the user can view any outlets.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('manager') || $user->hasRole('admin');
    }

    /**
     * Determine whether the user can view the outlet.
     */
    public function view(User $user, Outlet $outlet): bool
    {
        if ($user->hasRole('manager')) {
            return true;
        }

        if ($user->hasRole('admin')) {
            return $user->outlet_id === $outlet->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create outlets.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('manager');
    }

    /**
     * Determine whether the user can update the outlet.
     */
    public function update(User $user, Outlet $outlet): bool
    {
        return $user->hasRole('manager');
    }

    /**
     * Determine whether the user can delete the outlet.
     */
    public function delete(User $user, Outlet $outlet): bool
    {
        return $user->hasRole('manager');
    }

    /**
     * Check if user can access outlet data (for filtering)
     */
    public function accessOutletData(User $user, int $outletId): bool
    {
        if ($user->hasRole('manager')) {
            return true;
        }

        if ($user->hasRole('admin')) {
            return $user->outlet_id === $outletId;
        }

        return false;
    }
}
