<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class StockTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    public function array(): array
    {
        return [
            ['2234567890001', 250, 'Paracetamol 500mg Excel', 'tablet', 10, 'A'],
            ['2234567890002', 125, 'Amoxicillin 250mg Excel', 'kapsul', 12, 'B'],
            ['2234567890003', 300, 'Vitamin C 1000mg Excel', 'tablet', 20, 'A'],
            ['2234567890004', 180, 'Ibuprofen 400mg Excel', 'tablet', 10, 'B'],
            ['2234567890005', 140, 'Antasida Tablet Excel', 'tablet', 15, 'C'],
            ['2234567890006', 95, '<PERSON><PERSON><PERSON> 100mg Excel', 'tablet', 25, 'A'],
            ['2234567890007', 220, 'Cetirizine 10mg Excel', 'tablet', 10, 'B'],
        ];
    }

    public function headings(): array
    {
        return [
            'barcode',
            'quantity',
            'nama_product',
            'satuan',
            'pack',
            'outlet_pareto',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F46E5'],
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // barcode
            'B' => 12, // quantity
            'C' => 30, // nama_product
            'D' => 15, // satuan
            'E' => 10, // pack
            'F' => 15, // outlet_pareto
        ];
    }
}
