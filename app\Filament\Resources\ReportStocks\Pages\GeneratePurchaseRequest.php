<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Models\ReportStock;
use App\Services\ReportStockPurchaseService;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Request;

class GeneratePurchaseRequest extends Page
{
    protected static string $resource = ReportStockResource::class;

    protected static ?string $title = 'Generate Purchase Request';

    protected static ?string $navigationLabel = 'Generate Purchase Request';

    public ?array $data = [];

    public function mount(): void
    {
        $this->data = [
            'report_date' => Request::get('date', now()->toDateString()),
            'low_stock_threshold' => 10,
            'purchase_multiplier' => 2,
            'force_regenerate' => false,
        ];
    }

    protected function getViewData(): array
    {
        return [
            'data' => $this->data,
            'summary' => $this->getLowStockSummary(),
        ];
    }

    public function generate(): void
    {
        $data = $this->data;
        
        $reportStock = ReportStock::where('report_date', $data['report_date'])->first();
        
        if (!$reportStock) {
            Notification::make()
                ->title('Report Not Found')
                ->body("No report stock found for date {$data['report_date']}")
                ->danger()
                ->send();
            return;
        }
        
        $service = new ReportStockPurchaseService();
        $results = $service->generatePurchaseRequest($reportStock, $data);
        
        if ($results['success']) {
            Notification::make()
                ->title('Purchase Requests Generated Successfully')
                ->body("Generated {$results['total_outlets']} purchase requests with {$results['total_products']} total products")
                ->success()
                ->send();
                
            // Redirect to purchase requests page
            $this->redirect(route('filament.admin.resources.purchase-requests.index'));
        } else {
            Notification::make()
                ->title('Purchase Request Generation Failed')
                ->body(implode(', ', $results['errors']))
                ->danger()
                ->send();
        }
    }

    public function getLowStockSummary(): array
    {
        $data = $this->data;
        
        $reportStock = ReportStock::where('report_date', $data['report_date'])->first();
        
        if (!$reportStock) {
            return [
                'total_low_stock_items' => 0,
                'outlets_affected' => 0,
                'products_affected' => 0,
                'items_by_outlet' => []
            ];
        }
        
        $service = new ReportStockPurchaseService();
        return $service->getLowStockSummary($reportStock, [
            'low_stock_threshold' => $data['low_stock_threshold'] ?? 10
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('view_summary')
                ->label('View Low Stock Summary')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->modalHeading('Low Stock Summary')
                ->modalContent(function () {
                    $summary = $this->getLowStockSummary();
                    return view('filament.modals.low-stock-summary', compact('summary'));
                })
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return 'Generate Purchase Request';
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return false; // Hide from navigation, access via URL only
    }
}
