<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\OutletProduct;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Facades\Log;

class ProductBufferUpdateImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading, SkipsEmptyRows
{
    protected $outletId;
    protected $processedRows = 0;
    protected $skippedRows = 0;
    protected $updatedProducts = 0;
    protected $notFoundProducts = 0;
    protected $errors = [];

    public function __construct(int $outletId)
    {
        $this->outletId = $outletId;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            // Skip empty rows
            if (empty($row['barcode'])) {
                $this->skippedRows++;
                return null;
            }

            // Convert barcode to string to handle numeric barcodes
            $barcode = (string) $row['barcode'];

            // Find product by barcode
            $product = Product::where('barcode', $barcode)->first();

            if (!$product) {
                $this->notFoundProducts++;
                $this->errors[] = "Product with barcode '{$barcode}' not found";
                Log::warning('Product not found during buffer update', [
                    'barcode' => $barcode,
                    'outlet_id' => $this->outletId,
                ]);
                return null;
            }

            // Find or create outlet product configuration
            $outletProduct = OutletProduct::firstOrCreate(
                [
                    'outlet_id' => $this->outletId,
                    'product_id' => $product->id,
                ],
                [
                    'outlet_pareto' => null,
                    'rumus_pareto' => null,
                    'min_buffer' => 0,
                    'max_buffer' => 0,
                ]
            );

            // Update outlet product configuration
            $updateData = [];

            if (isset($row['outlet_pareto']) && !empty($row['outlet_pareto'])) {
                $updateData['outlet_pareto'] = $row['outlet_pareto'];
            }

            if (isset($row['rumus_pareto']) && !empty($row['rumus_pareto'])) {
                $updateData['rumus_pareto'] = $row['rumus_pareto'];
            }

            if (isset($row['min_buffer']) && is_numeric($row['min_buffer'])) {
                $updateData['min_buffer'] = (int) $row['min_buffer'];
            }

            if (isset($row['max_buffer']) && is_numeric($row['max_buffer'])) {
                $updateData['max_buffer'] = (int) $row['max_buffer'];
            }

            if (!empty($updateData)) {
                $outletProduct->update($updateData);
                $this->updatedProducts++;

                Log::info('Outlet product configuration updated', [
                    'outlet_product_id' => $outletProduct->id,
                    'product_id' => $product->id,
                    'barcode' => $barcode,
                    'outlet_id' => $this->outletId,
                    'updated_fields' => $updateData,
                ]);
            }

            $this->processedRows++;
            return null; // We're not creating new models, just updating existing ones

        } catch (\Exception $e) {
            $this->errors[] = "Row error: " . $e->getMessage();
            $this->skippedRows++;
            Log::error('Product buffer update error', [
                'row' => $row,
                'error' => $e->getMessage(),
                'outlet_id' => $this->outletId,
            ]);
            return null;
        }
    }

    public function rules(): array
    {
        return [
            'barcode' => 'required',
            'outlet_pareto' => 'nullable|string|in:A,B,C',
            'rumus_pareto' => 'nullable|string|in:FM,SM,BM',
            'min_buffer' => 'nullable|numeric|min:0',
            'max_buffer' => 'nullable|numeric|min:0',
        ];
    }

    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getSkippedRows(): int
    {
        return $this->skippedRows;
    }

    public function getUpdatedProducts(): int
    {
        return $this->updatedProducts;
    }

    public function getNotFoundProducts(): int
    {
        return $this->notFoundProducts;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getImportSummary(): string
    {
        $summary = [];

        if ($this->processedRows > 0) {
            $summary[] = "Processed {$this->processedRows} rows";
        }

        if ($this->updatedProducts > 0) {
            $summary[] = "Updated {$this->updatedProducts} products";
        }

        if ($this->notFoundProducts > 0) {
            $summary[] = "{$this->notFoundProducts} products not found";
        }

        if ($this->skippedRows > 0) {
            $summary[] = "Skipped {$this->skippedRows} rows";
        }

        if (count($this->errors) > 0) {
            $summary[] = "Encountered " . count($this->errors) . " errors";
        }

        return implode('. ', $summary) . '.';
    }

    /**
     * Batch size for Excel processing optimization
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for large Excel files
     */
    public function chunkSize(): int
    {
        return 500;
    }
}
