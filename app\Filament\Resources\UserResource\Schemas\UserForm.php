<?php

namespace App\Filament\Resources\UserResource\Schemas;

use App\Models\Outlet;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('username')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->alphaDash()
                    ->helperText('Username must be unique and can only contain letters, numbers, dashes, and underscores'),

                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->helperText('Full name of the user'),

                TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->helperText('Email address must be unique'),

                TextInput::make('phone')
                    ->tel()
                    ->maxLength(255)
                    ->helperText('Phone number (optional)'),

                TextInput::make('password')
                    ->password()
                    ->required(fn (string $context): bool => $context === 'create')
                    ->dehydrated(fn ($state): bool => filled($state))
                    ->dehydrateStateUsing(fn ($state): string => Hash::make($state))
                    ->minLength(8)
                    ->helperText('Minimum 8 characters. Leave empty to keep current password when editing.'),

                Select::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload()
                    ->required()
                    ->options(Role::pluck('name', 'id'))
                    ->helperText('Select user roles. Admin users are restricted to their assigned outlet, while managers have access to all outlets.')
                    ->reactive()
                    ->afterStateUpdated(function (callable $set, $state) {
                        // If manager role is selected, clear outlet_id
                        if (in_array('manager', Role::whereIn('id', $state ?? [])->pluck('name')->toArray())) {
                            $set('outlet_id', null);
                        }
                    }),

                Select::make('outlet_id')
                    ->relationship('outlet', 'name')
                    ->options(Outlet::pluck('name', 'id'))
                    ->searchable()
                    ->preload()
                    ->helperText('Select outlet for admin users. Managers can access all outlets and should not have an outlet assigned.')
                    ->hidden(function (callable $get) {
                        $roleIds = $get('roles') ?? [];
                        $roleNames = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
                        return in_array('manager', $roleNames);
                    })
                    ->required(function (callable $get) {
                        $roleIds = $get('roles') ?? [];
                        $roleNames = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
                        return in_array('admin', $roleNames) && !in_array('manager', $roleNames);
                    }),
            ]);
    }
}
