<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use Illuminate\Support\Facades\Log;

echo "=== Testing Single Log Output ===\n\n";

// Clean up any existing test data
Product::where('barcode', 'SINGLE_LOG_001')->delete();
$outlet = Outlet::firstOrCreate(['code' => 'SINGLELOG001'], ['name' => 'Single Log Test Outlet']);

echo "Using outlet: {$outlet->code} (ID: {$outlet->id})\n\n";

// Capture log messages
$logMessages = [];
Log::listen(function ($level, $message, $context) use (&$logMessages) {
    if ($level === 'info' && strpos($message, 'Report Stock Import') !== false) {
        $logMessages[] = [
            'level' => $level,
            'message' => $message,
            'context' => $context,
        ];
    }
});

// Create test data
$testData = collect([
    [
        'outlet' => 'SINGLELOG001',
        'nama_produk' => 'Single Log Test Product',
        'prt' => 'A',
        'barcode' => 'SINGLE_LOG_001',
        'pack' => '1',
        'qty' => 100,
        'sat' => 'PCS',
    ],
]);

echo "Testing import with single log output...\n";

try {
    $import = new ReportStockBulkImport('2025-09-12');
    $import->collection($testData);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Show captured log messages
    echo "Captured Log Messages:\n";
    if (empty($logMessages)) {
        echo "  No log messages captured\n";
    } else {
        foreach ($logMessages as $log) {
            echo "  [{$log['level']}] {$log['message']}\n";
            if (!empty($log['context'])) {
                echo "    Context:\n";
                foreach ($log['context'] as $key => $value) {
                    echo "      {$key}: {$value}\n";
                }
            }
        }
    }
    
    // Show import statistics
    $summary = $import->getImportSummary();
    echo "\nImport Summary:\n";
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "  {$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
Product::where('barcode', 'SINGLE_LOG_001')->delete();
if ($outlet->code === 'SINGLELOG001' && $outlet->outletProducts()->count() === 0) {
    $outlet->delete();
}
echo "✅ Cleanup completed\n";

echo "\n=== Test Completed ===\n";
