<?php

namespace App\Filament\Resources\ReportStocks\Pages;

use App\Filament\Resources\ReportStocks\ReportStockResource;
use App\Models\Outlet;
use App\Models\ReportStock;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class OutletSelection extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ReportStockResource::class;
    protected static ?string $title = 'Select Outlet';
    protected static ?string $navigationLabel = 'Report Stocks';

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('code')
                    ->label('Outlet Code')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable(),

                TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => "Code: {$record->code}")
                    ->weight('medium'),

                TextColumn::make('total_reports')
                    ->label('Total Reports')
                    ->badge()
                    ->color('primary')
                    ->getStateUsing(function ($record) {
                        $count = ReportStock::whereHas('details', function ($q) use ($record) {
                            $q->where('outlet_id', $record->id);
                        })->count();
                        return number_format($count);
                    })
                    ->description('Total stock reports'),

                TextColumn::make('latest_report')
                    ->label('Latest Report')
                    ->badge()
                    ->color('success')
                    ->getStateUsing(function ($record) {
                        $latest = ReportStock::where('outlet_id', $record->id)
                            ->latest('report_date')
                            ->first();

                        return $latest ? $latest->report_date->format('M j, Y') : 'No reports';
                    })
                    ->description(function ($record) {
                        $latest = ReportStock::where('outlet_id', $record->id)
                            ->latest('report_date')
                            ->first();

                        return $latest ? $latest->report_date->diffForHumans() : 'No stock reports yet';
                    }),

                TextColumn::make('unique_products')
                    ->label('Products')
                    ->badge()
                    ->color('warning')
                    ->getStateUsing(function ($record) {
                        $count = ReportStock::where('outlet_id', $record->id)
                            ->distinct('product_id')
                            ->count('product_id');
                        return number_format($count);
                    })
                    ->description('Unique products reported'),
            ])
            ->recordAction('viewDates')
            ->recordUrl(fn ($record) => static::$resource::getUrl('dates', ['outlet' => $record->id]))
            ->defaultSort('name')
            ->searchPlaceholder('Search outlets...')
            ->emptyStateHeading('No Outlets Found')
            ->emptyStateDescription('No outlets are available for stock reporting.')
            ->emptyStateIcon('heroicon-o-building-office')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    protected function getTableQuery(): Builder
    {
        $user = Auth::user();
        
        $query = Outlet::query();
        
        // For admin users, scope to their outlet only
        if ($user?->hasRole('admin') && $user->outlet_id) {
            $query->where('id', $user->outlet_id);
        }
        
        // Only show outlets that have stock reports
        $query->whereHas('reportStockDetails');
        
        return $query;
    }



    public function getTitle(): string
    {
        return 'Select Outlet for Stock Reports';
    }

    public function getHeading(): string
    {
        return 'Stock Reports by Outlet';
    }

    public function getSubheading(): ?string
    {
        return 'Choose an outlet to view stock report dates and details.';
    }

    public function getView(): string
    {
        return 'filament.resources.report-stocks.pages.outlet-selection';
    }
}
