<?php

namespace App\Filament\Resources\Products\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Product Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter product name')
                    ->helperText('The name of the product'),

                TextInput::make('barcode')
                    ->label('Barcode')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true)
                    ->placeholder('Enter product barcode')
                    ->helperText('Unique barcode identifier for the product'),

                TextInput::make('unit')
                    ->label('Unit')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('e.g., pcs, box, bottle')
                    ->helperText('Unit of measurement for the product'),

                TextInput::make('pack_quantity')
                    ->label('Pack Quantity')
                    ->numeric()
                    ->default(1)
                    ->required()
                    ->minValue(1)
                    ->placeholder('Enter pack quantity')
                    ->helperText('Number of units per pack'),
            ]);
    }
}
