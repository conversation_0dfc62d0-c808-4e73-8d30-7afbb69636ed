<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStockDetail;

echo "=== Testing Final Fix ===\n\n";

// Clean up any existing test data
Product::where('barcode', 'FINAL_TEST_001')->delete();
$outlet = Outlet::firstOrCreate(['code' => 'FINAL001'], ['name' => 'Final Test Outlet']);

echo "Using outlet: {$outlet->code} (ID: {$outlet->id})\n";

// Get initial counts
$initialCounts = [
    'products' => Product::count(),
    'outlet_products' => OutletProduct::count(),
    'report_stock_details' => ReportStockDetail::count(),
];

echo "Initial counts: Products={$initialCounts['products']}, OutletProducts={$initialCounts['outlet_products']}, ReportDetails={$initialCounts['report_stock_details']}\n\n";

// Create test data
$testData = collect([
    [
        'outlet' => 'FINAL001',
        'nama_produk' => 'Final Test Product',
        'prt' => 'A',
        'barcode' => 'FINAL_TEST_001',
        'pack' => '1',
        'qty' => 100,
        'sat' => 'PCS',
    ],
]);

echo "Testing import...\n";

try {
    $import = new ReportStockBulkImport('2025-09-12');
    $import->collection($testData);
    
    echo "✅ Import completed successfully!\n\n";
    
    // Get final counts
    $finalCounts = [
        'products' => Product::count(),
        'outlet_products' => OutletProduct::count(),
        'report_stock_details' => ReportStockDetail::count(),
    ];
    
    echo "Final counts: Products={$finalCounts['products']}, OutletProducts={$finalCounts['outlet_products']}, ReportDetails={$finalCounts['report_stock_details']}\n";
    
    $changes = [
        'products' => $finalCounts['products'] - $initialCounts['products'],
        'outlet_products' => $finalCounts['outlet_products'] - $initialCounts['outlet_products'],
        'report_stock_details' => $finalCounts['report_stock_details'] - $initialCounts['report_stock_details'],
    ];
    
    echo "Changes: Products=+{$changes['products']}, OutletProducts=+{$changes['outlet_products']}, ReportDetails=+{$changes['report_stock_details']}\n\n";
    
    // Verify the data
    $product = Product::where('barcode', 'FINAL_TEST_001')->first();
    if ($product) {
        echo "✅ Product created: ID={$product->id}, Barcode={$product->barcode}, Name={$product->name}\n";
        
        $outletProduct = OutletProduct::where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();
        if ($outletProduct) {
            echo "✅ Outlet product created: Pareto={$outletProduct->outlet_pareto}\n";
        } else {
            echo "❌ Outlet product not found\n";
        }
        
        $reportDetail = ReportStockDetail::where('outlet_id', $outlet->id)
            ->where('product_id', $product->id)
            ->first();
        if ($reportDetail) {
            echo "✅ Report stock detail created: Quantity={$reportDetail->quantity}\n";
        } else {
            echo "❌ Report stock detail not found\n";
        }
    } else {
        echo "❌ Product not created\n";
    }
    
    // Show import statistics
    $summary = $import->getImportSummary();
    echo "\nImport Summary:\n";
    foreach ($summary as $key => $value) {
        if (is_array($value)) {
            echo "  {$key}: " . implode(', ', $value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
Product::where('barcode', 'FINAL_TEST_001')->delete();
if ($outlet->code === 'FINAL001' && $outlet->outletProducts()->count() === 0) {
    $outlet->delete();
}
echo "✅ Cleanup completed\n";

echo "\n=== Test Completed ===\n";
