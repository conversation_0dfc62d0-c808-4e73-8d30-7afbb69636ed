<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $heading }}</h2>
                @if($view_type === 'dates')
                    <p class="text-sm text-gray-600 dark:text-gray-400">Click on any date to view outlets with purchase requests on that date.</p>
                @elseif($view_type === 'outlets')
                    <p class="text-sm text-gray-600 dark:text-gray-400">Click on any outlet to view the products requested on this date.</p>
                    <div class="mt-2">
                        <a href="{{ \App\Filament\Pages\PurchaseRequestReports::getUrl() }}"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            ← Back to Dates
                        </a>
                    </div>
                @else
                    <p class="text-sm text-gray-600 dark:text-gray-400">Detailed list of all products requested with their quantities and specifications.</p>
                    <div class="mt-2">
                        <a href="{{ \App\Filament\Pages\PurchaseRequestReports::getUrl(['view' => 'outlets', 'date' => $date]) }}"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            ← Back to Outlets
                        </a>
                    </div>
                @endif
            </div>

            @if($view_type === 'dates')
                @include('filament.pages.partials.dates-table', ['data' => $reportData])
            @elseif($view_type === 'outlets')
                @include('filament.pages.partials.outlets-table', ['data' => $reportData, 'date' => $date])
            @else
                @include('filament.pages.partials.products-table', ['data' => $reportData])
            @endif
        </div>
    </div>
</x-filament-panels::page>
