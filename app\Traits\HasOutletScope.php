<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

trait HasOutletScope
{
    /**
     * Apply outlet scope based on user role
     */
    public static function scopeForCurrentUser(Builder $query): Builder
    {
        $user = Auth::user();
        
        if (!$user) {
            return $query->whereRaw('1 = 0'); // Return no results if not authenticated
        }

        // Manager can see all data
        if ($user->hasRole('manager')) {
            return $query;
        }

        // Admin can only see their outlet's data
        if ($user->hasRole('admin') && $user->outlet_id) {
            return $query->where('outlet_id', $user->outlet_id);
        }

        // Default: no access
        return $query->whereRaw('1 = 0');
    }

    /**
     * Get available outlets for current user
     */
    public static function getAvailableOutlets(): array
    {
        $user = Auth::user();
        
        if (!$user) {
            return [];
        }

        if ($user->hasRole('manager')) {
            return \App\Models\Outlet::pluck('name', 'id')->toArray();
        }

        if ($user->hasRole('admin') && $user->outlet_id) {
            return \App\Models\Outlet::where('id', $user->outlet_id)
                ->pluck('name', 'id')
                ->toArray();
        }

        return [];
    }
}
