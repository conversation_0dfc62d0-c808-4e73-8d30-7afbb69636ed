<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use App\Imports\ReportStockBulkImport;
use App\Models\Outlet;

function formatBytes($bytes, $precision = 2)
{
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) { $bytes /= 1024; }
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== Dev Import Test ===\n";
$start = microtime(true);
$memStart = memory_get_usage();

$outlet = Outlet::first();
if (!$outlet) {
    $outlet = Outlet::create(['name' => 'TEST OUTLET', 'code' => 'TEST01']);
}
$outletCode = $outlet->code;

echo "Using outlet code: {$outletCode}\n";

// Build sample rows (moderate size to be safe)
$total = 5000; // adjust if needed
$rows = [];
$prtOptions = ['FM','SM','BM'];
for ($i = 1; $i <= $total; $i++) {
    $rows[] = [
        'OUTLET' => $outletCode,
        'NAMA PRODUK' => 'Product '.$i,
        'PRT' => $prtOptions[array_rand($prtOptions)],
        'BARCODE' => 'DEVTEST'.str_pad($i, 8, '0', STR_PAD_LEFT),
        'PACK' => rand(1, 10),
        'QTY' => rand(0, 100),
        'SAT' => 'Pcs',
    ];
}

$collection = new Collection($rows);
$import = new ReportStockBulkImport(date('Y-m-d'));

try {
    DB::disableQueryLog();
    $t1 = microtime(true);
    $import->collection($collection);
    $t2 = microtime(true);

    echo "Import OK\n";
    echo "Processed rows: ".$import->getProcessedRows()."\n";
    echo "Products created: ".$import->getProductsCreated()."\n";
    echo "Report stocks created: ".$import->getReportStocksCreated()."\n";
    echo "Report stocks updated: ".$import->getReportStocksUpdated()."\n";

    echo "Duration (collection only): ".round(($t2-$t1)*1000,2)." ms\n";

} catch (\Throwable $e) {
    echo "Import FAILED: ".$e->getMessage()."\n";
    echo $e->getFile().':'.$e->getLine()."\n";
}

$memEnd = memory_get_usage();
$end = microtime(true);

echo "Total duration: ".round(($end-$start)*1000,2)." ms\n";
echo "Mem used: ".formatBytes($memEnd - $memStart)." (peak ".formatBytes(memory_get_peak_usage()).")\n";

echo "\nLast 50 lines of laravel.log:\n";
$logPath = __DIR__.'/../storage/logs/laravel.log';
if (file_exists($logPath)) {
    $lines = @file($logPath);
    if ($lines !== false) {
        $last = array_slice($lines, -50);
        foreach ($last as $line) echo $line;
    }
}

echo "\n=== End ===\n";

