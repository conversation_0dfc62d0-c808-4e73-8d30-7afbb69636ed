<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\ReportStock;
use Carbon\Carbon;

echo "=== Test Notification Message ===\n\n";

// Simulate import results
$reportDate = '2025-09-12';
$reportStock = ReportStock::where('report_date', $reportDate)->first();

if (!$reportStock) {
    echo "No report stock found for date: {$reportDate}\n";
    echo "Creating sample data...\n";
    
    // Create sample report stock
    $reportStock = ReportStock::create([
        'report_date' => $reportDate,
        'is_generated' => false
    ]);
    
    echo "Created report stock with ID: {$reportStock->id}\n";
}

// Get statistics
$totalDetails = $reportStock->details()->count();
$totalOutlets = $reportStock->details()->distinct('outlet_id')->count();
$totalProducts = $reportStock->details()->distinct('product_id')->count();
$totalQuantity = $reportStock->details()->sum('quantity');

echo "Current statistics:\n";
echo "  Details: {$totalDetails}\n";
echo "  Outlets: {$totalOutlets}\n";
echo "  Products: {$totalProducts}\n";
echo "  Total Quantity: {$totalQuantity}\n\n";

// Build the notification message (same as in ListReportStocks.php)
$message = "✅ Import completed successfully!\n\n";

$message .= "📊 Import Summary:\n";
$message .= "• Report Date: " . Carbon::parse($reportDate)->format('M j, Y') . "\n";
$message .= "• Stock details imported: " . number_format($totalDetails) . "\n";
$message .= "• Outlets involved: " . number_format($totalOutlets) . "\n";
$message .= "• Unique products: " . number_format($totalProducts) . "\n";
$message .= "• Total stock quantity: " . number_format($totalQuantity) . "\n\n";

$message .= "🏪 Data Processing:\n";
$message .= "• Products and outlet relationships have been created/updated as needed\n";
$message .= "• Stock quantities have been imported for the specified date\n";
$message .= "• All data has been validated and processed successfully\n\n";

$message .= "✨ Import completed without errors!\n";
$message .= "💡 You can now view the imported stock data in the reports section.";

echo "=== Notification Preview ===\n";
echo $message . "\n\n";

echo "=== HTML Version (for Filament) ===\n";
$htmlMessage = str_replace("\n", "<br>", $message);
echo $htmlMessage . "\n\n";

echo "=== Test Complete ===\n";
