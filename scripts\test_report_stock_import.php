<?php

require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ReportStockBulkImport;

function formatBytes($bytes, $precision = 2)
{
    $units = ['B','KB','MB','GB','TB'];
    for ($i=0; $bytes>1024 && $i<count($units)-1; $i++) { $bytes/=1024; }
    return round($bytes, $precision).' '.$units[$i];
}

function formatTime($seconds)
{
    if ($seconds < 60) {
        return round($seconds, 2) . ' seconds';
    } elseif ($seconds < 3600) {
        return round($seconds / 60, 2) . ' minutes';
    } else {
        return round($seconds / 3600, 2) . ' hours';
    }
}

$path = $argv[1] ?? 'D:\laragon\www\apotek-keluarga\REKAP SOH OUTLET 07092025.xlsx';
$date = $argv[2] ?? '2025-09-07';

if (!file_exists($path)) {
    fwrite(STDERR, "File not found: {$path}\nUsage: php scripts/test_report_stock_import.php [<path>] [YYYY-MM-DD]\n");
    exit(1);
}

echo "\n=== Report Stock Bulk Import Test ===\n";
echo "File: {$path}\n";
echo "Date: {$date}\n";
echo "File size: " . formatBytes(filesize($path)) . "\n";

// Check database connection
try {
    DB::connection()->getPdo();
    echo "Database: Connected\n";
} catch (\Exception $e) {
    echo "Database: Connection failed - " . $e->getMessage() . "\n";
    exit(1);
}

// Get initial counts
$initialCounts = [
    'products' => DB::table('products')->count(),
    'outlets' => DB::table('outlets')->count(),
    'outlet_products' => DB::table('outlet_products')->count(),
    'report_stocks' => DB::table('report_stocks')->count(),
    'report_stock_details' => DB::table('report_stock_details')->count(),
];

echo "\nInitial counts:\n";
foreach ($initialCounts as $table => $count) {
    echo "  {$table}: {$count}\n";
}

echo "\nStarting import...\n";

$import = new ReportStockBulkImport($date);
$start = microtime(true);
$memStart = memory_get_usage(true);

try {
    // Use direct import for testing (not queued)
    Excel::import($import, $path);
    $success = true;
    echo "\nImport completed successfully!\n";
} catch (\Throwable $e) {
    $success = false;
    echo "\nImport FAILED: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

$memEnd = memory_get_usage(true);
$end = microtime(true);
$duration = $end - $start;

// Get final counts
$finalCounts = [
    'products' => DB::table('products')->count(),
    'outlets' => DB::table('outlets')->count(),
    'outlet_products' => DB::table('outlet_products')->count(),
    'report_stocks' => DB::table('report_stocks')->count(),
    'report_stock_details' => DB::table('report_stock_details')->count(),
];

echo "\n=== Import Statistics ===\n";
echo "Processed rows: " . $import->getProcessedRows() . "\n";
echo "Products created: " . $import->getProductsCreated() . "\n";
echo "Outlet products created: " . $import->getOutletProductsCreated() . "\n";
echo "Outlet products updated: " . $import->getOutletProductsUpdated() . "\n";
echo "Report stocks created: " . $import->getReportStocksCreated() . "\n";
echo "Report stocks updated: " . $import->getReportStocksUpdated() . "\n";
echo "Errors: " . count($import->getErrors()) . "\n";

if (!empty($import->getErrors())) {
    echo "\nFirst 10 errors:\n";
    foreach (array_slice($import->getErrors(), 0, 10) as $error) {
        echo "  Row {$error['row']}: {$error['error']}\n";
    }
}

echo "\n=== Performance Metrics ===\n";
echo "Duration: " . formatTime($duration) . "\n";
echo "Memory used: " . formatBytes($memEnd - $memStart) . "\n";
echo "Peak memory: " . formatBytes(memory_get_peak_usage(true)) . "\n";

if ($import->getProcessedRows() > 0) {
    $rowsPerSecond = $import->getProcessedRows() / $duration;
    echo "Processing speed: " . round($rowsPerSecond, 2) . " rows/second\n";
}

echo "\n=== Database Changes ===\n";
foreach ($finalCounts as $table => $finalCount) {
    $initialCount = $initialCounts[$table];
    $change = $finalCount - $initialCount;
    $changeStr = $change > 0 ? "+{$change}" : ($change < 0 ? "{$change}" : "0");
    echo "  {$table}: {$initialCount} -> {$finalCount} ({$changeStr})\n";
}

// Check specific report stock details for the date
$reportStockDetailsForDate = DB::table('report_stock_details')
    ->join('report_stocks', 'report_stock_details.report_stock_id', '=', 'report_stocks.id')
    ->where('report_stocks.report_date', $date)
    ->count();

echo "\nReport stock details for {$date}: {$reportStockDetailsForDate}\n";

echo "\n=== Test " . ($success ? "PASSED" : "FAILED") . " ===\n";
exit($success ? 0 : 1);
