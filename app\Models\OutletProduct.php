<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OutletProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'outlet_id',
        'product_id',
        'outlet_pareto',
        'rumus_pareto',
        'min_buffer',
        'max_buffer',
    ];

    protected $casts = [
        'min_buffer' => 'integer',
        'max_buffer' => 'integer',
    ];

    public function outlet(): BelongsTo
    {
        return $this->belongsTo(Outlet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Scopes
    public function scopeForOutlet($query, $outletId)
    {
        return $query->where('outlet_id', $outletId);
    }

    public function scopeWithPareto($query)
    {
        return $query->whereNotNull('outlet_pareto');
    }

    public function scopeWithBuffer($query)
    {
        return $query->where(function ($q) {
            $q->where('min_buffer', '>', 0)
              ->orWhere('max_buffer', '>', 0);
        });
    }

    // Helper methods
    public function getDisplayName(): string
    {
        return "{$this->product->name} @ {$this->outlet->name}";
    }

    public function isBufferConfigured(): bool
    {
        return $this->min_buffer > 0 || $this->max_buffer > 0;
    }

    public function hasPareto(): bool
    {
        return !empty($this->outlet_pareto);
    }
}
