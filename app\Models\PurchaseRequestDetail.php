<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\HasOutletScope;

class PurchaseRequestDetail extends Model
{
    use HasFactory, HasOutletScope;

    protected $fillable = [
        'purchase_request_id',
        'outlet_id',
        'product_id',
        'purchase_quantity',
    ];

    protected $casts = [
        'purchase_quantity' => 'integer',
    ];

    public function purchaseRequest(): BelongsTo
    {
        return $this->belongsTo(PurchaseRequest::class);
    }

    public function outlet(): BelongsTo
    {
        return $this->belongsTo(Outlet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Scopes
    public function scopeForOutletAndDate($query, $outletId, $date)
    {
        return $query->where('outlet_id', $outletId)
            ->whereHas('purchaseRequest', function ($q) use ($date) {
                $q->where('purchase_request_date', $date);
            });
    }

    public function scopeForDate($query, $date)
    {
        return $query->whereHas('purchaseRequest', function ($q) use ($date) {
            $q->where('request_date', $date);
        });
    }

    public function scopePending($query)
    {
        return $query->whereHas('purchaseRequest', function ($q) {
            $q->where('is_processed', false);
        });
    }

    // Helper methods
    public function getDisplayName(): string
    {
        return "{$this->product->name} - {$this->outlet->name} ({$this->purchaseRequest->request_date->format('Y-m-d')})";
    }

    public function getTotalValue(): float
    {
        return $this->purchase_quantity * ($this->product->pack_quantity ?? 1);
    }
}
