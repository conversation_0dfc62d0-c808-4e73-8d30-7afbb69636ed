<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PurchaseRequest;
use App\Models\PurchaseRequestDetail;
use App\Models\Outlet;
use App\Models\Product;
use Carbon\Carbon;

class PurchaseRequestSampleSeeder extends Seeder
{
    public function run(): void
    {
        // Get existing outlets and products
        $outlets = Outlet::all();
        $products = Product::all();

        if ($outlets->isEmpty() || $products->isEmpty()) {
            $this->command->info('No outlets or products found. Please seed outlets and products first.');
            return;
        }

        // Create sample purchase requests for the last 7 days
        for ($i = 0; $i < 7; $i++) {
            $date = Carbon::now()->subDays($i);

            // Create 1-3 purchase requests per day (one per outlet to avoid unique constraint)
            $outletsForDay = $outlets->shuffle()->take(rand(1, min(3, $outlets->count())));

            foreach ($outletsForDay as $outlet) {
                // Check if purchase request already exists for this outlet and date
                $existingRequest = PurchaseRequest::where('outlet_id', $outlet->id)
                    ->where('request_date', $date->format('Y-m-d'))
                    ->first();

                if ($existingRequest) {
                    continue; // Skip if already exists
                }

                $purchaseRequest = PurchaseRequest::create([
                    'outlet_id' => $outlet->id,
                    'request_date' => $date->format('Y-m-d'),
                    'is_processed' => false,
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);

                // Add 3-8 products to each purchase request
                $productsCount = rand(3, 8);
                $selectedProducts = $products->random($productsCount);

                foreach ($selectedProducts as $product) {
                    PurchaseRequestDetail::create([
                        'purchase_request_id' => $purchaseRequest->id,
                        'outlet_id' => $outlet->id,
                        'product_id' => $product->id,
                        'purchase_quantity' => rand(5, 100),
                        'created_at' => $date,
                        'updated_at' => $date,
                    ]);
                }
            }
        }

        $this->command->info('Sample purchase request data created successfully!');
    }
}
