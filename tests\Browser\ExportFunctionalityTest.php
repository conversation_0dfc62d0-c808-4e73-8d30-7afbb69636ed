<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use App\Models\User;
use App\Models\Product;
use App\Models\ReportStock;
use App\Models\PurchaseRequest;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class ExportFunctionalityTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'OutletSeeder']);
        $this->artisan('db:seed', ['--class' => 'UserSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    public function test_admin_can_export_products()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Export to Excel')
                    ->click('Export to Excel')
                    ->waitFor('.fi-no-notification', 5); // Wait for notification to appear and disappear
        });
    }

    public function test_manager_can_export_purchase_requests()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Export to Excel')
                    ->click('Export to Excel')
                    ->waitFor('.fi-no-notification', 5);
        });
    }

    public function test_stock_report_export_functionality()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // First create a report stock
            $browser->loginAs($admin)
                    ->visit('/admin/report-stocks')
                    ->click('@create-button')
                    ->waitForText('Create report stock')
                    ->press('Create')
                    ->waitForLocation('/admin/report-stocks');
            
            // Then test export functionality
            $browser->visit('/admin/report-stocks')
                    ->assertSee('Download Excel Template')
                    ->click('Download Excel Template')
                    ->waitFor('.fi-no-notification', 5);
        });
    }

    public function test_purchase_request_lines_export()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            // Create a purchase request first
            $purchaseRequest = PurchaseRequest::factory()->create([
                'outlet_id' => $manager->outlet_id ?? 1,
                'request_date' => now(),
                'notes' => 'Test purchase request for export'
            ]);
            
            $browser->loginAs($manager)
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Test purchase request for export')
                    ->click('@view-' . $purchaseRequest->id)
                    ->waitForText('Purchase Request Lines')
                    ->assertSee('Export Lines');
        });
    }

    public function test_export_permissions_for_admin()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Export to Excel') // Admin should see export button
                    ->visit('/admin/report-stocks')
                    ->assertSee('Download Excel Template'); // Admin should see template download
        });
    }

    public function test_export_permissions_for_manager()
    {
        $this->browse(function (Browser $browser) {
            $manager = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($manager)
                    ->visit('/admin/products')
                    ->assertSee('Export to Excel') // Manager should see export button
                    ->visit('/admin/purchase-requests')
                    ->assertSee('Export to Excel'); // Manager should see export button
        });
    }

    public function test_export_notifications()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->click('Export to Excel')
                    ->waitForText('Export Started') // Should see success notification
                    ->assertSee('Your products export is being generated...');
        });
    }

    public function test_stock_template_download()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($admin)
                    ->visit('/admin/report-stocks')
                    ->assertSee('Download Excel Template')
                    ->click('Download Excel Template')
                    ->waitFor('.fi-no-notification', 5); // Wait for download to complete
        });
    }

    public function test_export_with_data_validation()
    {
        $this->browse(function (Browser $browser) {
            $admin = User::where('email', '<EMAIL>')->first();
            
            // Ensure we have products to export
            Product::factory()->create([
                'outlet_id' => $admin->outlet_id,
                'name' => 'Test Export Product',
                'barcode' => '9999999999999'
            ]);
            
            $browser->loginAs($admin)
                    ->visit('/admin/products')
                    ->assertSee('Test Export Product')
                    ->click('Export to Excel')
                    ->waitForText('Export Started');
        });
    }
}
