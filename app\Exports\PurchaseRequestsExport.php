<?php

namespace App\Exports;

use App\Models\PurchaseRequest;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Facades\Auth;

class PurchaseRequestsExport implements FromQuery, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithMapping
{
    protected $outletId;
    protected $dateFrom;
    protected $dateTo;

    public function __construct($outletId = null, $dateFrom = null, $dateTo = null)
    {
        $this->outletId = $outletId;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public function query()
    {
        $query = PurchaseRequest::query();

        // Apply outlet filtering based on user role
        if ($this->outletId) {
            $query->whereHas('details', function ($q) {
                $q->where('outlet_id', $this->outletId);
            });
        } elseif (Auth::user()?->hasRole('admin')) {
            $query->whereHas('details', function ($q) {
                $q->where('outlet_id', Auth::user()->outlet_id);
            });
        }

        // Apply date filtering
        if ($this->dateFrom) {
            $query->whereDate('purchase_request_date', '>=', $this->dateFrom);
        }

        if ($this->dateTo) {
            $query->whereDate('purchase_request_date', '<=', $this->dateTo);
        }

        return $query->orderBy('purchase_request_date', 'desc');
    }

    public function map($purchaseRequest): array
    {
        return [
            $purchaseRequest->outlet->name ?? 'Unknown',
            $purchaseRequest->request_date->format('Y-m-d'),
            $purchaseRequest->purchaseRequestLines->count(),
            $purchaseRequest->purchaseRequestLines->sum('purchase_quantity'),
            $purchaseRequest->notes ?? 'N/A',
            $purchaseRequest->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function headings(): array
    {
        return [
            'Outlet',
            'Request Date',
            'Items Count',
            'Total Quantity',
            'Notes',
            'Created At',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '10B981'], // Green color
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25, // outlet
            'B' => 15, // request_date
            'C' => 12, // items_count
            'D' => 15, // total_quantity
            'E' => 50, // notes
            'F' => 20, // created_at
        ];
    }

    public function title(): string
    {
        $title = 'Purchase Requests';
        
        if ($this->outletId) {
            $outlet = \App\Models\Outlet::find($this->outletId);
            $title .= ' - ' . ($outlet->name ?? 'Unknown Outlet');
        } else {
            $title .= ' - All Outlets';
        }
        
        if ($this->dateFrom || $this->dateTo) {
            $title .= ' (' . ($this->dateFrom ?? 'Start') . ' to ' . ($this->dateTo ?? 'End') . ')';
        }
        
        return $title;
    }
}
