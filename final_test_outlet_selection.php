<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Models\Outlet;
use App\Filament\Resources\ReportStocks\Pages\OutletSelectionByDate;
use Illuminate\Support\Facades\Auth;

try {
    echo "🔍 Final Test: Outlet Selection Page\n";
    echo "=====================================\n\n";
    
    // Login as manager
    $manager = User::where('email', '<EMAIL>')->first();
    Auth::login($manager);
    echo "✅ Logged in as manager\n";
    
    // Test data
    $testDate = '2025-09-02';
    echo "📅 Testing date: {$testDate}\n";
    
    // 1. Check if report stock exists
    $reportStock = ReportStock::where('report_date', $testDate)->first();
    if (!$reportStock) {
        echo "❌ No report stock found for {$testDate}\n";
        return;
    }
    echo "✅ Report stock found (ID: {$reportStock->id})\n";
    
    // 2. Check if details exist
    $detailsCount = ReportStockDetail::whereHas('reportStock', function ($q) use ($testDate) {
        $q->where('report_date', $testDate);
    })->count();
    echo "✅ Found {$detailsCount} report stock details\n";
    
    // 3. Check outlets query
    $outlets = Outlet::whereHas('reportStockDetails', function ($query) use ($testDate) {
        $query->whereHas('reportStock', function ($q) use ($testDate) {
            $q->where('report_date', $testDate);
        });
    })->get();
    echo "✅ Found {$outlets->count()} outlets with reports\n";
    
    foreach ($outlets as $outlet) {
        echo "   - {$outlet->name} (ID: {$outlet->id})\n";
    }
    
    // 4. Test page instantiation
    try {
        $page = new OutletSelectionByDate();
        $page->mount($testDate);
        echo "✅ OutletSelectionByDate page mounted successfully\n";
        echo "   - Report date: {$page->reportDate->format('Y-m-d')}\n";
        echo "   - Report stock ID: {$page->reportStock->id}\n";
        
        // 5. Test view method
        $viewName = $page->getView();
        echo "✅ View name: {$viewName}\n";
        
        $viewPath = resource_path('views/' . str_replace('.', '/', $viewName) . '.blade.php');
        if (file_exists($viewPath)) {
            echo "✅ View file exists\n";
        } else {
            echo "❌ View file missing: {$viewPath}\n";
        }
        
        // 6. Test title and heading methods
        echo "✅ Page title: " . $page->getTitle() . "\n";
        echo "✅ Page heading: " . $page->getHeading() . "\n";
        echo "✅ Page subheading: " . $page->getSubheading() . "\n";
        
    } catch (Exception $e) {
        echo "❌ Error with page: " . $e->getMessage() . "\n";
        return;
    }
    
    // 7. Test URL generation
    $resource = 'App\Filament\Resources\ReportStocks\ReportStockResource';
    try {
        $outletUrl = $resource::getUrl('outlets', ['date' => $testDate]);
        echo "✅ Outlet URL: {$outletUrl}\n";
        
        if ($outlets->count() > 0) {
            $detailsUrl = $resource::getUrl('details', [
                'outlet' => $outlets->first()->id,
                'date' => $testDate
            ]);
            echo "✅ Details URL: {$detailsUrl}\n";
        }
    } catch (Exception $e) {
        echo "❌ URL generation error: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "=====================================\n";
    echo "✅ Data exists\n";
    echo "✅ Query works\n";
    echo "✅ Page can be instantiated\n";
    echo "✅ View template exists\n";
    echo "✅ URL generation works\n";
    echo "\n🔗 Try accessing: http://127.0.0.1:8000/admin/report-stocks/dates/{$testDate}/outlets\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
