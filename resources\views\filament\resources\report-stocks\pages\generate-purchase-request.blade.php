<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header Section --}}
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-medium text-gray-900">Generate Purchase Requests</h2>
                    <p class="text-sm text-gray-500">Generate purchase requests for products with stock ≤ min_buffer</p>
                </div>
            </div>
        </div>

        {{-- Simple Form Section --}}
        <div class="bg-white rounded-lg shadow p-6">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Report Date</label>
                    <input type="date" value="{{ $data['report_date'] }}"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Low Stock Threshold</label>
                        <input type="number" value="{{ $data['low_stock_threshold'] }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Purchase Multiplier</label>
                        <input type="number" value="{{ $data['purchase_multiplier'] }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" {{ $data['force_regenerate'] ? 'checked' : '' }}
                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-900">Force Regenerate</label>
                </div>

                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex space-x-3">
                        <button onclick="generatePurchaseRequests()"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            Generate Purchase Requests
                        </button>

                        <a href="{{ route('filament.admin.resources.report-stocks.index') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                    </div>

                    <div class="text-sm text-gray-500">
                        <span class="font-medium">Tip:</span> Use CLI command for automation
                    </div>
                </div>
            </div>
        </div>

        {{-- Information Cards --}}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">How it works</h3>
                        <p class="text-sm text-blue-600">System analyzes stock levels and creates purchase requests for items below threshold</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Buffer Settings</h3>
                        <p class="text-sm text-yellow-600">Uses outlet product buffer settings when available, otherwise uses threshold × multiplier</p>
                    </div>
                </div>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">Safe Operation</h3>
                        <p class="text-sm text-green-600">Prevents duplicates and uses database transactions for data integrity</p>
                    </div>
                </div>
            </div>
        </div>

        {{-- Recent Activity --}}
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Purchase Requests</h3>
                <p class="text-sm text-gray-500">Latest purchase requests generated from stock reports</p>
            </div>
            <div class="p-6">
                @php
                    $recentRequests = \App\Models\PurchaseRequest::with(['outlet'])
                        ->orderBy('created_at', 'desc')
                        ->limit(5)
                        ->get();
                @endphp
                
                @if($recentRequests->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentRequests as $request)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $request->outlet->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $request->request_date->format('M d, Y') }} • {{ $request->getTotalProducts() }} products</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $request->is_processed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $request->is_processed ? 'Processed' : 'Pending' }}
                                    </span>
                                    <a href="{{ route('filament.admin.resources.purchase-requests.view', $request) }}" 
                                       class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No purchase requests yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Generate your first purchase request from stock reports.</p>
                    </div>
                @endif
            </div>
        </div>

        {{-- CLI Command Section --}}
        <div class="bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Alternative: CLI Command</h3>
            <p class="text-sm text-gray-600 mb-4">You can also generate purchase requests using the command line:</p>
            <div class="bg-gray-800 text-green-400 p-4 rounded-md font-mono text-sm">
                php artisan purchase:generate --date={{ $data['report_date'] }} --threshold={{ $data['low_stock_threshold'] }} --multiplier={{ $data['purchase_multiplier'] }} {{ $data['force_regenerate'] ? '--force' : '' }}
            </div>
        </div>
    </div>

    <script>
        function generatePurchaseRequests() {
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Generating...';
            button.disabled = true;

            // Get form data
            const reportDate = document.querySelector('input[type="date"]').value;
            const threshold = document.querySelector('input[type="number"]:nth-of-type(1)').value;
            const multiplier = document.querySelector('input[type="number"]:nth-of-type(2)').value;
            const force = document.querySelector('input[type="checkbox"]').checked;

            // Make request to CLI command (simplified approach)
            fetch('/admin/report-stocks/generate-purchase-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    report_date: reportDate,
                    low_stock_threshold: threshold,
                    purchase_multiplier: multiplier,
                    force_regenerate: force
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Purchase requests generated successfully!');
                    window.location.href = '/admin/purchase-requests';
                } else {
                    alert('Error: ' + (data.message || 'Generation failed'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating purchase requests');
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    </script>
</x-filament-panels::page>
