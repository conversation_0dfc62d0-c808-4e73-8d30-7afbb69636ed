<?php

namespace App\Exports;

use App\Models\Outlet;
use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;

class OutletProductTemplateExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function collection()
    {
        // Get sample data for template
        $outlets = Outlet::take(2)->get();
        $products = Product::take(3)->get();
        
        $sampleData = collect();
        
        // Add sample rows
        $sampleData->push([
            'OUTLET001', // outlet_code
            'PROD001', // barcode
            'Sample Product 1', // product_name
            'tablet', // unit
            10, // pack_quantity
            'FM', // outlet_pareto
            'FM', // rumus_pareto
            5, // min_buffer
            20, // max_buffer
        ]);
        
        $sampleData->push([
            'OUTLET001', // outlet_code
            'PROD002', // barcode
            'Sample Product 2', // product_name
            'bottle', // unit
            1, // pack_quantity
            'SM', // outlet_pareto
            'SM', // rumus_pareto
            3, // min_buffer
            15, // max_buffer
        ]);
        
        $sampleData->push([
            'OUTLET002', // outlet_code
            'PROD003', // barcode
            'Sample Product 3', // product_name
            'box', // unit
            12, // pack_quantity
            'BM', // outlet_pareto
            'BM', // rumus_pareto
            2, // min_buffer
            10, // max_buffer
        ]);
        
        return $sampleData;
    }

    public function headings(): array
    {
        return [
            'outlet_code',
            'barcode',
            'product_name',
            'unit',
            'pack_quantity',
            'outlet_pareto',
            'rumus_pareto',
            'min_buffer',
            'max_buffer',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '059669'], // Green color
                ],
            ],
            // Style sample data rows
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'], // Light gray
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // outlet_code
            'B' => 15, // barcode
            'C' => 30, // product_name
            'D' => 12, // unit
            'E' => 15, // pack_quantity
            'F' => 15, // outlet_pareto
            'G' => 15, // rumus_pareto
            'H' => 12, // min_buffer
            'I' => 12, // max_buffer
        ];
    }

    public function title(): string
    {
        return 'Outlet Products Template';
    }
}
